<script>
    import {setContext} from 'svelte';
    import Login from './components/login/index.svelte';
    import Register from './components/register/index.svelte';
    import Modal from './components/Modal/index.svelte';

    export let isProduction;
    export let resolve;

    setContext('isProduction', isProduction);
    let type = 'login';
    function changeType(e) {
        type = e.detail;
    }

    export let isShow = true;
    export let onClose;
    export let maskClosable;

    async function onSuccess(e) {
		resolve(e.detail);
        isShow = false;
    }
    // TODO 组件换成 LoginComponent
    // context在哪里呢？
</script>

{#if isShow}
    <Modal on:close={onClose} maskClosable={maskClosable}>
        <div class="container" width="447">
            {#if type === 'login'}
                <Login on:change-type={changeType} on:login-success={onSuccess} />
            {:else}
                <Register on:change-type={changeType} on:register-sucess={onSuccess} />
            {/if}
        </div>
    </Modal>
{/if}

<style type="text/stylus">
.container
    width 340px
    padding 20px
</style>
