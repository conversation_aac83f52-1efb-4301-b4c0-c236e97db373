<script>
    import { createEventDispatcher } from 'svelte';

    const dispatch = createEventDispatcher();

    export let width = '600px';
    // export let closeOnPressEscape = true;
    // 点击蒙层是否允许关闭
    export let maskClosable = true;

    let isShow = false;

    function hide() {
    }
</script>

<div class="modal-background" on:click={() => maskClosable && dispatch('close')}/>

<div class="modal" style="width: {width}px">
    <slot/>
    <i class="modal-close-icon" on:click={() => dispatch('close')}></i>
</div>

<style>
    .modal-close-icon {
        position: absolute;
        width: 14px;
        height: 14px;
        top: 0;
        right: -24px;
        z-index: 22;
        cursor: pointer;
        background: url(//ali.static.yximgs.com/udata/pkg/cloudcdn/i/close-ico.9f9752c.svg) no-repeat center;
    }

    .modal-background {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
    }

    .modal {
        position: absolute;
        left: 50%;
        top: 50%;
        max-height: calc(100vh - 4em);
        transform: translate(-50%, -50%);
        border-radius: 0.2em;
        background: white;
    }
</style>
