<script lang="ts">
    import BusinessForm from '$lib/features/stage1/business/index.svelte';
    import Agreements from "$lib/features/components/Agreements.svelte";
    import { getContext } from "svelte";


    const configStore = getContext('loginPageConfig');
    const bAccountConfig = $configStore.bAccountConfig;


    let showFooterSection = true;

    function updateShowFooter(event) {
        showFooterSection = event.detail;
    }
    function toggleShowFooter() {
        showFooterSection = !showFooterSection;
    }
    let agreementType = 'login';
    function updateAgreementType(event) {
        agreementType = event.detail;
    }
    const configAgreements = bAccountConfig.agreements || [];
    const bAgreements = [
        {
            title: '用户协议',
            url: 'https://passport.kuaishou.com/b-account-h5/agreement',
            onClick: () => {
                bAccountConfig?.baseAgreementOnClick && bAccountConfig.baseAgreementOnClick();
            },
        },
        ...configAgreements,
    ];
</script>

<div>
    <div>
        <BusinessForm
                on:toggleShowFooter={toggleShowFooter}
                on:updateShowFooter={updateShowFooter}
                on:updateAgreementType={updateAgreementType}
        />
    </div>
    <Agreements
            agreements={bAgreements}
            show={showFooterSection}
            {agreementType}
    />
</div>
