// rollup.config.js
import typescript from 'rollup-plugin-typescript2';
import svelte from 'rollup-plugin-svelte';
import resolve from 'rollup-plugin-node-resolve';
import commonjs from 'rollup-plugin-commonjs';
import livereload from 'rollup-plugin-livereload';
import { terser } from 'rollup-plugin-terser';
import autoPreprocess from 'svelte-preprocess';
import pkg from './package.json';
import babel from 'rollup-plugin-babel';

const production = !process.env.ROLLUP_WATCH;
console.log('p', production);

const name = pkg.name
    .replace(/^(@\S+\/)?(svelte-)?(\S+)/, '$3')
    .replace(/^\w/, m => m.toUpperCase())
    .replace(/-\w/g, m => m[1].toUpperCase());


// todo build 命令
// todo 看看 issue，是否需要 babel
// todo external
export default {
    input: 'src/index.ts',
    output: [{
        file: pkg.module,
        format: 'es',
        sourcemap: true,
    }, {
        file: pkg.main,
        format: 'cjs',
        name,
        sourcemap: true,
    }],
    // output: {
    //     sourcemap: true,
    //     format: 'iife',
    //     name: 'app',
    //     file: 'public/bundle.js',
    // },
    plugins: [
        svelte({
            // enable run-time checks when not in production
            dev: !production,
            // we'll extract any component CSS out into
            // a separate file — better for performance
            // 这样对组件好吗？
            // css: css => {
            // 	css.write('public/bundle.css');
            // },
            preprocess: autoPreprocess({ /* options */ }),
        }),

        // If you have external dependencies installed from
        // npm, you'll most likely need these plugins. In
        // some cases you'll need additional configuration —
        // consult the documentation for details:
        // https://github.com/rollup/rollup-plugin-commonjs
        resolve({
            browser: true,
        }),
        commonjs(),
        typescript({
            clean: true,
            tsconfig: 'tsconfig.json',
            tsconfigOverride: {
                compilerOptions: {
                    target: 'es2015',
                },
            },
            rollupCommonJSResolveHack: false,
        }),

        // Watch the `public` directory and refresh the
        // browser on changes when not in production
        !production && livereload('public'),

        // production && babel({
        // 	extensions: [...DEFAULT_EXTENSIONS, 'svelte'],
        // 	exclude: 'node_modules/**',
        // 	presets: [['@babel/preset-env', {
        // 		useBuiltIns: 'usage',
        // 		targets: {
        // 			"ie": "8"
        // 		}
        // 	}]],
        // }),

        // If we're building for production (npm run build
        // instead of npm run dev), minify
        production && terser(),
    ],
    external: id => {
        console.log(id);
        return production ? (/^@ks\/sso|weblogger/).test(id) : false;
    },
    watch: {
        clearScreen: false,
    },
};
