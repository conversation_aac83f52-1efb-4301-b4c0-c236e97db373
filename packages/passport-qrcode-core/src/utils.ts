import type { AxiosAdapter, InternalAxiosRequestConfig } from 'axios';

type WhiteList = Array<string>;

export const noopAdapter: AxiosAdapter = (config: InternalAxiosRequestConfig) => {
    console.error(
        '[Sig4] 没有执行正确的请求适配器, 请检查配置是否传入adapter: sig4Adapter(options: Sig4Options, adapter: AxiosAdapter)',
    );
    console.error('[Sig4]', config);
    return Promise.reject(config);
};

export type SignAdapterOption = {
    list: WhiteList;
};

export function formatParams(config: InternalAxiosRequestConfig) {
    const { url, params = {}, data, headers } = config;
    const urlObj = new URL(url!, window.location.origin); // 创建 URL 对象
    const path = urlObj.pathname; // 获取路径部分，不包含域名

    // 将查询参数转换成对象格式
    const queryParams = new URLSearchParams(urlObj.search);
    const query: Record<string, string> = { ...params };
    queryParams.forEach((value, key) => {
        query[key] = value;
    });

    const contentType = headers.getContentType() || headers['Content-Type'] || '';
    return {
        path,
        query,
        data,
        contentType: String(contentType),
    };
}

export const now = (function () {
    if (typeof performance !== 'undefined' && performance.now) {
        return function now() {
            return performance.now();
        };
    }
    return function now() {
        return new Date().getTime();
    };
})();

export function parserReportValue(reportValue: unknown) {
    let message = '';
    try {
        message = JSON.stringify(reportValue);
    } catch (e) {
        if (typeof reportValue === 'string') {
            message = reportValue;
        } else if (typeof reportValue === 'undefined') {
            message = 'report value is undefined';
        } else if (e instanceof Error) {
            message = `stringify error:${e.message}`;
        } else if (reportValue?.toString) {
            message = `unknown error:${reportValue.toString()}`;
        } else {
            message = 'unknown error';
        }
    }
    return message;
}

export type SignSuccess = {
    signResult: string;
    signInput: string;
    report: Record<string, unknown>;
};

export type SignFailed = {
    signFailed: true;
    error: Error;
    report: Record<string, unknown>;
};

export function isSignFailedError(err: unknown): err is SignFailed {
    return (err as SignFailed).signFailed;
}

export function formToJSON(queryString: string) {
    const jsonObject = Object.fromEntries(new URLSearchParams(queryString));
    return jsonObject;
}

export function parsePayload(payload: unknown, contentType: 'application/x-www-form-urlencoded' | 'application/json') {
    if (typeof payload === 'object') {
        return payload;
    }
    if (contentType === 'application/x-www-form-urlencoded' && typeof payload === 'string') {
        return formToJSON(payload);
    }
    if (contentType === 'application/json' && typeof payload === 'string') {
        return JSON.parse(payload);
    }
    throw new Error(`[Gundam Sig4] unknown content type:${contentType}`);
}

