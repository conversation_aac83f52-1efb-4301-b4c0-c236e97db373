import { getConfig } from '@/core/configInit';
import { DEFAULT_COUNTRY_CODE } from '@/config';
import { getLoginToken } from '@/core/loginToken';
import { identityVerificationInterceptor } from '@/core/verificationInterceptor';
import { genEncryptReqParams } from '@/utils';

/**
 * 请求短信验证码
 */
export interface requestSmsCodeData {
    type: number;
    phone: string;
    countryCode?: string;
    ztIdentityVerificationType?: number;
    ztIdentityVerificationCheckToken?: string;
}
export interface requestSmsCodeResponse {
    result: number;
    error_msg?: string;
    url?: string;
    sid?: string;
}
export function requestSmsCode({
    type,
    phone,
    countryCode = DEFAULT_COUNTRY_CODE,
}: requestSmsCodeData): Promise<requestSmsCodeResponse> {
    const config = getConfig();
    return identityVerificationInterceptor(`/pass/${config.appName}/web/sms/code`, {
        type,
        phone,
        countryCode,
    });
}
export const requestMobileCode = requestSmsCode; // 别名

// 增加通过邮箱或者手机号发送验证码的接口
export interface requestCodeByAccountData {
    type: number;
    account: string;
    countryCode?: string;
    ztIdentityVerificationType?: number;
    ztIdentityVerificationCheckToken?: string;
}
export function requestCodeByAccount({
       type,
       account,
       countryCode = DEFAULT_COUNTRY_CODE,
}: requestCodeByAccountData): Promise<requestSmsCodeResponse> {
    const config = getConfig();
    return identityVerificationInterceptor(`/pass/${config.appName}/web/email/codeByAccount`, {
        sid: config.sid,
        type,
        account,
        countryCode,
    });
}

export function requestCodeByAccountV2({
    type,
    account,
    countryCode = DEFAULT_COUNTRY_CODE,
}: requestCodeByAccountData): Promise<requestSmsCodeResponse> {
    const config = getConfig();
    const encryptReqParams = genEncryptReqParams({
        account,
    });
    return identityVerificationInterceptor(`/pass/${config.appName}/web/email/codeByAccount/v2`, {
        sid: config.sid,
        type,
        countryCode,
        ...encryptReqParams,
    });
}


/**
 * 短信验证码登录
 */
export interface smsCodeLoginData {
    phone?: string;
    countryCode?: string;
    account?: string;
    smsCode: string | number;
    createId?: boolean;
}
export function smsCodeLogin({
    phone,
    countryCode = DEFAULT_COUNTRY_CODE,
    smsCode,
    createId = true,
}: smsCodeLoginData) {
    const config = getConfig();
    return getLoginToken(`/pass/${config.appName}/web/login/mobileCode`, {
        sid: config.sid,
        countryCode,
        phone,
        smsCode,
        registerOpen: createId,
    });
}
export const login = smsCodeLogin; // 别名


export function smsCodeLoginV2({
     phone,
     countryCode = DEFAULT_COUNTRY_CODE,
     smsCode,
     account,
     createId = true,
 }: smsCodeLoginData) {
    const config = getConfig();
    let encryptReqParams;
    if (phone) {
        encryptReqParams = genEncryptReqParams({
            phone,
        });
    } else {
        encryptReqParams = genEncryptReqParams({
            account,
        });
    }

    return getLoginToken(`/pass/${config.appName}/web/login/mobileCode/v2`, {
        sid: config.sid,
        countryCode,
        smsCode,
        registerOpen: createId,
        ...encryptReqParams,
    });
}

export function getCountryCodeList() {
    const config = getConfig();
    return identityVerificationInterceptor(`/pass/${config.appName}/web/sms/countryCodeList`, {
        sid: config.sid,
    });
}
