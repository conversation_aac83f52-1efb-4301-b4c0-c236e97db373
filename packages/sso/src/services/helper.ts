
import { request } from '@/core/request';
import { DEFAULT_SID } from '@/config/constants'
import { privateGetConfig } from '@/core/configInit';

export interface SidPageInfo {
    result: number;
    background: string[];
    thirdPartyLogin: boolean;
    callbackUrls: string[];
    logo: string[];
    type: number;
}

/**
 * 检查第三方登录时传入的 callbackUrl/followUrl 是否合法。如不合法，需要 @账号中台后端 配置白名单
 */
export async function isLegalCallbackUrl(callbackUrl: string): Promise<boolean> {
    const config = privateGetConfig();
    const res = await request<SidPageInfo>(`${config.baseUrl}/pass/kuaishou/pc/pageInfo?sid=${config.sid || DEFAULT_SID}`, {
    }, 'GET')

    if (!res.callbackUrls.length) {
        return Promise.reject({
            error_msg: `未配置第三方登录 callbackUrl 白名单，请访问该链接申请添加 callbackUrl 白名单: https://passport-portal.corp.kuaishou.com/#/apply/sidWhitelist?sid=${config.sid}`
        })
    }

    let host;
    try {
        host = new URL(callbackUrl).host;
    } catch {
        return Promise.reject({
            error_msg: `传入的 callbackUrl: ${callbackUrl} 不合法`
        })
    }

    return res.callbackUrls.includes(host);
}
