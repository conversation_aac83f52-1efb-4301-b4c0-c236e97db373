<script lang="ts">
    import { createEventDispatcher, getContext } from 'svelte';
    import { resetPasswordByPhoneWithoutLogin } from '@ks/sso';
    import { EnumLoginType } from "$lib/shared/const";
    import IconLeftArrow from "$lib/utils/IconLeftArrow.svelte";
    import { resetMultiUserInfo } from "$lib/features/stage1/kuaishou/ResetPassword/store";

    export let height:number;

    let multiUserToken;
    let userInfos;
    let phone;
    let smsCode: string;
    let password: string;
    let countryCode: string;
    const dispatch = createEventDispatcher();

    resetMultiUserInfo.subscribe((value) => {
        multiUserToken = value.multiUserToken;
        userInfos = value.userInfos;
        phone = value.phone;
        countryCode = value.countryCode;
        smsCode = value.smsCode;
        password = value.password;
    })

    let currentUser;
    let errorMsg = '';


    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    const onLoginStart = getContext('onLoginStart');

    async function submit(userId: string) {
        currentUser = userId;
        try {
            onLoginStart({
                name: EnumLoginType.ResetPasswordChooseUser,
            });
            const result = await resetPasswordByPhoneWithoutLogin({
                phone,
                smsCode,
                password,
                multiUserToken,
                targetUserId: userId,
            });
            onSuccess({
                loginType: EnumLoginType.ResetPasswordChooseUser,
                result,
            });
        } catch (e) {
            onFail({
                loginType: EnumLoginType.ResetPasswordChooseUser,
                error: e,
            });
        }
    }
    function backToLogin() {
        dispatch('updatePageState');
    }
</script>

<div class="choose-user"
     style="height: {height}px"
>
    <div class="choose-user-title">请选择要重置密码的账号</div>
    <div class="choose-user-body">
        {#each userInfos as user}
            <div
                    class="choose-user-list-item"
                    class:selected={user.userId === currentUser}
                    on:click={() => submit(user.userId)}>

                <img src={user.detailInfo.icon} />
                <div class="flex flex-col gap-[4px]">
                    <div class="choose-user-list-item-name">{user.detailInfo.nickname}</div>
                    <div class="choose-user-list-item-id">快手ID：{user.userId}</div>
                </div>
            </div>
        {/each}
    </div>
    <div class="choose-user-footer">
        <div class="flex gap-[4px] items-center cursor-pointer"
             on:click={backToLogin}
        >
            <IconLeftArrow />
            <div>
                返回重置密码
            </div>
        </div>
    </div>
</div>

<style>
    .choose-user {
        display: flex;
        flex-direction: column;
        padding-top: 48px;
        padding-left: 48px;
        padding-right: 48px;
        padding-bottom: 32px;
    }
    .choose-user .choose-user-title {
        color: #222222;
        font-size: 24px;
        font-weight: 600;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 32px;
        text-align: left;
        vertical-align: top;
        margin-bottom: 32px;
    }
    .choose-user .choose-user-body {
        flex: 1;
        display: flex;
        gap: 16px;
        flex-direction: column;
    }
    .choose-user-list-item {
        display: flex;
        border-radius: 8px;
        padding: 12px;
        padding-top: 11px;
        padding-bottom: 11px;
        border-style: solid;
        border-width: 1px;
        border-color: #EAEAEA;
        cursor: pointer;
        gap: 16px;
        align-items: center;
    }
    .choose-user-list-item img {
        width: 48px;
        height: 48px;
        border-radius: 50%;
    }
    .choose-user-list-item:hover {
        background: #F0F7FF;
        border-color: var(--colorB);
    }
    .choose-user-list-item.selected {
        background: #F0F7FF;
        border-color: var(--colorB);
    }
    .choose-user-list-item-name {
        color: #222222;
        font-size: 14px;
        font-weight: 600;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 22px;
        text-align: left;
        vertical-align: top;
    }
    .choose-user-list-item-id {
        color: #9C9C9C;
        font-size: 12px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: left;
        vertical-align: top;
    }
    .choose-user .choose-user-footer {
        display: flex;
        color: #9C9C9C;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: left;
        vertical-align: top;
        gap: 4px;
        align-items: center;
    }
</style>
