<script lang="ts">
    import { getConfigPageFromServer } from "$lib/services";
    import { writable } from "svelte/store";
    import { setContext, onMount } from "svelte";
    import type { ActionStartDimension } from "$lib/core/radar";
    import BusinessLogin from '$lib/features/index.svelte';
    import Toast from "$lib/shared/toast/Toast.svelte";
    import { EnumDefaultLoginType } from "$lib/shared/const";
    import { sendEvent } from "$lib/core/radar";

    export let sid: string;
    export let baseUrl: string;
    export let env: 'staging | production';

    export let onSuccess: (res: any) => void;
    export let onFail: (error: any) => void;
    export let onLoginStart: (res: ActionStartDimension) => void;
    export let loginPageConfig: object;

    export let agreements: Array<any>;
    export let appTitle: string;

    export let defaultLoginType: EnumDefaultLoginType;
    export let authorizeLogoUrl: string;

    const configState = writable(loginPageConfig);
    setContext('env', env);
    setContext('onSuccess', onSuccess);
    setContext('onLoginStart', onLoginStart);
    setContext('onFail', onFail);
    setContext('loginPageConfig', configState);
    setContext('appTitle', appTitle);
    setContext('defaultLoginType', defaultLoginType);
    setContext('authorizeLogoUrl', authorizeLogoUrl);

    // 获取服务端配置
    let promise = getConfigPageFromServer(baseUrl, {
        sid,
    }).then((res) => {
        console.log('res', res);
        configState.set({
            ...loginPageConfig,
            ...res,
        });
        return res;
    });

    onMount(() => {
        sendEvent({
            name: 'b-h5-login-component init',
        });
    })
</script>

<div>
    {#await promise}
        <div>loading...</div>
    {:then res}
        <BusinessLogin
                {defaultLoginType}
        />
    {/await}
    <Toast />
</div>

