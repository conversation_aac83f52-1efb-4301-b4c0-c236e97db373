import { request } from '@/common/request';
import { did, env } from './common';

/**
 * // 请求日志配置信息的接口
 * 从接口获取logger的配置信息
 */
interface LoggerResource {
    domains: string[];
    cdn: string;
}
type ValCoverPromise<T> = {
    [P in keyof T]: Promise<T[P]>;
}
export const getLoggerResource = (sid: string): ValCoverPromise<LoggerResource> => {
    const domain = (() => { // 根据env获取domian
        if (env.get() === 'staging') {
            return 'http://ksid-staging.corp.kuaishou.com';
        }
        if (env.get() === 'development') {
            return 'https://ksid.test.gifshow.com'
        }
        // prt环境的地址
        // return 'https://zt-passport.prt.kuaishou.com';
        // 线上的地址
        return 'https://id.kuaishou.com';
    })();
    const path = '/pass/kuaishou/getCdns';
    const url = `${domain}${path}`;
    const resultPromise = request(url, {
        sid,
        did,
    }) as Promise<LoggerResource>;
    const domains = resultPromise.then(res => res.domains, () => []);
    const cdn = resultPromise.then(res => res.cdn, () => '');
    return {
        domains,
        cdn,
    };
};
