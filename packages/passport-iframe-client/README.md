# passport iframe client 使用说明


## :warning:注意
项目没有通过 babel 编译，需要业务方根据需求手动配置


## 用法示例
```ts
import { injectPassportLoginIframe, removePassportLoginIframe } from '@ks/passport-iframe-client';

function handleMsg(msg) {
    switch (msg.type) {
        case 'passport-login-iframe-msg-success':
            // 登录成功 可以从msg.data拿到authToken去换业务的登录态
            // your code
            break;
        case 'passport-login-iframe-msg-fail':
            // 登录失败
            // your code
            break;
        case 'passport-login-iframe-msg-register':
            // 用户点击注册
            // your code
            break;
        case 'passport-login-iframe-msg-close':
            // 用户点击叉，会自动执行关闭removePassportLoginIframe
            // your code
            break;
    }
}

// 注册装载iframe
// 生产环境 url https://passport.kuaishou.com/pc/account/login-component-page
injectPassportLoginIframe('https://passport.kuaishou.com/pc/account/login-component-page', handleMsg, config);

// 移除iframe 本方法会在close事件时自动调用
removePassportLoginIframe();
```


### injectPassportLoginIframe

向页面里面注入登录的iframe

#### 参数

```ts
url: string
callback: (data) => void
config: Object
```

- url: 登录页iframe的页面地址
- callback: 回调函数
- config: {
- sid: string; 业务id
- kpn?: string; 业务kpn
- env?: string; development production，不填默认为production
- indexPage?: string; login-password密码登录 或者 login-qrcode扫码登录， 默认展示扫码登录
- qrType?: string; 新版二维码登录参数
- serviceOwnParams?: any; 新版二维码登录透传参数
- handleRegisterByEvent?: 是否业务层处理点击注册的操作，如果传入true，需要自行处理passport-login-iframe-msg-register的行为，否则进入快手账号的注册流程
- showRegister?: 是否展示注册入口
- }


- 回调参数说明
- data结构为 {
- type: 'passport-login-iframe-msg-success' | 'passport-login-iframe-msg-fail' | 'passport-login-iframe-msg-register' | 'passport-login-iframe-msg-close',
- data: object | undefined
- }
- passport-login-iframe-msg-success: 登录成功，data的内容为账号这边返回的passToken等数据
- passport-login-iframe-msg-fail: 登录失败，data的内容为账号这边返回的失败原因数据
- passport-login-iframe-msg-register: 注册，业务侧可以根据自己的业务情况跳转注册页面
- passport-login-iframe-msg-close: 用户取消登录，passport-iframe-client内部会自动执行removePassportLoginIframe卸除iframe

#### 返回值
无


### removePassportLoginIframe

移除登录的iframe

#### 参数
无

#### 返回值
无
