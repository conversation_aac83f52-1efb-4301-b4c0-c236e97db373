import { IS_PROD } from './const';

export function createDevLocalStorage() {
    let devLocalStorage;
    if (IS_PROD) {
        devLocalStorage = {
            get length() {
                return -1;
            },
            setItem: (key: string, value: any) => { },
            getItem: (key: string) => { },
            removeItem: (key: string) => { },
            clear: () => { },
            key: (index: number) => { },
        };
    } else {
        devLocalStorage = localStorage;
    }
    return devLocalStorage;
}
