import { writable } from 'svelte/store';

export const tabKuaiShouStore = writable([true, false, false]);
export const tabBusinessMainStore = writable([true, false, false]);
export const tabBusinessSubStore = writable([true, false, false]);

export const setKuaishouTab = (index: number) => {
    const newBools = new Array(3).fill(false);
    newBools[index] = true;
    tabKuaiShouStore.set(newBools);
}


export const setBusinessTab = (index: number) => {
    const newBools = new Array(3).fill(false);
    newBools[index] = true;
    tabBusinessMainStore.set(newBools);
}

export const setBusinessSubTab = (index: number) => {
    const newBools = new Array(3).fill(false);
    newBools[index] = true;
    tabBusinessSubStore.set(newBools);
}
