import { stringify, getDomainFormUrl } from './utils';
import { sendEvent } from "$lib/core/radar";

const pendingRequest: Record<string, {
    reject: (reason?: any) => void;
    xhr: XMLHttpRequest;
}> = {};

export function cancelRequest(url: string) {
    if (!pendingRequest[url]) {
        return;
    }
    const { reject, xhr } = pendingRequest[url];
    xhr.abort();
    reject('From Cancel Request');
}

function addOfflineListener(listener: any) {
    window.addEventListener('offline', listener);
}
function removeOfflineListener(listener: any) {
    window.removeEventListener('offline', listener);
}

/**
 * request请求的回调方法
 */
function requestHookCreator() {
    const reportData: Record<string, any> = {};
    type ResultType = 'SUCC' | 'EXCEPTION' | 'FAILED';
    function getResult(status: 'resolved'| 'rejected', response: any): ResultType {
        // 100110031: 对应多账号的情况的登录
        if (status === 'resolved' || response?.result === 100110031) {
            return 'SUCC';
        }
        if (response?.isXHRError) {
            return 'EXCEPTION';
        }
        return 'FAILED';
    }
    return {
        before(url: string, data: Record<string, any>) {
            Object.assign(reportData, {
                startTime: new Date().getTime(),
                request: data,
                apiUrl: url,
            });
        },
        after(status: 'resolved'| 'rejected', reportType: 'service' | 'catch', response: any) {
            const result: ResultType = getResult(status, response);
            Object.assign(reportData, {
                endTime: new Date().getTime(),
                result,
                response,
                reportType,
            });
            sendEvent({
                name: 'HTTP API',
                message: reportData.apiUrl,
                extra_info: {
                    ...reportData,
                },
            }, {
                duration: reportData.endTime - reportData.startTime,
            });
        }
    }
}

export function request<Response = unknown>(url: string, data: Record<string, any>, method = 'POST') {
    const requestHook = requestHookCreator();
    return new Promise((resolve, reject) => {
        requestHook.before(url, data);
        const xhr = new XMLHttpRequest();
        xhr.open(method, url);
        xhr.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');
        if (data.encryptHeaders) { // 加密文本的请求头
            Object.entries<string>(data.encryptHeaders).forEach(function ([key, value]) {
                xhr.setRequestHeader(key, value);
            });
        }
        xhr.responseType = 'json';
        xhr.withCredentials = true;
        // 只有异步才能设置timeout
        xhr.onload = () => {
            removeOfflineListener(offlineListener);
            delete pendingRequest[url];
            let res = xhr.response;
            if (typeof res === 'string') {
                // ie 11 xhr.responseType = 'json'; 这个设置是无效的
                // https://github.com/naugtur/xhr/issues/123
                try {
                    res = JSON.parse(res);
                } catch(e) {
                    requestHook.after('rejected', 'catch', e);
                    reject(e);
                }
            }
            const status = xhr.status;
            if (status < 200 && status >= 300 && status !== 304 || res?.result !== 1) {
                const rejectData = {
                    ...res,
                    isXHRError: false,
                    errorReason: 'responseData'
                } as Response;
                requestHook.after('rejected', 'service', rejectData);
                reject(rejectData);
            } else {
                requestHook.after('resolved', 'service', res);
                resolve(res);
            }
        };

        xhr.ontimeout = e => {
            removeOfflineListener(offlineListener);
            const rejectData = {
                ...e,
                isXHRError: true,
                errorReason: 'timeout',
                error_msg: '请求超时',
            };
            requestHook.after('rejected', 'service', rejectData);
            reject(rejectData);
        };
        xhr.onerror = e => {
            removeOfflineListener(offlineListener);
            const rejectData = {
                ...e,
                isXHRError: true,
                errorReason: 'error',
                error_msg: '网络错误',
                error_domain: getDomainFormUrl(url),
            };
            requestHook.after('rejected', 'service', rejectData);
            reject(rejectData);
        }
        function offlineListenerCreator() {
            return () => {
                const rejectData = {
                    isXHRError: true,
                    errorReason: 'error',
                    error_msg: '网络错误',
                    error_domain: getDomainFormUrl(url),
                };
                requestHook.after('rejected', 'service', rejectData);
                reject(rejectData);
            }
        }
        const offlineListener = offlineListenerCreator();
        addOfflineListener(offlineListener);
        pendingRequest[url] = {
            reject,
            xhr,
        };
        const body = {
            ...data,
            encryptHeaders: undefined,
        };
        xhr.send(stringify(body));
    }) as Promise<Response>;
}
