declare global {
    import { KsLog } from 'ks-log';
    interface Window {
        /**
         * 由ksLog.config() 自动挂载
         * @deprecated
         */
        ksLog: KsLog;
    }
}

declare module 'ks-log' {
    /**
     * [0, 1] 之间的数
     */
    type percentNumber = number;
    interface KsLogOption {
        /**
         * debug环境不发送error数据
         */
        isDebug: boolean;

        /**
         * 统计数据接口接口
         */
        actionUrl: string;

        /**
         * 是否自动发送pv数据, 可在当前script标签增加data-autopv="false"修改, 默认为true
         */
        autoPV: boolean;

        /**
         * PV 日志是否发送到百度统计做为备份, 设置成功时将自动加载百度统计脚本(有利于SEO), 默认为false
         */
        sendBdPV: boolean;

        /**
         * 业务名, 由于使用kslog上报的业务线过多，希望基于bizName进行隔离，可以和kpn保持一致
         */
        bizName: string,

        /**
         * 页面名, 可在当前script标签增加data-page-tag="XXXX"修改, 默认为空，主要用于url聚类
         */
        pageTag: string,

        /**
         * session ID, 如果业务有要求可指定，没有可不设置
         */
        sessionId: string,

        /**
         * 性能日志收集方案: 1: 只开启onload性能指标，2: 开启所有performance性能指标，默认为1
         */
        perfType: percentNumber,

        /**
         * PV数据样本率 0 to 1, 可在当前script标签增加data-pv-rate="0.xx"修改
         */
        pvSampleRate: percentNumber,

        /**
         * 统计性能数据样本率 0 to 1, 可在当前script标签增加data-perf-rate="0.xx"修改
         */
        perfSampleRate: percentNumber,

        /**
         * js错误样式率 0 to 1, 可在当前script标签增加data-error-rate="0.xx"修改
         */
        errorSampleRate: percentNumber;

        /**
         * 全局统计数据样本率 0 to 1, 可在当前script标签增加data-all-rate="0.xx"修改
         * @warning 注意: 如果设置了自定义pvSampleRate, perfSampleRate, errorSampleRate, 相应实际样本率会覆盖allSampleRate
         */
        allSampleRate: percentNumber;

    }
    interface LogParam {
        url: string;
        pageTag: string;
    }
    interface LogErrorParam {
        event: string;
        exceptionType: string;
        message: string;
        src: string;
        colno: Error['colno'];
        lineno: Error['lineno'];
        stack: Error['stack'];
    }
    interface LogPerfParam {
        name: string;
        duration: number;
    }
    export interface KsLog {
        /**
         * 初使化函数. 如果不需要改配置, 可以不调用
         * @warning 会挂载到 window.ksLog
         * @param {Partial<KsLogOption>} option
         */
        config: (option: Partial<KsLogOption>) => void;
        /**
         * 发送PV数据
         * @param  {object} param 与pv共同传递的其他属性, param参数可以不传, 常见param如 {
         *   url: 'xxx', // 自定统计的url, 否则会使用当前网页的url
         *   pageTag: 'index', // 页面分类, 该类型为任意字符串
         * }
         */
        sendPV: (params?: LogParam) => void;
        /**
         * 发送一个计数的打点
         * @param  {string} name  打点名称, 可为任意字符
         * @param  {boolean} force  是否忽略采样比例限制，强制打点(预留能力，一般不需要传递)
         */
        sendCountTag: (tagName: string, force?: boolean) => void;
        /**
         * 发送行为数据
         * @param  {object} obj 对象, obj中所有属性会被转为querystring, 格式要求参考api接口
         * @param  {boolean} 忽略样本比例限制, 强制发送
         * @return {boolean} 是否成功发送
         */
        sendAction: (params: LogParam, force?: boolean) => void;
        /**
         * 发送js错误数据
         * @param  {object} error
         * 示例：{
            message: message,
            src: src,
            type: 'JS/VIDEO/LIVE_STREAM',
            lineno: lineno,
            colno: colno,
            stack: err ? err.stack : ''
        }
         */
        sendError: (error: string | LogErrorParam) => void;
        /**
         * 发送一个性能打点
         * @param {<type>}  obj  可以是对象或对象数组
         * 示例: [{ name:'dnsCost1', duration: 123 }, { name:'dnsCost2', duration: 123 }]
         *     或 { name:'dnsCost1', duration: 123 }
         */
        sendPerf: (params: LogPerfParam | LogPerfParam[]) => void;
        /**
         * 注销
         */
        detach: () => void;
    }
    declare const ksLog: KsLog;
    export default ksLog;
}
