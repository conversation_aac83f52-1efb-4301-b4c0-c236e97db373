<script lang="ts">
    import { twMerge } from 'tailwind-merge';
    import ToolbarButton from '$lib/shared/toolbar/ToolbarButton.svelte';
    export let name: string = 'RightArrow';
    export let width = 24;
    export let height = 24;
</script>

<ToolbarButton on:click {name} {...$$restProps} class={twMerge($$props.class)} let:svgSize
>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
        <path fill="#8A9199" fill-rule="evenodd" d="m6.296 5.469 6.53 6.53-6.53 6.53-1.06-1.06 5.469-5.47-5.47-5.47 1.061-1.06Zm6.47 0 6.53 6.53-6.53 6.53-1.061-1.06 5.47-5.47-5.47-5.47 1.06-1.06Z" clip-rule="evenodd"/>
    </svg>
</ToolbarButton>

<style>
</style>
