<template>
  <div class="one-id-query-container">
      <VerifyViaSMS @confirm="onConfirm" />
  </div>
</template>

<script lang="ts">
import { defineComponent, getCurrentInstance } from '@vue/composition-api'
import VerifyViaSMS from '@/modules/one-id-query/components/verify-via-sms/VerifyViaSMS.vue';
import { MUTI_ACCOUNTS_CODE } from '@/common/config/constants';
import { Toast } from '@ks/sharp-ui';
import { loginWithSMS } from '@/modules/one-id-query/common/utils';

export default defineComponent({
  components: { VerifyViaSMS },

  setup() {
    const vm = getCurrentInstance();

    function onLoginSuccess(data: any) {
      console.log('onLoginSuccess', data);
      (vm?.proxy as any).$router.push({
        path: '/one-id-query/v3/rebind/new-phone',
        query: { phone: data.phone }
      });
    }

    function onLoginError(data: any) {
      console.log('onLoginError', data);
      // 多账号错误
      if (data.result === MUTI_ACCOUNTS_CODE) {
        sessionStorage.multiAccountsInfo = JSON.stringify(data); // 缓存多账号结果
        (vm?.proxy as any).$router.push('/one-id-query/v3/rebind/multi-accounts');  // 切换到结果页
        return;
      }

      // 其他错误
      Toast.error(data?.error_msg || '验证失败，请检查网络后再试！');
    }

    function onConfirm(phoneInfo: any) {
      loginWithSMS(phoneInfo)
        .then(() => onLoginSuccess(phoneInfo))
        .catch(err => onLoginError(err));
    }

    return {
      onConfirm
    };
  }
});
</script>

<style scoped lang="less">
@import "../common/styles.less";
</style>
