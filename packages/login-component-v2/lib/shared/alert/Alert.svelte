<script lang="ts">
    import TransitionFrame from '$lib/utils/TransitionFrame.svelte';
    import type { ComponentProps } from 'svelte';
    import { twMerge } from 'tailwind-merge';

    interface $$Props extends ComponentProps<TransitionFrame> {
        defaultClass?: string;
    }

    export let dismissable: boolean = false;
    export let defaultClass: string = 'text-[14px] py-[3px] px-[12px]';

    let divClass: string;
    $: divClass = twMerge(defaultClass, ($$slots.icon || dismissable) && 'flex items-center', $$props.class);
</script>

<TransitionFrame {dismissable} color="primary" role="alert" rounded {...$$restProps} class={divClass} on:close let:close>
    {#if $$slots.icon}
        <slot name="icon" />
    {/if}

    {#if $$slots.icon || dismissable}
        <div><slot /></div>
    {:else}
        <slot />
    {/if}
</TransitionFrame>
