import qs from 'qs';
import { loginPageInfo } from '@/api/common-login';
import webLog from '../utils/weblog';

/**
 * Check whether url passes white list or not
 * Rule:
 * 1. If url ends with any of the string in white list
 * 2. The path has to be one of the paths in `whitePaths`
 * @param callbackUrl url provided
 * @param whiteList the white list
 * @returns bool
 */
function checkUrlWhiteList(url: string, whiteList: string[]): boolean {
    try {
        const whitePathsMap = {
            production: [
                '/rest/infra/sts',
                '/oauth2/web/api/sts',
                '/portal/rest/infra/sts',
                '/pass/kuaishou/login/passToken',
                '/rest/k/sts',
                '/thirdPart/qq/',
                '/thirdPart/wechat/',
                '/pc/account/third-part-login-page/qq',
                '/pc/account/third-part-login-page/wechat',
            ],
            development: [
                '/rest/infra/sts',
                '*',
            ],
            staging: [
                '/rest/infra/sts',
                '/oauth2/web/api/sts',
                '/portal/rest/infra/sts',
                '/pass/kuaishou/login/passToken',
                '/rest/k/sts',
                '/thirdPart/qq/',
                '/thirdPart/wechat/',
                '/pc/account/third-part-login-page/qq',
                '/pc/account/third-part-login-page/wechat',
            ],
        };
        const { origin, pathname } = new URL(url); // `url` should be a legal URL
        const nodeEnv: 'production'|'development'|'staging' = process.env.NODE_ENV || 'production';
        /**
         * Get `path` whitelist according to env, rules:
         * 1. totally equal
         * 2. if '*' exists, pass
         */
        const matchedPath = pathname === '/'
            ? '/'
            : whitePathsMap[nodeEnv].find(whitePath => whitePath === '*' || pathname === whitePath);
        /**
         * Check origin, rule:
         * 1. ends with one of the white origin. This rule is consistant with rule in backend
         */
        const matchedOrigin = whiteList.filter(whiteUrl => origin.endsWith(whiteUrl));
        return matchedOrigin.length !== 0 && !!matchedPath;
    } catch (e) {
        return false;
    }
}

/**
 * @file 参数的校验
 */
export async function validLoginPageQuery() {
    // 校验登录页面的参数
    if (!location.pathname.endsWith('/login')) {
        return;
    }
    const queryObj = qs.parse(location.search.replace(/^\?/, ''));
    const callback = queryObj.callback as string;
    const sid = queryObj.sid as string || 'kuaishou.web.api';
    if (!callback) { // 没有callback参数,无需验证
        return;
    }
    try {
        const pageInfo = await loginPageInfo({ sid });
        const callbackUrls = pageInfo.callbackUrls || [];
        const isLegalCallback = callback
            ? checkUrlWhiteList(callback, callbackUrls)
            : true;
        if (!isLegalCallback) { // 非法的callback
            webLog.plugins.radar.event({
                name: 'valid_login_page_query',
                extra_info: JSON.stringify({
                    sid,
                    callback,
                }),
            });
            delete queryObj.callback;
            const paramsStr = qs.stringify(queryObj);
            const replaceUrl = `${location.origin}${location.pathname}?${paramsStr}`;
            location.replace(replaceUrl);
        }
    } catch {
        const replaceUrl = `${location.origin}${location.pathname}`;
        location.replace(replaceUrl);
    }
}
