#!/bin/sh
set -e
set -x

echo '====='
echo '开始构建 apps（脚本废弃⚠️）'
echo '====='

pnpm config set registry 'http://npm.corp.kuaishou.com'

pnpm install


# 示例 sh ./build.sh account-zt-pc
# ./build.sh [account-zt-pc|account-zt-h5]
# app_name=${1:-none}

# if [[ $app_name == "account-zt-pc" ]] || [[ $app_name == "account-zt-h5" ]]; then
#   echo "正在构建: $app_name"
#   npx turbo build --filter=$app_name
#   ls -al apps/*/dist
# else
#   echo "请输入正确的 app 名称(account-zt-pc|account-zt-h5): $app_name"
# fi


echo '====='
echo '结束构建 apps'
echo '====='
