import { request, pageReq } from '@/core/request';
import { privateGetConfig } from '@/core/configInit';
import { getLogoutV3Url } from '@ks-passport/url-kit';

/**
 * 使用业务账号中心域名登出
 *
 * 在使用业务帐号中心域名的时候，处理登出逻辑。在正常情况下，业务方使用 logoutV2 进行登出即可，不需要关心此函数的存在。
 * logoutV2 内部会判断业务是否使用了业务帐号中心域名，如果使用会自动使用本函数登出。
 */
export function _logoutUsingBizHost(param?: { followUrl?: string; unionLogout?: boolean }): void {
    const config = privateGetConfig();
    pageReq((config.logoutHost || config.baseUrl) + '/rest/infra/n/logout', {
        sid: config.sid,
        followUrl: param?.followUrl || config.followUrl,
        unionLogout: !!param?.unionLogout,
    });
}

/**
 * 登出
 *
 * 这个方法有点问题，在不使用业务账号中心域名的情况下，只会清除快手账号中心域名下的全局登录态，不会清除业务方域名下的登录态
 */
export function logout(param?: { followUrl?: string; unionLogout?: boolean; useBizHostLogout?: boolean }) {
    const config = privateGetConfig();
    if (config.bizIdHost && param?.useBizHostLogout !== false) {
        // 在使用业务账号中心域名的case下的登出
        // unionLogout 是否需要集联退出
        // followUrl同步后跳转的encode url
        // 默认情况下如果使用了业务账号中心域名 默认使用/rest/infra/n/logout登出 除非手动配置useBizHostLogout为false 使用原有的登出接口登出
        _logoutUsingBizHost(param);
        return;
    }
    return request(config.baseUrl + '/pass/kuaishou/login/logout', {
        sid: config.sid,
    });
}

export interface LogoutV1Params {
    followUrl?: string;
}
export function logoutV1(params: LogoutV1Params) {
    const config = privateGetConfig();
    const apiParams: {
        sid: string;
        followUrl: string;
        unionLogout?: boolean
    } = {
        sid: config.sid,
        followUrl: params.followUrl || location.href,
    }

    pageReq(`${config.logoutHost}/rest/infra/logout`, apiParams);
}

export interface LogoutV2Params {
    followUrl: string;
    unionLogout?: boolean;
}

/**
 * 登出
 */
export function logoutV2(params: LogoutV2Params) {
    const config = privateGetConfig();
    const apiParams: {
        sid: string;
        followUrl: string;
        unionLogout?: boolean
    } = {
        sid: config.sid,
        followUrl: params.followUrl,
    }

    if (config.bizIdHost) {
        apiParams.unionLogout = !!params.unionLogout;
    }
    pageReq(`${config.logoutHost}/rest/infra/n/logout`, apiParams);
}

export interface LogoutV3Params {
    followUrl?: string;
    unionLogout?: boolean;
}

/**
 * 注销登录。
 */
export function logoutV3(params: LogoutV3Params) {
    const config = privateGetConfig();
    const unionLogout = config.bizIdHost ? !!params.unionLogout : undefined;
    pageReq(
        getLogoutV3Url({
            sid: config.sid, env: config.env, followUrl: params.followUrl || location.href, unionLogout
        }), {}
    );
}
