import { stringify } from '../utils/utils';
import { createDevLog } from '@/utils/logDev';
import { privateGetConfig } from '../core/configInit';
import { getZtPassportFrontEndOrigin } from '@ks-passport/url-kit';
import { RefreshLoginResult, MessageEventWithRefreshResult } from '../types/services'

const devLog = createDevLog('sso:sts');

export function createHiddenIframe(src: string): HTMLIFrameElement {
    const iframe = document.createElement('iframe');
    iframe.src = src;
    iframe.style.border = '0';
    iframe.style.width = '0';
    iframe.style.height = '0';
    iframe.style.position = 'fixed';
    iframe.style.top = '0';
    iframe.style.left = '0';
    iframe.style.visibility = 'hidden';
    iframe.style.opacity = '0';
    document.body.appendChild(iframe);
    return iframe
}

interface SaveTokenBaseOption {
    stsUrl: string;
    sid: string;
    authToken: string;
}

interface SaveTokenOption extends SaveTokenBaseOption {
    followUrl: string;
    failUrl?: string;
}

/**
 * 跳转到 stsURL, 种 token
 * @param option
 */
export function saveToken(option: SaveTokenOption): void {
    devLog('saveToken', option);
    const {
        stsUrl,
        sid,
        authToken,
        followUrl,
        failUrl,
    } = option;

    const connect = stsUrl.indexOf('?') > -1 ? '&' : '?';
    const url = `${stsUrl}${connect}${stringify({
        sid: sid === 'kuaishou.bid.api' ? 'bid.api' : sid,
        authToken,
        // followURL 可能传空
        ... (
            followUrl
                ? {followUrl}
                : {}
        ),
        // failURL 是可选的
        ... (
            failUrl
                ? {failUrl}
                : {}
        ),
    })}`;
    if(privateGetConfig().env !== 'production'){
        localStorage.setItem('__DEBUG_STS_FULL_URL', url);
    }
    devLog('go url', url);
    location.href = url;
}

/**
 * 在 iframe 里 跳转 stsURL
 * @param option
 */
export async function saveTokenInIframe(option: SaveTokenBaseOption): Promise<{
    result: number;
    message?: string;
}> {
    const {
        stsUrl,
        sid,
        authToken,
    } = option;

    const iframeResultURL = `${getZtPassportFrontEndOrigin(privateGetConfig().env)}/pc/account/passToken/result`;

    devLog('set token iframeResultURL: ', iframeResultURL);

    const connect = stsUrl.indexOf('?') > -1 ? '&' : '?';
    const url = `${stsUrl}${connect}${stringify({
        sid: sid === 'kuaishou.bid.api' ? 'bid.api' : sid,
        authToken,
        followUrl: `${iframeResultURL}?successful=true&for=sts`,
        failUrl: `${iframeResultURL}?successful=false&for=sts`,
    })}`

    devLog('set token url: ', url);

    const iframe = createHiddenIframe(url);

    return new Promise((resolve) => {
        const listener = (event: MessageEventWithRefreshResult) => {
            devLog('set token result: ', event);
            let data;
            try {
                data = JSON.parse(event.data);
            } catch {
                devLog('JSON.parse error')
                return;
            }
            const successful = data.successful === 'true';
            const resolveResult: RefreshLoginResult = { result: successful ? 1 : 0 };
            data.failReason && (resolveResult.message = data.failReason);

            devLog('resolve', resolveResult);
            resolve(resolveResult);
            document.body.removeChild(iframe);
            window.removeEventListener('message', listener);
        };
        window.addEventListener('message', listener);
    });
}
