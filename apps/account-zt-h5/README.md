# 帐户中台相关h5

- [快手内授权登录页](https://passport.kuaishou.com/account-h5/autohrize)
- [通用h5登录页](https://passport.kuaishou.com/account-h5/login?sid=kuaishou.web.api)
- [埋点文档](https://wiki.corp.kuaishou.com/pages/viewpage.action?pageId=*********)

有问题欢迎咨询: zhujiahui feng<PERSON>yu

- 通用h5登录页
利用id.kuaishou.com/xxx/sns进行重定向进而触发前端页面登陆逻辑

测试域名: https://ksidtest-h5.corp.kuaishou.com/account-h5/login?sid=xxx&callback=xxx

## 需求迭代

### 补充二选一TASK埋点

冷冰 ********

[埋点-7补充TaskEvent](https://wiki.corp.kuaishou.com/pages/viewpage.action?pageId=*********)

### 接入风控 SDK

[风控SDK说明](https://docs.corp.kuaishou.com/d/home/<USER>

### 监控查看

<https://radar.corp.kuaishou.com/project/03265832dd/dashboard>

### 新版视觉稿

<https://www.figma.com/file/FzjI5CLWFvRvpldO10aQMz/%E8%B4%A6%E5%8F%B7%E7%BB%9F%E4%B8%80%E7%99%BB%E5%BD%95H5%E9%A1%B5%E4%BC%98%E5%8C%96%26%E7%BB%9F%E8%AE%A1?node-id=3%3A784>

UI: @mayuan
FE: @chenxiaolong

### 弹窗内展示页面

目前有个业务“商业化-联盟-内容联盟-壁纸业务”（`sid`为`kuaishou.unioncontent.wallpaper`，产品 @suweijie，客户端开发 @chenshuai05）会在客户端弹窗内显示 h5 统一登录页，当 url 的 query 里存在 smallWebview 参数时，表示在弹窗内打开。

原先弹窗打开时会存在较多的样式覆盖，主要就是让输入框、按钮等界面元素变得稍大一些。但是在 2022-06-10 新版样式改版时，联系到壁纸业务方得知该业务有变动，遂决定不针对弹窗打开的场景做特殊的样式处理，仅仅隐藏多账号选择时的返回按钮以防止与客户端弹窗内的返回按钮冲突。

## 其他

********
bussType 直接传递 sid 参数
风控插件配置 丁冬
前端 冷冰

## 测试部署

## 线上部署

- [流水线](https://halo.corp.kuaishou.com/devcloud/pipeline/history/43904?back=star-pipeline)（构建失败，需要改一下 build.sh 本地 构建 '@ks/identity-verification 包）
- [容器云](https://halo.corp.kuaishou.com/devcloud/cloud/cloud/detail/?node_global_id=SERVICE-1132221&tabActiveName=environment-table)
- [雷达监控：H5 统一登录页](https://radar-plus.corp.kuaishou.com/project/03265832dd/dashboard?env=h5)

PRT 环境域名：zt-passport.prt.kuaishou.com
