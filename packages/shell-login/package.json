{"name": "@mfe/shell-login", "version": "1.0.1", "description": "", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": "https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general", "scripts": {"dev": "ts-node -P tsconfig.json src/index.ts", "build": "rimraf dist && rollup -c", "test": "ts-node -P tsconfig.json src/test.ts", "lint": "echo 'lint skipped.'", "sdk-version": "yarn build && sdk-version upgrade", "postinstall": "patch-package"}, "files": ["dist"], "author": "", "dependencies": {"@types/qrcode-terminal": "^0.12.0", "got": "11.8.2", "qrcode-terminal": "^0.12.0"}, "peerDependencies": {"@ks/sso": "^2.10.2-beta.2"}, "devDependencies": {"@rollup/plugin-commonjs": "^22.0.1", "@rollup/plugin-node-resolve": "^13.3.0", "patch-package": "^6.4.7", "rimraf": "^2.6.2", "rollup": "^2.77.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.32.1", "standard-version": "^7.1.0", "ts-node": "10.8.1", "typescript": "^4.6.2"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}}