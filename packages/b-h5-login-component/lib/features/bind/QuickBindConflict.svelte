<script lang="ts">
    import Button from "$lib/shared/button/index.svelte";
    import { dialog } from "$lib/shared/popup/dialog";
    import { bindConflictInfo, bindPhoneInfo } from "$lib/features/store";
    import { EnumLoginType } from "$lib/shared/const";
    import { notBindAndLogin, phoneRebindAndLogin } from "@ks/general-sso";
    import { createEventDispatcher, getContext } from "svelte";
    import { useToast } from "$lib/shared/toast/toastStore";

    const dispatch = createEventDispatcher();
    const onLoginStart = getContext('onLoginStart');
    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    let buttondisabled = false;
    let buttonText = '一键换绑并登录';


    const toast = useToast();

    let loginErrorMsg = '';
    async function notBind() {
        const confirmed = await dialog.confirm({
            title: '是否确认暂不绑定',
            message: '将无法使用手机号登录商家账号',
            cancelText: '取消',
            confirmText: '暂不绑定'
        });

        if (!confirmed) {
            return;
        }
        try {
            onLoginStart({
                name: EnumLoginType.NotBindB,
            });
            buttondisabled = true;
            buttonText = '暂不绑定手机号，登录中';
            const res = await notBindAndLogin($bindPhoneInfo.bindAuthToken);
            buttondisabled = false;
            onSuccess({
                loginType: EnumLoginType.NotBindB,
                result: res,
            });
        } catch (e) {
            buttondisabled = false;
            buttonText = `一键换绑并登录`;
            loginErrorMsg = e?.error_msg || '未知错误，请稍后重试';
            toast.show({
                message: loginErrorMsg,
            });
            onFail({
                loginType: EnumLoginType.NotBindB,
                error: e,
            });
        }
    }

    function bindOther() {
        dispatch('updateBindStatus', 'BindOther');
    }

    async function changeBind() {
        try {
            onLoginStart({
                name: EnumLoginType.RebindPhoneB,
            });
            buttondisabled = true;
            const res = await phoneRebindAndLogin($bindConflictInfo.authToken);
            buttondisabled = false;
            // buttonText = '换绑成功，即将登录';
            onSuccess({
                loginType: EnumLoginType.RebindPhoneB,
                result: res,
            });
        } catch (e) {
            buttondisabled = false;
            loginErrorMsg = e?.error_msg || '未知错误，请稍后重试';
            toast.show({
                message: loginErrorMsg,
            });
            onFail({
                loginType: EnumLoginType.RebindPhoneB,
                error: e,
            });
        }
    }

</script>

<div class="container">
    <div class="title">
        绑定冲突
    </div>
    <div class="desc">
        当前手机号已绑定其他商家账号，且该账号已绑定另一个快手账号
    </div>

    <div class="guide">
        使用商家账号“{$bindConflictInfo.hintInfo.bidBindInfo.nickName}”登录并更改快手号绑定关系
    </div>

    <div class="user-card">
        <img class="user-icon" src={$bindConflictInfo.hintInfo.bidBindInfo.icons[0]} alt=""/>
        <div class="user-name">{$bindConflictInfo.hintInfo.bidBindInfo.nickName}</div>
        <div class="user-info">已绑定手机号：{$bindConflictInfo.hintInfo.bidBindInfo.blurryPhone}</div>
        <div class="user-info">原绑定快手号：{$bindConflictInfo.hintInfo.oldKsBindInfo.nickName}</div>
        <div class="user-info">新绑定快手号：{$bindConflictInfo.hintInfo.newKsBindInfo.nickName}</div>
    </div>

    <div class="px-[25px] w-full">
        <Button
                disabled={buttondisabled}
                class="mb-[12px] py-[16px]"
                on:click={changeBind}
        >
            {buttonText}
        </Button>
        <Button
                disabled={false}
                class="text-[black] py-[15px] !bg-[#FFFFFF] border-[#E6E6E6ff] border mb-[16px]"
                on:click={bindOther}
        >
            绑定其他手机号
        </Button>
    </div>

    <div class="not-bind"
         on:click={notBind}
    >
        暂不绑定
    </div>
</div>

<style>
    .container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-left: 32px;
        padding-right: 32px;
    }
    .icon {
        width: 60px;
        height: 60px;
        margin-bottom: 24px;
        margin-top: 40px;
        background: red;
    }
    .title {
        color: #000000;
        font-size: 24px;
        font-weight: 500;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 28px;
        text-align: center;
        vertical-align: middle;
        margin-bottom: 24px;
        margin-top: 40px;
    }
    .desc {
        color: #646B73;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 22px;
        vertical-align: top;
        margin-bottom: 16px;
    }
    .guide {
        color: #666666;
        font-size: 15px;
        font-weight: 500;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 21px;
        text-align: left;
        vertical-align: top;
    }
    .user-icon {
        width: 65px;
        height: 65px;
        margin-top: 16px;
        margin-bottom: 16px;
        border-radius: 50%;
    }
    .user-card {
        border-radius: 16px;
        background: #F6F6F6ff;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 16px;
        margin-bottom: 40px;
    }
    .user-name {
        color: #222222;
        font-size: 17px;
        font-weight: 500;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 24px;
        text-align: left;
        vertical-align: top;
        margin-bottom: 8px;
    }
    .user-info {
        color: #666666;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: left;
        vertical-align: top;
        margin-bottom: 6px;
    }
    .user-info:last-child {
        margin-bottom: 16px;
    }
    .not-bind {
        color: #9C9C9C;
        font-size: 13px;
        font-weight: 500;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: center;
        vertical-align: top;
    }
</style>
