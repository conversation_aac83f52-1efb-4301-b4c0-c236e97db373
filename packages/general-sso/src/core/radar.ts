import Weblog from '@ks/weblogger/lib/log.browser';
import Radar<PERSON>ore from '@ks-radar/radar-core';
import RadarEventCollect from '@ks-radar/radar-event-collect';

export interface EventDimension {
    name: string;
    category?: string | string[];
    event_type?: string | string[];
    src?: string;
    result_type?: string;
    message?: string;
    extra_info?: object | string;
    yoda_version?: string;
    webview_type?: string;
    is_official?: 1;
}

// radar 自定义事件上报实例
let eventCollect: null | RadarEventCollect = null;
export const initRadar = (sid?: string) => {
    setTimeout(() => {
        if (eventCollect) {
            return;
        }
        // webLog 实例
        const weblog = new Weblog(
            {
                env: 'production',
            },
            {
                product_name: 'KUAISHOU',
            },
        );

        // radar 实例
        const radarCore = new RadarCore({
            weblogger: weblog, // 传入weblogger实例
            projectId: '0abb8037ac', // 传入项目id
            customDimensions: {
                c_dimension1: sid,
                c_dimension2: 'general-sso@' + process.env.VERSION || '',
            }
        });
        eventCollect = new RadarEventCollect({
            core: radarCore,
        });
    })
}
export const sendEvent = (event: EventDimension, value?: object & {
    duration?: number;
    event_count?: number;
}) => {
    if (!eventCollect) {
        initRadar();
    }
    e();
    function e() {
        if (eventCollect) {
            eventCollect.event(event, value);
        } else {
            setTimeout(e, 500);
        }
    }
}
