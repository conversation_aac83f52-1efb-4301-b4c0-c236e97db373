/**
 * 判断在哪个app里   phone + pc + pad = 1
 * ipad: Mozilla/5.0 (iPad; CPU OS 9_1 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13B143 Safari/601.1
 *
 * @param  {string}  app 可以是 pc phone pad qq weixin.....
 * @return {Boolean}
 */

const rules = {
    crawler: /\b(Twitterbot|facebookexternalhit|Facebot|vkShare|Googlebot|Baiduspider|WhatsApp)\b/,
    // 手机
    phone: function (ua: string) {
        return /\bMobile\b/.test(ua) && !rules.pad.test(ua);
    },
    // 电脑
    pc: function (ua: string) {
        return !/\bMobile\b/.test(ua);
    },
    // 平板  暂时只处理ipad
    pad: /\biPad\b/,
    android: /\bAndroid\b/,
    ios: /\b(iPad|iPhone)\b/,
    qq: /\bQQ\b/,
    kwai: /\bKwai\b/,
    livemate: / livemate\//,
    weixin: /\bMicroMessenger\b/,
    alipay: /\bAlipayClient\b/,
    weibo: /\bWeibo\b/,
    qzone: /\bQzone\b/,
    // 腾讯浏览服务(Tencent Browser Service) 简称TBS
    tbs: /\bTBS\b/,
    // 手机QQ浏览器
    qqBrowser: /\bMQQBrowser\b/,
    // 手机百度
    baidu: /\bbaiduboxapp\b/,
    uc: /\bUCBrowser\b/,
};
export default function inWhichApp(app: keyof typeof rules) {
    const ua = navigator.userAgent;
    const isCrawler = rules.crawler.test(ua);
    // 爬虫与所有互斥
    if (app === 'crawler') {
        return isCrawler;
    }
    // 爬虫与所有互斥 其他类型返回 false
    if (isCrawler) {
        return false;
    }

    const rule = rules[app];
    if (rule) {
        if (rule instanceof Function) {
            return rule(ua);
        }
        return rule.test(ua);
    }
    throw new Error('no such app type');
}

export const isInWeixin = inWhichApp('weixin');
