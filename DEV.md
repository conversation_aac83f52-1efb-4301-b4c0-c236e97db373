## 版本说明

- Major（主要版本）：主要版本号指示软件的重大改变或重大功能更新。当进行不向后兼容的更改、重大功能添加或结构性变化时，增加主要版本号。

- Minor（次要版本）：次要版本号表示向后兼容的新功能添加或较小的改进。当进行新功能添加或增强现有功能，但不破坏现有功能的向后兼容性时，增加次要版本号。

- Patch（补丁版本）：补丁版本号表示针对已发布版本的错误修复、问题修复或其他补丁级别的更新。当进行 bug 修复、安全修复或其他小规模更新时，增加补丁版本号。

- Alpha（预发布版本）：Alpha 版本是软件开发过程中的早期测试版本，通常提供给内部团队或有限的测试人员使用。Alpha 版本可能包含不完整的功能和存在严重问题的代码。它是开发阶段的一个早期阶段。

- Beta（测试版本）：Beta 版本是在软件开发过程中的测试阶段中发布的版本。它比 Alpha 版本更接近最终发布版本，但仍可能包含一些问题和需要修复的 bug。Beta 版本通常提供给更广泛的测试人员和用户群体进行测试和反馈。

- Example: `1.0.0-alpha < 1.0.0-alpha.1 < 1.0.0-alpha.beta < 1.0.0-beta < 1.0.0-beta.2 < 1.0.0-beta.11 < 1.0.0-rc.1 < 1.0.0`.

### 版本管理

```sh
# 1. 版本 commments
npx changeset

# 2. 递增版本号 && 将 commments 写入 CHANGELOG.md
npx changeset version
```

### 预发布版本管理

```sh
# 1. 进入预发布模式
npx changeset pre enter beta

# 2. 预发布版本 commments
npx changeset

# 3. 递增预发布版本 && 写入 CHANGELOG.md
npx changeset version

# 4. 退出预发布模式
npx changeset pre exit

# 5. 递增正式版 && 合并预发布阶段所有 commments 写入 CHANGELOG.md
npx changeset version
```
