<script lang="ts">
    import { twMerge } from 'tailwind-merge';
    import ToolbarButton from '$lib/shared/toolbar/ToolbarButton.svelte';
    export let name: string = '<PERSON><PERSON><PERSON>ogo';
    export let width = 24;
    export let height = 24;
    export let allowHover = true;
    let originWidth = 24;
    let originHeight = 24;
    // function onHover() {
    //     if (!allowHover) {
    //         return;
    //     }
    //     width = 26;
    //     height = 26;
    // }
    // function onOut() {
    //     if (!allowHover) {
    //         return;
    //     }
    //     width = originWidth;
    //     height = originHeight;
    // }
</script>

<ToolbarButton on:click {name} {...$$restProps} class={twMerge($$props.class)} let:svgSize
>
    <svg xmlns="http://www.w3.org/2000/svg"
         width={width}
         height={height}
         fill="none"
         viewBox="0 0 24 24"
    >
        <path class="{$$props.class}" fill="#FF4906" fill-rule="evenodd" d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0Z" clip-rule="evenodd"/>
        <path fill="#fff" fill-rule="evenodd" d="M10.242 11.133a3.672 3.672 0 0 0 2.809-1.302 3.164 3.164 0 1 0 .402-4.177 3.68 3.68 0 1 0-3.21 5.48Zm0-5.86a2.18 2.18 0 1 0 0 4.36 2.18 2.18 0 0 0 0-4.36Zm8.508 9.187a2.716 2.716 0 0 0-2.715-2.718h-3.992a2.716 2.716 0 0 0-2.587 1.892l-2.362-1.188a1.325 1.325 0 0 0-.596-.141c-.728 0-1.318.587-1.318 1.31v3.847c0 .204.048.405.14.588a1.323 1.323 0 0 0 1.77.584l2.369-1.18a2.716 2.716 0 0 0 2.584 1.882h3.992c1.5 0 2.715-1.217 2.715-2.718V14.46Zm-9.422.66v.822l-2.216 1.105-.006.003a.365.365 0 0 1-.48-.157.347.347 0 0 1-.04-.16v-2.394a.358.358 0 0 1 .362-.347c.056 0 .111.013.162.038l2.218 1.09Zm1.406 1.455c0 .74.594 1.343 1.332 1.355h3.924c.74 0 1.342-.595 1.354-1.333v-2.094c0-.74-.594-1.342-1.332-1.354h-3.924c-.74 0-1.341.594-1.353 1.332v2.094Zm3.211-8.606a1.664 1.664 0 1 1 3.328 0 1.664 1.664 0 0 1-3.328 0Z" clip-rule="evenodd"/>
    </svg>
</ToolbarButton>

<style>
    path.currentC {
        fill: var(--colorC);
    }
    path.currentB {
        fill: var(--colorB);
    }
</style>
