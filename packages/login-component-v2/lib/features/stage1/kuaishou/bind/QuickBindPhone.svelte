<script lang="ts">
    import { bindPhoneInfo, bindConflictInfo } from "$lib/features/stage1/kuaishou/store";
    import Button from '$lib/shared/button/index.svelte';
    import IconKuaiLogo from "$lib/utils/IconKuaiLogo.svelte";
    import IconBusinessLogo from "$lib/utils/IconBusinessLogo.svelte";
    import IconRightArrow from "$lib/utils/IconRightArrow.svelte";
    import BindPhoneForm from "$lib/features/stage1/kuaishou/bind/BindPhone.svelte";
    import { createEventDispatcher, getContext } from "svelte";
    import { phoneQuickBindAndLogin, notBindAndLogin } from "@ks/general-sso";
    import { EnumLoginType, EnumPageState, CODE_BIND_CONFLICT } from "$lib/shared/const";
    import Tooltip from "$lib/shared/tooltip/Tooltip.svelte";
    import IconQuestion from "$lib/utils/IconQuestion.svelte";
    import IconLeftArrow from "$lib/utils/IconLeftArrow.svelte";
    import QuickBindConflict from "$lib/features/stage1/kuaishou/bind/QuickBindConflict.svelte";
    import GlobalErrorMsg from "$lib/features/components/GlobalErrorMsg.svelte";

    const dispatch = createEventDispatcher();

    const configStore = getContext('loginPageConfig');
    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    const onLoginStart = getContext('onLoginStart');


    const currentStage = $configStore.currentMainState;

    enum EnumBindStatus {
        QuickBind = 'QuickBind',
        BindConflict = 'BindConflict',
        BindOther = 'BindOther',
    }

    let isQuickBind = true;
    let buttondisabled = false;
    let loginErrorMsg = '';
    let conflictErrorMsg = ''; // 用于接收 QuickBindConflict 的错误信息

    let bindStatus = EnumBindStatus.QuickBind;

    function updateBindStatus(event) {
        bindStatus = event.detail;
    }
    let buttonText = `使用手机号 ${$bindPhoneInfo.phone} 一键绑定`;

    async function quickBindAction() {
        try {
            onLoginStart({
                name: EnumLoginType.QuickBindB,
            });
            buttondisabled = true;
            const res = await phoneQuickBindAndLogin($bindPhoneInfo.bindAuthToken);
            buttondisabled = false;
            onSuccess({
                loginType: EnumLoginType.QuickBindB,
                result: res,
            });
        } catch (e) {
            buttondisabled = false;
            // console.log('phonebind error', e);
            if (e?.result === CODE_BIND_CONFLICT) {
                bindStatus = EnumBindStatus.BindConflict;
                bindConflictInfo.set({
                    authToken: e?.authToken,
                    hintInfo: e?.hintInfo,
                });
                return;
            }
            loginErrorMsg = e?.error_msg || '网络错误，请稍后重试';
            onFail({
                loginType: EnumLoginType.QuickBindB,
                error: e,
            });
        }
    }

    async function notBind() {
        try {
            onLoginStart({
                name: EnumLoginType.NotBindB,
            });
            buttondisabled = true;
            buttonText = '暂不绑定手机号，登录中';
            const res = await notBindAndLogin($bindPhoneInfo.bindAuthToken);
            buttondisabled = false;
            onSuccess({
                loginType: EnumLoginType.NotBindB,
                result: res,
            });
        } catch (e) {
            buttondisabled = false;
            buttonText = `使用手机号 ${$bindPhoneInfo.phone} 一键绑定`;
            onFail({
                loginType: EnumLoginType.NotBindB,
                error: e,
            });
        }
    }
    function backToLogin() {
        dispatch('updatePageState', EnumPageState.form);
    }
</script>
<div class="container" class:has-conflict-error={bindStatus === EnumBindStatus.BindConflict && conflictErrorMsg}>
    {#if bindStatus !== EnumBindStatus.BindConflict}
        <div class="bind-phone-title">
            商家账号绑定手机号
            <IconQuestion width={24} height={24} />
            <Tooltip>首次通过快手APP账号授权登录商家账号，需要绑定手机号</Tooltip>
        </div>
    {/if}

    {#if bindStatus === EnumBindStatus.QuickBind}
        <div class="section py-[54px] px-[54px] flex justify-between">
                <div class="flex flex-col items-center gap-[10px]"
                >
                    <IconKuaiLogo
                            width={40}
                            height={40}
                            allowHover={false}
                    />
                    <div class="text-[14px] font-[400] leading-[22px] text-[#333840]">快手APP账号</div>
                </div>

                <IconRightArrow />

                <div class="flex flex-col items-center gap-[10px]"
                >
                    <IconBusinessLogo
                            width={40}
                            height={40}
                    />
                    <div class="text-[14px] font-[400] leading-[22px] text-[#333840]">快手商家账号</div>
                </div>
        </div>
        <div class="w-full relative">
            {#if loginErrorMsg}
            <GlobalErrorMsg loginErrorMsg={loginErrorMsg}
                            class="absolute left-0 top-[-36px]"
            />
            {/if}

            <Button class="currentB my-[8px]"
                    on:click={quickBindAction}
                    disabled={buttondisabled}
            >
                {buttonText}
            </Button>
        </div>
        <div class="vice-operate"
        >
            <div class="flex items-center gap-[4px] cursor-pointer"
                 on:click={backToLogin}
            >
                <IconLeftArrow />
                {currentStage === 3 ? '返回登录' : '返回授权登录'}
            </div>
            <div class="flex items-center gap-[8px]">
                <span class="cursor-pointer"
                      on:click={() => bindStatus = EnumBindStatus.BindOther}>
                    绑定其他手机号
                </span>
                <span class="divide border-l-[1px] h-[14px]"></span>
                <span class="cursor-pointer"
                      on:click={notBind}
                >
                暂不绑定
            </span>
            </div>
        </div>
    {:else if bindStatus === EnumBindStatus.BindOther}
        <BindPhoneForm
                on:backToLogin={backToLogin}
        />
    {:else}
        <QuickBindConflict
                on:updateBindStatus={updateBindStatus}
                on:backToLogin={backToLogin}
                on:errorChange={(e) => conflictErrorMsg = e.detail}
        />
    {/if}
</div>

<style>
    /* 外层容器，动态调整 padding */
    .container {
        padding: 48px;
        transition: padding-bottom 0.2s ease; /* 平滑过渡 */
    }

    /* 当 QuickBindConflict 有错误信息时，减少底部 padding */
    .container.has-conflict-error {
        padding-bottom: 12px; /* 减少底部 padding 来补偿错误信息的高度，保留一些间距 */
    }

    .bind-phone-title {
        color: #222222;
        font-size: 24px;
        font-weight: 600;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 32px;
        text-align: left;
        vertical-align: top;
        display: flex;
        align-items: center;
        gap: 4px;
        margin-bottom: 24px;
    }
    .vice-operate {
        color: #9C9C9C;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: center;
        vertical-align: top;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
</style>
