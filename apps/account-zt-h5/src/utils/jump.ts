function tryJump(link: string) {
    // 判断是否触发跳转操作
    return new Promise<boolean>((res) => {
        function determine() {
            if (
                document.hidden ||
                (document as any).webkitHidden ||
                (document as any).msHidden ||
                document.visibilityState === 'hidden'
            ) {
                res(true);
                // eslint-disable-next-line no-use-before-define
                removeListener();
            }
        }

        function addListener() {
            document.addEventListener('visibilitychange', determine);
            document.addEventListener('msvisibilitychange', determine);
            document.addEventListener('webkitvisibilitychange', determine);
        }

        function removeListener() {
            document.removeEventListener('visibilitychange', determine);
            document.removeEventListener('msvisibilitychange', determine);
            document.removeEventListener('webkitvisibilitychange', determine);
        }

        addListener();

        // 200 毫秒内页面没有失焦，返回 false
        setTimeout(() => {
            determine();
            res(false);
            removeListener();
        }, 200);

        // 尝试跳转
        location.href = link;
    });
}

/**
 * 尝试拉起 快手/极速版 打开 url
 * @param url
 */
export async function jump2App(url: string): Promise<boolean> {
    // 主 APP 跳转链接
    const kwaiUrl = `kwai://webview?url=${encodeURIComponent(url)}`;

    // 极速版的跳转链接
    const nebulaUrl = `ksnebula://webview?url=${encodeURIComponent(url)}`;

    return (await tryJump(kwaiUrl)) || (await tryJump(nebulaUrl));
}
