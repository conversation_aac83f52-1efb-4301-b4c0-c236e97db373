diff --git a/node_modules/jsencrypt/lib/lib/jsbn/rng.js b/node_modules/jsencrypt/lib/lib/jsbn/rng.js
index f6f6804..7ef6738 100644
--- a/node_modules/jsencrypt/lib/lib/jsbn/rng.js
+++ b/node_modules/jsencrypt/lib/lib/jsbn/rng.js
@@ -8,7 +8,7 @@ if (rng_pool == null) {
     rng_pool = [];
     rng_pptr = 0;
     var t = void 0;
-    if (window.crypto && window.crypto.getRandomValues) {
+    if (window && window.crypto && window.crypto.getRandomValues) {
         // Extract entropy (2048 bits) from RNG if available
         var z = new Uint32Array(256);
         window.crypto.getRandomValues(z);
@@ -39,11 +39,13 @@ if (rng_pool == null) {
             // Sometimes Firefox will deny permission to access event properties for some reason. Ignore.
         }
     };
-    if (window.addEventListener) {
-        window.addEventListener("mousemove", onMouseMoveListener_1, false);
-    }
-    else if (window.attachEvent) {
-        window.attachEvent("onmousemove", onMouseMoveListener_1);
+    if (typeof window !== 'undefined') {
+        if (window.addEventListener) {
+            window.addEventListener("mousemove", onMouseMoveListener_1, false);
+        }
+        else if (window.attachEvent) {
+            window.attachEvent("onmousemove", onMouseMoveListener_1);
+        }
     }
 }
 function rng_get_byte() {
