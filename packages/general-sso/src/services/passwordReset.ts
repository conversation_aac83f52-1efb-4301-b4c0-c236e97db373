import { getConfig } from '@/core/configInit';
import { genEncryptReqParams } from '@/utils';
import { DEFAULT_COUNTRY_CODE } from '@/config';
import { getLoginToken } from '@/core/loginToken';

/**
 * 短信重置密码
 */
export interface resetPasswordByPhoneData {
    phone: string;
    countryCode?: string;
    password: string;
    smsCode: string | number;
    setCookie?: boolean;
}
export function resetPasswordByPhone({
    countryCode = DEFAULT_COUNTRY_CODE,
    phone,
    smsCode,
    password,
    setCookie = true,
}: resetPasswordByPhoneData) {
    const config = getConfig();
    const encryptReqParams = genEncryptReqParams({
        phone,
        password,
    });
    const params = {
        sid: config.sid,
        countryCode,
        smsCode,
        setCookie,
        ...encryptReqParams,
    };

    return getLoginToken(`/pass/${config.appName}/web/password/reset/v2`, {
        ...params,
    });
}

/**
 * 短信重置密码
 */
export interface resetPasswordByAccountData {
    account: string;
    countryCode?: string;
    password: string;
    code: string | number;
    setCookie?: boolean;
}

export function resetPasswordByAccount({
    countryCode = DEFAULT_COUNTRY_CODE,
    account,
    code,
    password,
    setCookie = true,
}: resetPasswordByAccountData) {
    const config = getConfig();
    const params = {
        sid: config.sid,
        countryCode,
        code,
        setCookie,
        account,
        password,
    };

    return getLoginToken(`/pass/${config.appName}/web/password/resetByAccount`, {
        ...params,
    });
}

export function resetPasswordByAccountV2({
    countryCode = DEFAULT_COUNTRY_CODE,
    account,
    code,
    password,
    setCookie = true,
}: resetPasswordByAccountData) {
    const config = getConfig();
    const encryptReqParams = genEncryptReqParams({
        account,
        password,
    });
    const params = {
        sid: config.sid,
        countryCode,
        code,
        setCookie,
        ...encryptReqParams,
    };

    return getLoginToken(`/pass/${config.appName}/web/password/resetByAccount/v2`, {
        ...params,
    });
}

/**
 * 通过密码修改为新密码
 */
export interface changePasswordByPasswordData {
    password: string;
    password2: string;
    setCookie?: boolean;
}
export function changePasswordByPassword({ password, password2, setCookie = true }: changePasswordByPasswordData) {
    const encryptReqParams = genEncryptReqParams({
        password,
        password2,
    });
    const config = getConfig();
    const params = {
        sid: config.sid,
        setCookie,
        ...encryptReqParams,
    };

    return getLoginToken(`/pass/${config.appName}/web/password/modify/v2`, {
        ...params,
    });
}
