/**
 * @file
 */
import pkgJson from 'package.json';

export const pkgName = pkgJson.name;
export const pkgVersion = pkgJson.version;

export type Env = 'production'|'development'|'staging';
export const env = {
    value: 'production',
    set(val: Env) {
        this.value = val;
    },
    get():Env {
      return this.value as Env;
    }
}

const ASYNC_LOAD_EVENT_PREFIX  = '__ASYNC_LOAD_EVENT__';
type Listener = (...args: unknown[]) => void;
// 基线版本运行 事件注册
export const addAsyncLoadEvent = (identity: string, listener: Listener) => {
    const eventName = `${ASYNC_LOAD_EVENT_PREFIX}${identity}`;
    if (typeof window !== 'undefined') {
        (window as unknown as Record<string, Listener>)[eventName] = listener;
    }
};
// 远端新版本运行 事件通知
export const notifyAsyncLoadEvent = (identity: string, ...args: Parameters<Listener>) => {
    const eventName = `${ASYNC_LOAD_EVENT_PREFIX}${identity}`;
    if (typeof window !== 'undefined') {
        (window as unknown as Record<string, Listener>)[eventName]?.(...args);
    }
};

// 事件: 异步模块加载
const ASYNC_MODULE_LOAD_EVENT_NAME = 'ASYNC_MODULE_LOAD_EVENT_NAME';

export const addAsyncModuleLoadEvent = (listener: Listener) => {
    return addAsyncLoadEvent(ASYNC_MODULE_LOAD_EVENT_NAME, listener);
}

export const notifyAsyncModuleLoadEvent = (...args: Parameters<Listener>) => {
    return notifyAsyncLoadEvent(ASYNC_MODULE_LOAD_EVENT_NAME, ...args);
}

/**
 * 对fn函数结果进行缓存
 * @param fn
 */
export const cached = <V>(fn: (...args: any[]) => V) => {
    const installModules: Record<string, V> = Object.create(null);
    return (...args: any[]) => {
        const key = JSON.stringify(args[0]);
        const module = installModules[key];
        // 已获取 | 获取中
        if (module) {
            return module;
        }
        // 未获取
        return installModules[key] = fn(...args);
    }
}

export const stringifyPrimitive = (v: string | boolean | number | undefined) => {
    switch (typeof v) {
        case 'string':
            return v;
        case 'boolean':
            return v ? 'true' : 'false';
        case 'number':
            return isFinite(v) ? v : '';
        default:
            return '';
    }
}
export const stringify = (obj: any, sep = '&', eq = '=') => {
    if (obj === null) {
        obj = undefined;
    }
    if (typeof obj === 'object') {
        return Object.keys(obj).map((k) => {
            const ks = encodeURIComponent(stringifyPrimitive(k)) + eq;
            if (Array.isArray(obj[k])) {
                return obj[k].map((v: any) => {
                    return ks + encodeURIComponent(stringifyPrimitive(v));
                }).join(sep);
            }
            return ks + encodeURIComponent(stringifyPrimitive(obj[k]));
        }).join(sep);
    }
    return encodeURIComponent(stringifyPrimitive(obj)) + eq + encodeURIComponent(stringifyPrimitive(obj));
}

export const getDomainFormUrl = (url: string) : string => {
    const regx = /^(?:(?:[a-z]+)?:\/\/)?([^/]+)/
    const match = url.match(regx);
    return match ? match[1] : ''
}

/**
 * 获取cookie
 * @param {string} objName
 * @returns {string}
 */
export function getCookie(objName: string) {
    // 获取指定名称的cookie的值
    const arrStr = typeof document !== 'undefined' && document.cookie.split('; ') || [];
    let temp;
    for (let i = 0; i < arrStr.length; i++) {
        temp = arrStr[i].split('=');
        if (temp[0] == objName) {
            return unescape(temp[1] || '');
        }
    }
    return '';
}

export const did = getCookie('did');














