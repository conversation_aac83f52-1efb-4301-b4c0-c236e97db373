import { privateGetConfig } from './configInit';
import { request, pageReq } from './request';
import { getDeviceInfo } from './deviceInfo'
import { generateCallback } from '../utils/utils';
import { initCaptcha } from './captcha';
import { startIdentityVerification } from '../utils/identityVerificationAdapter';
import { BizAccountParam, AuthTokenResult } from '../types/services';

// TODO: maybe public
type AuthTokenResponse = {
    sid: string;
    userId: number;
    isNewUser?: boolean;
} & {
    [key: string]: string;
};

// TODO: maybe public
interface NeedKsCaptchaResponse {
    captchaConfig: string;
    captchaJsSdk: string;
    captchaType: number;
    captchaUri: string;
    error_msg: string
    result: number;
}

// TODO: private
/**
 * 登录接口的统一封装，传入不同的 path 和参数进行登录，登录成功/失败的处理都是一致的
 */
export async function getLoginToken(path: string, params: any): Promise<AuthTokenResult> {
    const config = privateGetConfig();
    const currentSid = params.sid ?? config.sid;
    try {
        const deviceInfo = await getDeviceInfo();
        if (deviceInfo) { // 增加风控参数
            params.__NS_asfinfo = deviceInfo;
        }
    } catch (error) {
       console.error(error, 'deviceInfo');
    }
    // 如果有业务账号中心域名的情况取token都在业务账号中心域名操作
    return request<AuthTokenResponse>((config.bizIdHost || config.baseUrl) + path, params).then((res) => {
        const {
            [currentSid + '.at']: authToken,
            sid = currentSid,
            [currentSid + '_st']: serviceToken,
            userId,
            ssecurity,
            stsUrl,
            followUrl,
            isNewUser = false,
            passToken,
            bUserId,
        } = res;
        if (config.bizIdHost
            && config.syncToCenter !== false
            && !path.includes('pass/kuaishou/login/passToken')
        ) {
            // 在使用业务账号中心域名的情况，如果用户不强制取消同步，并且非passToken接口会同步到中心账号域名
            _syncLoginStatus({
                sid: currentSid,
                followUrl: config.followUrl,
            });
        }
        const quickLoginAt = `${config.sid}.at`;
        return {
            [currentSid + '.at']: authToken,
            authToken,
            sid,
            ssecurity,
            callback: generateCallback(config.callback, {authToken, sid}),
            [currentSid + '_st']: serviceToken,
            serviceToken,
            userId,
            stsUrl,
            followUrl,
            isNewUser,
            [quickLoginAt]: res[quickLoginAt],
            passToken,
            bUserId,
        };
    }).catch(res => {
        const { result } = res;
        /**
         * sso自动调起快手验证码
         */
        if (
            (result === ********* && config.useKsCaptcha) // 需要走网关验证码（*********）且使用sso自带的拉起快手验证码的交互（useKsCaptcha: true）
            || result === 101100128 // 需要快手验证码（101100128）
        ) {
            const { captchaJsSdk, captchaConfig }: NeedKsCaptchaResponse = res;
            return initCaptcha(captchaJsSdk, captchaConfig)
                // 滑块验证通过
                .then(ticket => {
                    return getLoginToken(path, { ...params, captchaToken: ticket });
                })
                // 滑块验证失败
                .catch(res => {
                    // result=2表示captchaSession 过期，需要重新获取 captchaSession
                    if (res.result === 2) {
                        // 重新获取captchaSession
                        return getLoginToken(path, params);
                    }
                    // 其他错误result视为非常规错误，直接抛出由业务方处理
                    return Promise.reject(res);
                });
        }
        // 返回在400001-410999之间时，视为需要快手验证码，会使用统一身份认证SDK打开验证页面
        if (result >= 400001 && result <= 410999) {
            return startIdentityVerification({
                url: res.url,
                qrcodeEnv: config.env,
                platform: config.platform,
            })
            // 滑块验证通过
            .then(res => {
                if(res.result === -999) {
                    // 行为验证码和端外短信验证出现取消，直接向下透传
                    return Promise.reject(res);
                }
                return getLoginToken(path, {
                    ...params,
                    ztIdentityVerificationType: res.type,
                    ztIdentityVerificationCheckToken: res.token,
                });
            })
            // 滑块验证失败
            .catch(res => {
                // result=2表示captchaSession 过期，需要重新获取 captchaSession
                if (res.result === 2) {
                    // 重新获取captchaSession
                    return getLoginToken(path, params);
                }
                // 其他错误result视为非常规错误，直接抛出由业务方处理
                return Promise.reject(res);
            });
        }
        /**
         * 1.需要走网关验证码（*********）但不使用sso自带的拉起快手验证码的交互（useKsCaptcha: false），
         *   后续需要使用`getCaptchaToken`来再次手动调起验证码交互（可能是快手验证码、也可能是腾讯）
         * 2.其他错误
         */
        return Promise.reject(res);
    });
}

// TODO: public
/**
 * 使用业务账号中心域名时，同步登录态
 */
export function _syncLoginStatus(param?: BizAccountParam) {
    const config = privateGetConfig();
    // 拿到passToken之后调用sync同步登录状态
    return pageReq(config.bizIdHost + '/pass/kuaishou/login/sync', {
        sid: param?.sid ?? config.sid,
        followUrl: param?.followUrl || config.followUrl,
    });
}
