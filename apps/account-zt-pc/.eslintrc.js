module.exports = {
    root: true,
    env: {
        node: true,
    },
    extends: ['@ks/eslint-config-game/typescript'],
    plugins: ['@typescript-eslint/eslint-plugin'],
    parserOptions: {
        ecmaVersion: 2020,
    },
    rules: {
        'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
        'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    },
};
