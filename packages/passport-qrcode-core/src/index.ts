import QrcodeVerify from '@/QrcodeVerify';
import { AccountQrcodeVerify } from "@/AccountQrcodeVerify";
import { IdentityQrcodeVerify } from "@/IdentityQrcodeVerify";
import { envType, getConfig } from './env';
import { setHostFromConfig } from './service';
import { setHostFromConfig as setAccountHostFromConfig } from './account.service';

export { envType } from './env';

export {
    QrcodeVerifyResult,
} from './QrcodeVerify';

/**
 * 按环境配置给接口设置请求域名
 * @param env
 */
function setupServiceHost(env: envType) {
    console.info('env', env);
    const config = getConfig(env || 'production')
    setHostFromConfig(config);
    setAccountHostFromConfig(config);
    return config;
}

/**
 * 开始二维码验证
 * @param option
 */
export function startQrCodeVerify(option: {
    env: envType;
    sid: string;
    kpn: string;
    qrType: string;
    serviceOwnParams: string;
}): QrcodeVerify{
    setupServiceHost(option.env);
    return new QrcodeVerify(option);
}

/**
 * 开始实名二维码验证
 * @param option
 */
export function startAccountQrcodeVerify(option: {
    env: envType;
    sid: string;
    kpn: string;
    bizName: string;
    authType?: number;
    qrType?: string;
}): AccountQrcodeVerify {
    console.log('option', option);
    setupServiceHost(option.env);
    return new AccountQrcodeVerify(option)
}

/**
 *
 */
export function startIdentityQrcodeVerify(option: {
    qrType?: string;
    env: envType;
    sid: string;
    kpn: string;
    type: string;
    url: string;
}): IdentityQrcodeVerify {
    console.log('option', option);
    setupServiceHost(option.env);
    return new IdentityQrcodeVerify(option)
}
