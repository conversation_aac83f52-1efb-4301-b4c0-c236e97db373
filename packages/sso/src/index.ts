// 增加一下Promise判断
if (!Promise || typeof Promise.prototype.then !== 'function') {
    throw new Error('@ks/sso lib need Promise polyfill');
}

export { startIdentityVerification } from '@ks/identity-verification';

export * from './types/common'
export * from './types/services'

export * from './core/request';
export * from './core/configInit';
export { _syncLoginStatus } from './core/loginToken';
export { getCaptchaToken, initCaptcha } from './core/captcha';

// export * from './services/services';
export * from './services/saveToken'
export * from './services/logout'
export * from './services/passwordLogin'
export * from './services/smsLogin'
export * from './services/thirdPartLogin'
export * from './services/refreshLoginStatus'
export * from './services/QrLogin'
export * from './services/bind'
export * from './services/emailLogin'
export * from './services/passToken'
export * from './services/multiUser'
export * from './services/register'
export * from './services/resetPassword'
export * from './services/helper'
export * from './services/acountInfo'
export { bidQuickLogin } from './core/bidLoginAdapter'
