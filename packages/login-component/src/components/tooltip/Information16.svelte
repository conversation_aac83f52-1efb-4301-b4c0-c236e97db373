<script>
    let className = undefined;
    export { className as class };
    export let id = undefined;
    export let tabindex = undefined;
    export let focusable = false;
    export let title = undefined;
    export let style = undefined;
    $: ariaLabel = $$props["aria-label"];
    $: ariaLabelledBy = $$props["aria-labelledby"];
    $: labelled = ariaLabel || ariaLabelledBy || title;
    $: attributes = {
        "aria-label": ariaLabel,
        "aria-labelledby": ariaLabelledBy,
        "aria-hidden": labelled ? undefined : true,
        role: labelled ? "img" : undefined,
        focusable: tabindex === "0" ? true : focusable,
        tabindex,
    };
</script>

<!-- svelte-ignore a11y-mouse-events-have-key-events -->
<svg
    data-carbon-icon="Information16"
    on:click
    on:mouseover
    on:mouseenter
    on:mouseleave
    on:keyup
    on:keydown
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 16 16"
    fill="currentColor"
    width="16"
    height="16"
    class="{className}"
    preserveAspectRatio="xMidYMid meet"
    style="{style}"
    id="{id}"
    {...attributes}
>
    <path
        d="M8.5 11L8.5 6.5 6.5 6.5 6.5 7.5 7.5 7.5 7.5 11 6 11 6 12 10 12 10 11zM8 3.5c-.4 0-.8.3-.8.8S7.6 5 8 5c.4 0 .8-.3.8-.8S8.4 3.5 8 3.5z"
    ></path><path
    d="M8,15c-3.9,0-7-3.1-7-7s3.1-7,7-7s7,3.1,7,7S11.9,15,8,15z M8,2C4.7,2,2,4.7,2,8s2.7,6,6,6s6-2.7,6-6S11.3,2,8,2z"
></path>
    <slot>
        {#if title}
            <title>{title}</title>
        {/if}
    </slot>
</svg>
