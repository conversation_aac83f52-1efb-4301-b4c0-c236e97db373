import JSEncrypt from 'jsencrypt';
import { EncryptContent, EncryptOptions, genMixContent, TextFormat } from './shared';
import { b64tohex } from './base64';

export function formatText(base64Text: string, format: TextFormat) {
    switch (format) {
        case 'base64':
            return base64Text;
        case 'hex':
            return b64tohex(base64Text);
        default:
            return format;
    }
}

/**
 * 加密算法的配置参数
 */
const EncryptConfig = {
    // 公钥
    publicKey:
        'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjWfCb9IR5pMheXLUHCQko8VddGGDZ2jN0Edj/yQoXl91plE6r/muh1oKeuWtSpDwqDDAO5s3lHYfPFb45eWTky0a4ijOBlGbls5WJBQqoqD3gYTPcZyc1KFmn9wRTbNLMFhWN2kCHzo6YOO9kRcCQdAuXaj2sxFrirdglL8v7I0gp0n2ME+3V4Jwiv86cL24t6DfzxHqW/CO7Q/7P6bE5xVZHkuup7J1vXrjewN0r9nXovmahYlLIop4QuWC6zDVHDSTk/SXifHJBidOgEWHKgQSC5FS3xism5bth8XKWu4WX/z2pND4vA4STNE9LwULQPX2MJFjqUdYG7fBePZnIwIDAQAB',
    // 秘钥的版本
    keyVersion: 'version0',
};

class Encryptor {
    publicKey: string;
    keyVersion: string;
    private _encryptor: JSEncrypt;

    constructor(publicKey: string, keyVersion: string) {
        this.publicKey = publicKey;
        this.keyVersion = keyVersion;
        this._encryptor = new JSEncrypt();
        this._encryptor.setPublicKey(publicKey);
    }

    encrypt(rawText: string, options?: EncryptOptions): EncryptContent {
        // console.log('encryptor-rsa', { rawText, options });
        // 内容混入
        let contentText = rawText;
        if (options && options.isMix) {
            contentText = genMixContent(rawText, options);
        }

        // 加密
        const encryptResult = this._encryptor.encrypt(contentText);

        // 加密成功
        if (encryptResult) {
            const format = (options && options.format) || 'hex';
            return {
                encryptText: formatText(encryptResult, format),
                ignoreEncrypt: false,
            };
        }

        // 加密失败
        return {
            encryptText: rawText,
            ignoreEncrypt: true,
        };
    }
}

export const encryptor: Encryptor = new Encryptor(EncryptConfig.publicKey, EncryptConfig.keyVersion);
