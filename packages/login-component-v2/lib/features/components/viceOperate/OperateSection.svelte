<script lang="ts">
    import Feedback from "$lib/features/components/viceOperate/Feedback.svelte";

    export let items = [];
    export let goToRegister;
    export let goToResetPassword;
</script>


{#each items as item, index}
    {#if item?.rawHTML}
        <div on:click={item?.onClick}>
            {@html item.rawHTML}
        </div>
    {:else}
        {#if item.type === 'register'}
            <span class="hover:cursor-pointer"
                  on:click={() => {
                      item?.onClick?.();
                      goToRegister();
                  }}
            >
                注册
            </span>
        {:else if item.type === 'reset'}
            <span class="hover:cursor-pointer"
                  on:click={() => {
                      item?.onClick?.();
                      goToResetPassword();
                  }}>
                忘记密码？
            </span>
        {:else if item.type === 'question'}
            <Feedback
                    openFeedback={item?.onClick}
            />
        {/if}
    {/if}
    {#if index !== items.length - 1 && items.length > 1}
        <span class="divide border-l-[1px] h-[14px]"></span>
    {/if}
{/each}
