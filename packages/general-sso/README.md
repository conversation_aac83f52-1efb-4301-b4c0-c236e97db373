# 业务方自助迭代 SDK
参见[在线文档](https://docs.corp.kuaishou.com/d/home/<USER>

# SSO JS SDK使用说明

可参见[在线文档](https://docs.corp.kuaishou.com/d/home/<USER>

### 示例

#### 示例

```typescript
import {
  init,
  smsCodeLogin,
} from '@ks/general-sso';
// 初始化（必须）
init({
  sid: 'xxxx',
  appName: 'yyyy',
  baseUrl: 'https://spaceship-id.test.gifshow.com',
});

// 登录接口使用示例
try {
  const res = await smsCodeLogin({
      phone: '18800001111',
      smsCode: '123456',
  });
  // 登录成功
  console.log(res);
} catch (res) {
  // 登录出现问题
  console.error(res);
}

// 其他更多接口参考下面的接口说明
// ...
```

#### 业务方可注入IDC全局变量
| 全局变量                        | 原域名(默认值)        | IDC占位符                               | 说明                     |
| ------------------------------- | --------------------- | --------------------------------------- | ------------------------ |
| GENERAL_SSO_API_PASSPORT_PREFIX | passport.kuaishou.com | idc----api-passport----idc.kuaishou.com | iframe的结果通知页的域名 |
IDC占位符列表:
https://team.corp.kuaishou.com/project-board/1237/outline?page=31906

接入IDC域名的话；请`在html页面中`使用以下代码:
```typescript
window.GENERAL_SSO_API_PASSPORT_PREFIX = 'https://idc----api-passport----idc.kuaishou.com';
```

### 详细接口说明

#### init

初始化sso

##### 参数

```ts
{
  sid: string;
  appName: string;
  baseUrl: string;
  callback?: string;
  language?: string;
  env?: 'production' | 'development';
}
```

- sid: 服务id，例如spaceship.api
- appName: 是模版化服务url路径名，一般取sid的前缀，例如spaceship
- baseUrl: 接口的前缀域名+路径
- callback: 换取登录token的url地址。纯前端项目则需要通过设置这个url，如有后端换token的接口则忽略该参数
- language: 语言代码 如 'en', 'zh'
- env: refreshLoginStatus 使用的环境

##### 返回值

无

#### setLanguage(language: string)

更新配置里的 `language` 参数

#### requestSmsCode

发送短信验证码

别名：requestMobileCode

##### 参数

```ts
{
  type: number;
  phone: string;
  countryCode?: string;
}
```

- type: 短信验证码类型，具体值咨询后端
- phone: 手机号
- countryCode: 国际码，默认``+86``

##### 返回值

[基本接口返回](#/基本接口返回)


#### smsCodeLogin

手机号+短信验证码登录

别名：login

##### 参数

```ts
{
  phone: string;
  countryCode?: string;
  smsCode: string | number;
  createId?: boolean;
}
```

- phone: 手机号
- countryCode: 国际码，默认``+86``
- smsCode: 短信验证码
- createId: 是否开启注册，默认为``true``。

##### 返回值

[授权认证返回](#/授权认证返回)


#### passwordLogin

密码登录

##### 参数

```ts
{
  phone: string;
  countryCode?: string;
  password: string;
}
```

- phone: 手机号
- countryCode: 国际码，默认``+86``
- password: 密码

##### 返回值

[授权认证返回](#/授权认证返回)


#### chooseUser

多帐户时选择用户登录

##### 参数

```ts
{
  userId: number;
  multiUserToken: string;
  phone: string;
  countryCode?: string;
}
```

- phone: 手机号
- countryCode: 国际码，默认``+86``
- userId: 希望登录的用户的id
- multiUserToken: 多账号情况下后端接口中返回的token

##### 返回值

[授权认证返回](#/授权认证返回)





#### DEPRECATED <strike>passToken</strike>
<strike>
同步/共享token
</strike>

`>=2.0`已废弃

请使用[`refreshLoginStatus`](#refreshLoginStatus)

##### 参数

```ts
sid?: string
```

- sid: 当前业务的sid

##### 返回值

[授权认证返回](#/授权认证返回)
</strike>


<a id="refreshLoginStatus"></a>
#### refreshLoginStatus
在 隐藏 iframe 内 刷新登录态

版本 `>=0.2.0`

[安全需求](https://docs.corp.kuaishou.com/d/home/<USER>

##### 参数

- {String} param.sid 业务sid
- {String} param.passTokenUrl 当前业务域名可以携带passToken Cookie 的地址
- {String} param.stsUrl 更新种植Cookie 地址
- {Boolean} param.setRootDomain 登录 Cookie 是否往根域名下种植，默认为 false 即种植在 stsUrl 对应的子域名上
- {Number} param.timeout 设置超时时间
- {String} param.followUrl 刷新登录态成功后跳转的地址
- {Boolean} param.openFollowUrlInNewWindow 是否使用新窗口打开 followUrl，默认为 false 即在当前页面打开 followUrl。仅在`followUrl`存在时有效。

##### 返回值
promise
* 调用成功
  ```js
   { result: 1 }
  ```
* 失败
  ```js
    {
      result: 0,
      message: 'reason' //原因
    }
  ```

#### resetPasswordByPhone

用手机重置密码

##### 参数

```ts
{
  phone: string;
  countryCode?: string;
  password: string;
  smsCode: string | number;
  setCookie?: boolean;
}
```

- phone: 手机号
- countryCode: 国际码，默认``+86``
- password: 新密码
- smsCode: 短信验证码
- setCookie: 是否设置cookie（是的话就会设置登录态，原来有登录态的话旧登录态就会失效），默认为``true`` **(v0.1.2+)**

##### 返回值

[授权认证返回](#/授权认证返回)


#### logout

退出登录

##### 参数

无

##### 返回值

[基本接口返回](#/基本接口返回)


#### smsCodeRegister

手机号+短信验证码+密码注册账号 **(v0.1.2+)**

##### 参数

```ts
{
  phone: string;
  countryCode?: string;
  smsCode: string | number;
  password: string;
  setCookie?: boolean;
}
```

- phone: 手机号
- countryCode: 国际码，默认``+86``
- smsCode: 短信验证码
- password: 初始密码
- setCookie: 是否设置cookie（是的话就会设置登录态，原来有登录态的话旧登录态就会失效），默认为``true``

##### 返回值

[授权认证返回](#/授权认证返回)


#### emailRegister

邮箱+邮箱验证码+密码注册账号 **(v0.1.2+)**

##### 参数

```ts
{
  email: string;
  emailCode: string | number;
  password: string;
  setCookie?: boolean;
}
```

- email: 邮箱
- password: 密码
- emailCode: 邮箱验证码
- setCookie: 是否设置cookie（是的话就会设置登录态，原来有登录态的话旧登录态就会失效），默认为``true``

##### 返回值

[授权认证返回](#/授权认证返回)


#### emailPasswordLogin

邮箱+密码登录 **(v0.1.2+)**

##### 参数

```ts
{
  email: string;
  password: string;
}
```

- email: 邮箱
- password: 密码

##### 返回值

[授权认证返回](#/授权认证返回)


#### requestEmailCode

发送邮箱验证码 **(v0.1.2+)**

##### 参数

```ts
{
  email: string;
  type: number;
}
```

- email: 邮箱
- type: 邮箱验证码类型，具体请咨询后端

##### 返回值

[基本接口返回](#/基本接口返回)


#### resetPasswordByEmail

通过邮箱重置密码 **(v0.1.2+)**

##### 参数

```ts
{
  email: string;
  password: string;
  emailCode: string | number;
  setCookie: boolean;
}
```

- email: 邮箱
- password: 密码
- emailCode: 邮箱验证码
- setCookie: 是否设置cookie（是的话就会设置登录态，原来有登录态的话旧登录态就会失效），默认为``true``

##### 返回值

[授权认证返回](#/授权认证返回)


#### checkPassStrength

校验密码强度

##### 参数

```ts
{
  password: string;
}
```

- password: 待校验的密码

##### 返回值

[基本接口返回](#/基本接口返回)


#### checkPhoneAvailability

检查手机号是否可用

##### 参数

```ts
{
  phone: string;
  countryCode?: string;
}
```

- phone: 手机号
- countryCode: 国际码，默认``+86``

##### 返回值

[基本接口返回](#/基本接口返回)


#### checkEmailAvailability

检查邮箱是否可用

##### 参数

```ts
{
  email: string;
}
```

- email: 邮箱

##### 返回值

[基本接口返回](#/基本接口返回)



#### 公共

##### 授权认证返回

```ts
{
  result: number;
  error_msg?: string;
  authToken?: string;
  sid?: string;
  callback?: string;
  serviceToken?: string;
  ssecurity?: string;
  userId?: number;
  stsUrl?: string;
  followUrl?: string;
  isNewUser?: boolean;
  multiUserToken?: string;
  userInfos?: any[];
} & {
  [key: string]: string;
}
```

- authToken: 授权token
- sid: 各个业务线的业务标识id
- callback: 换取登录token的url地址。纯前端项目则需要通过设置这个url，如有后端换token的接口则忽略该参数
- serviceToken: 服务token
- ssecurity: 安全相关参数
- userId: 用户id
- stsUrl: 设置token相关cookie的url
- followUrl: 设置完cookie之后默认的回跳url
- isNewUser: 是否是新注册的用户

如果multiUserToken有值，说明是多账号的情况，需要指定一个目标账号用``chooseUser``方法进行登录：

- multiUserToken: 多用户时会返回一个多用户token
- userInfos: 多用户时会返回多用户列表

##### 基本接口返回

```ts
{
  result: number;
  error_msg?: string;
}
```

- result: 接口返回码
- error_msg: 接口返回错误信息



#### saveToken
保存登录态并跳转 follow URL

##### 参数
一般可以从 登录返回的结果 中取
* stsUrl 种 token 地址
* sid    应用标识
* authToken 登录token
* followUrl 成功跳转地址
* failUrl 失败跳转地址 可选

#### saveTokenInIframe

不跳页面 (在 iframe 中) 保存登录态
返回异步结果
跳转等操作 请业务方在收到Promise 后操作

```js
saveTokenInIframe()
    .then(({result}) => {
        if (result === 1) {
            // do something;
        } else {
            // fail
        }
    })

```

##### 参数
* stsUrl 种 token 地址
* sid    应用标识
* authToken 登录token

##### 返回
Promise<{result}>

result 1 成功

