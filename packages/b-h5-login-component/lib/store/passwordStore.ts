import { EMAIL_SOURCE, KEYCODE, EnumInputLoginType } from '$lib/shared/const'
import { writable } from 'svelte/store'
import { filterInput, isValidAccount, isValidPhone } from "$lib/utils/validate";



export function createPasswordStore(passwordLoginTypes: string[]) {
    let loginType: EnumInputLoginType
    if (passwordLoginTypes.length === 2) {
        loginType = EnumInputLoginType.phoneAndEmail
    } else if (passwordLoginTypes.includes(EnumInputLoginType.phone)) {
        loginType = EnumInputLoginType.phone
    } else {
        loginType = EnumInputLoginType.email
    }
    let state = {

        account: '',
        accountErrorMsg: '',

        password: '',
        passwordErrorMsg: '',

        emailInput: undefined as any,
        emailDropdownList: EMAIL_SOURCE,
        emailDropdownVisible: false,
        emailDropdown: null as any,
        emailHighlightIndex: 0,
        loginType,
        placeholder: loginType === EnumInputLoginType.phoneAndEmail ? '请输入手机号/邮箱' : loginType === EnumInputLoginType.email ? '请输入邮箱' : '请输入手机号',

        bottomAlertOpen: false,
        chooseUserOpen: false,
        agreementChecked: false,

        loginErrorMsg: '',
    }

    const { set, update, subscribe } = writable({
        ...state,
    });

    subscribe(value => {
        state = value
    })

    const setValues = (value: Partial<Parameters<typeof set>[0]>) => {
        set({
            ...state,
            ...value,
        })
    }

    const validateAccount = () => {
        let passed = true;
        if (!state.account) {
            setValues({
                loginErrorMsg: state.placeholder,
            });
            passed = false;
        } else if (!isValidAccount(state.account, loginType)) {
            let msg = '手机/邮箱格式不正确';
            if (loginType === EnumInputLoginType.phone) {
                msg = '手机号码格式不正确';
            } else if (loginType === EnumInputLoginType.email) {
                msg = '邮箱格式不正确';
            }
            setValues({
                loginErrorMsg: msg,
            });
            passed = false;
        }
        // if (passed) {
        //     setValues({
        //         loginErrorMsg: '',
        //     });
        // }
        return passed;
    }

    const validatePassword = () => {
        let passed = true;
        if (!state.password) {
            setValues({
                loginErrorMsg: '请输入密码',
            });
            passed = false;
        } else if (state.password?.length < 8 || state.password?.length > 20) {
            setValues({
                loginErrorMsg: '请输入8-20个字符的密码',
            });
            passed = false;
        }
        // if (passed) {
        //     setValues({
        //         loginErrorMsg: '',
        //     });
        // }
        return passed;
    }

    const reducers = {
        clearInputValue() {
            setValues({
                account: '',
                accountErrorMsg: '',
            })
        },
        validateSend() {
            return validateAccount();
        },

        validateLogin() {
            const bool1 = validateAccount();
            const bool2 = validatePassword();
            return bool1 && bool2;
        },
        isButtonForbidden() {
            // return !(isValidAccount(state.account, loginType) && state.password?.length >= 8 && state.password?.length <= 20);
            return !(state.account && state.password);
        },
        onAccountBlur() {
            validateAccount();
        },
        onPasswordBlur() {
            validatePassword();
        },
        onBottomReject() {
            setValues({
                bottomAlertOpen: false,
            });
        },
        onBottomAccept() {
            setValues({
                bottomAlertOpen: false,
            });
        },
    }

    return {
        update,
        subscribe,
        set,
        setValues,
        reducers,
    }
}
