{"name": "account-zt-h5", "version": "0.1.0", "private": true, "scripts": {"dev": "gitCommitLog=$(git log -1 --pretty=format:'%h on %ct') vue-cli-service serve", "build": "gitCommitLog=$(git log -1 --pretty=format:'%h on %ct') vue-cli-service build", "build:dev": "gitCommitLog=$(git log -1 --pretty=format:'%h on %ct') vue-cli-service build --mode=development", "lint-check": "eslint . && prettier --check .", "lint-fix": "eslint --fix . && prettier --write .", "serve:mock": "MOCK=true vue-cli-service serve", "build:local": "USE_LOCAL=true npx vue-cli-service build", "lint": "vue-cli-service lint", "build:scanned-h5:style": "stylus -c < src/modules/qr-scanned-h5/style.styl > ./dist/scanned-h5.css", "build:scanned-h5:js": "vue-cli-service build --target lib --name scanned-h5 'src/modules/qr-scanned-h5/main.ts'", "build:scanned-h5": "yarn run build:scanned-h5:js && yarn run build:scanned-h5:style"}, "dependencies": {"swrv": "^0.9.6", "@ks-passport/url-kit": "^1.0.0", "@ks-radar/radar-core": "^1.2.4", "@ks-radar/radar-event-collect": "^1.2.4", "@ks-radar/radar-util": "^1.2.4", "@ks/identity-verification": "^0.2.10", "@ks/ks-bridge": "^2.0.2", "@ks/sharp-ui": "0.16.1", "@ks/sso": "2.10.0-beta.28", "@ks/weblogger": "^3.9.46", "@ks/yoda-js-sdk": "^0.3.4", "@ks/yoda-kuaishou-plugin": "^0.0.5", "@types/js-cookie": "^2.2.6", "@vue/composition-api": "^1.0.0-rc.1", "axios": "^0.21.1", "core-js": "^3.6.5", "js-cookie": "^2.2.1", "less": "^4.1.0", "less-loader": "^7.2.1", "qs": "^6.9.6", "vue": "^2.6.11", "vue-router": "^3.2.0"}, "devDependencies": {"@ks/babel-plugin-component": "^1.1.2", "@ks/eslint-config-game": "^1.1.9-alpha.3", "@types/qs": "^6.9.5", "@typescript-eslint/eslint-plugin": "^4.13.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-typescript": "^5.0.2", "eslint": "^6.7.2", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^6.2.2", "lint-staged": "^9.5.0", "prettier": "^3.0.0", "stylus": "^0.54.7", "stylus-loader": "^3.0.2", "typescript": "~3.9.3", "vconsole": "^3.4.0", "vue-template-compiler": "^2.6.11", "webpack-runtime-public-path-plugin": "^1.1.2"}}