<script lang="ts">
    import { createEventDispatcher, getContext } from 'svelte';
    import { chooseUser } from '@ks/sso';
    import { multiUserInfo, bindPhoneInfo } from '$lib/features/stage1/kuaishou/store';
    import { CODE_NEED_BIND, CODE_NEED_BIND_V2, EnumLoginType, EnumPageState } from "$lib/shared/const";
    import IconLeftArrow from "$lib/utils/IconLeftArrow.svelte";

    export let height:number;

    let multiUserToken;
    let userInfos;
    let phone;
    let loginType: string;
    let countryCode: string;
    const dispatch = createEventDispatcher();

    multiUserInfo.subscribe((value) => {
        multiUserToken = value.multiUserToken;
        userInfos = value.userInfos;
        phone = value.phone;
        loginType = value.loginType;
        countryCode = value.countryCode;
    })

    let currentUser;
    let errorMsg = '';


    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    const onLoginStart = getContext('onLoginStart');

    async function submit(userId: string) {
        currentUser = userId;
        try {
            onLoginStart({
                name: EnumLoginType.ChooseC,
            });
            const result = await chooseUser({
                userId: currentUser,
                multiUserToken: multiUserToken,
                phone: phone,
                countryCode,
            });
            onSuccess({
                loginType: EnumLoginType.ChooseC,
                result,
            });
        } catch (e) {
            if ([CODE_NEED_BIND, CODE_NEED_BIND_V2].includes(e?.result)) {
                // 需要跳转到绑定手机号页面
                bindPhoneInfo.set({
                    phone: e.bindInfo.phone,
                    bindAuthToken: e.bindInfo.bindAuthToken,
                    quickLoginResponse: e,
                });
                dispatch('updatePageState', EnumPageState.bindPhone);
                return;
            }
            onFail({
                loginType: EnumLoginType.ChooseC,
                error: e,
            });
        }
    }
    function backToLogin() {
        dispatch('updatePageState', EnumPageState.form);
    }
</script>

<div class="choose-user"
     style="height: {height}px"
>
    <div class="choose-user-title">请选择要授权的账号</div>
    <div class="choose-user-body">
        {#each userInfos as user}
            <div
                class="choose-user-list-item"
                class:selected={user.userId === currentUser}
                on:click={() => submit(user.userId)}>

                    <img src={user.headUrl} />
                    <div class="flex flex-col gap-[4px]">
                        <div class="choose-user-list-item-name">{user.name}</div>
                        <div class="choose-user-list-item-id">快手ID：{user.userId}</div>
                    </div>
            </div>
        {/each}
    </div>
    <div class="choose-user-footer">
        <div class="flex gap-[4px] items-center cursor-pointer"
             on:click={backToLogin}
        >
            <IconLeftArrow />
            <div>
                返回授权登录
            </div>
        </div>
    </div>
</div>

<style>
    .choose-user {
        display: flex;
        flex-direction: column;
        padding-top: 48px;
        padding-left: 48px;
        padding-right: 48px;
        padding-bottom: 32px;
    }
    .choose-user .choose-user-title {
        color: #222222;
        font-size: 24px;
        font-weight: 600;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 32px;
        text-align: left;
        vertical-align: top;
        margin-bottom: 32px;
    }
    .choose-user .choose-user-body {
        flex: 1;
        display: flex;
        gap: 16px;
        flex-direction: column;
    }
    .choose-user-list-item {
        display: flex;
        border-radius: 8px;
        padding: 12px;
        padding-top: 11px;
        padding-bottom: 11px;
        border-style: solid;
        border-width: 1px;
        border-color: #EAEAEA;
        cursor: pointer;
        gap: 16px;
        align-items: center;
    }
    .choose-user-list-item img {
        width: 48px;
        height: 48px;
        border-radius: 50%;
    }
    .choose-user-list-item:hover {
        background: #F0F7FF;
        border-color: var(--colorB);
    }
    .choose-user-list-item.selected {
        background: #F0F7FF;
        border-color: var(--colorB);
    }
    .choose-user-list-item-name {
        color: #222222;
        font-size: 14px;
        font-weight: 600;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 22px;
        text-align: left;
        vertical-align: top;
    }
    .choose-user-list-item-id {
        color: #9C9C9C;
        font-size: 12px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: left;
        vertical-align: top;
    }
    .choose-user .choose-user-footer {
        display: flex;
        color: #9C9C9C;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: left;
        vertical-align: top;
        gap: 4px;
        align-items: center;
    }
</style>
