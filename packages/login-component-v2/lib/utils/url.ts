import qs from 'qs';
import { parse } from 'qs';

export function appendParams(url: string, params: object) {
    const searchIndex = url.indexOf('?');
    const preUrl = searchIndex > -1 ? url.slice(0, searchIndex) : url;

    const oldParams = searchIndex === -1
        ? {}
        : qs.parse(url.slice(searchIndex + 1));

    const newParams = {
        ...oldParams,
        ...params,
    };
    return `${preUrl}?${qs.stringify(newParams)}`;
}

export function useURLSearch() {
    const search = location.search ? location.search.substr(1) : '';

    if (!search) {
        return {};
    }
    const result = parse(search);
    return result;
}

export function removeParamWithoutRefresh(...paramNames: string[]) {
    let url = new URL(window.location.href);
    paramNames.forEach(paramName => {
        url.searchParams.delete(paramName);
    });
    window.history.pushState({}, document.title, url.href);
}
