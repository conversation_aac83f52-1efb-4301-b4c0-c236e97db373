<script lang="ts">
    import FormStage1Kuaishou from "$lib/features/stage1/kuaishou/index.svelte";
    import FormStage1Business from '$lib/features/stage1/business/index.svelte';
    import Agreements from "$lib/features/components/Agreements.svelte";
    import { getContext, setContext } from "svelte";


    const configStore = getContext('loginPageConfig');
    const cAccountConfig = $configStore?.cAccountConfig;
    const bAccountConfig = $configStore?.bAccountConfig;

    let currentIsC = true;
    function switchCurrentIsC() {
        currentIsC = !currentIsC;
    }
    setContext('switchCurrentIsC', switchCurrentIsC);

    let showFooterSection = true;

    function updateShowFooter(event) {
        showFooterSection = event.detail;
    }
    function toggleShowFooter() {
        showFooterSection = !showFooterSection;
    }
    let agreementType = 'login';
    function updateAgreementType(event) {
        agreementType = event.detail;
    }
    const configAgreements = bAccountConfig.agreements || [];
    const bAgreements = [
        {
            title: '用户协议',
            url: 'https://passport.kuaishou.com/b-account-h5/agreement',
            onClick: () => {
                bAccountConfig?.baseAgreementOnClick && bAccountConfig.baseAgreementOnClick();
            },
        },
        ...configAgreements,
    ]
</script>

<div>
    {#if currentIsC}
        <FormStage1Kuaishou
                on:toggleShowFooter={toggleShowFooter}
                on:updateShowFooter={updateShowFooter}
        />
        <Agreements
                agreements={cAccountConfig.agreements}
                show={showFooterSection}
                {agreementType}
        />
    {:else }
        <FormStage1Business
                on:toggleShowFooter={toggleShowFooter}
                on:updateShowFooter={updateShowFooter}
                on:updateAgreementType={updateAgreementType}
        />
        <Agreements
                agreements={bAgreements}
                show={showFooterSection}
                {agreementType}
        />
    {/if}
</div>

<style>

</style>
