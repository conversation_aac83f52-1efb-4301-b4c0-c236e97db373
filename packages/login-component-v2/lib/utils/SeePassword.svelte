<script lang="ts">
    import { twMerge } from 'tailwind-merge';
    import ToolbarButton from '$lib/shared/toolbar/ToolbarButton.svelte';

    export let name: string = 'See';
</script>

<ToolbarButton on:click {name} {...$$restProps} class={twMerge('ms-auto', $$props.class)} let:svgSize>
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16">
        <path fill="#8A9199" fill-rule="evenodd" d="M8 6.334a1.667 1.667 0 1 0 0 3.333 1.667 1.667 0 0 0 0-3.333ZM5.333 8.001a2.667 2.667 0 1 1 5.334 0 2.667 2.667 0 0 1-5.334 0Z" clip-rule="evenodd"/>
        <path fill="#8A9199" fill-rule="evenodd" d="M2.398 5.025C3.498 3.616 5.296 2.166 8 2.166s4.502 1.45 5.602 2.859a10.193 10.193 0 0 1 1.45 2.568 6.256 6.256 0 0 1 .085.235l.005.015.001.005v.001l-.476.15.477.15v.002l-.002.005-.005.014a3.51 3.51 0 0 1-.084.236 10.192 10.192 0 0 1-1.45 2.568c-1.101 1.409-2.899 2.859-5.603 2.859-2.704 0-4.502-1.45-5.602-2.86a10.19 10.19 0 0 1-1.45-2.567 6.248 6.248 0 0 1-.085-.236l-.005-.014-.001-.005V8.15s0-.002.476-.15l-.477-.15v-.002l.002-.005.005-.015a3.784 3.784 0 0 1 .084-.235 10.19 10.19 0 0 1 1.45-2.568ZM1.333 7.999l-.477-.15L.81 8l.046.15.477-.15Zm.53 0c.***************.02.052a9.191 9.191 0 0 0 1.303 2.307c.982 1.258 2.518 2.475 4.814 2.475 2.296 0 3.832-1.217 4.814-2.475A9.19 9.19 0 0 0 14.137 8a8.243 8.243 0 0 0-.262-.621 9.19 9.19 0 0 0-1.06-1.737C11.831 4.383 10.295 3.166 8 3.166c-2.296 0-3.832 1.217-4.814 2.475a9.19 9.19 0 0 0-1.323 2.358Zm12.804 0 .477.15.046-.15-.046-.149-.477.15Z" clip-rule="evenodd"/>
    </svg>
</ToolbarButton>
