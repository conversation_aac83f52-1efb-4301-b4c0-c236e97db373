import { EncryptOptions, EncryptContent, genMixContent, ENCRYPT_HEADER_KEYS } from './shared';
import { sm2, sm3 } from 'sm-crypto';

const EncryptConfig = {
    keyVersion: 'sm2_version0',
    publicKey:
        '040e5e9f602234e5241f5d5d68953db9d2d07a12aafbfb0eee6686bd98cdd791596b29b6c5375d821777cd14cb05bc59ee041231f966d627e109f38c268b2461ac',
};
// 1 - C1C3C2，0 - C1C2C3，默认为 1
const cipherMode = 0;

class Encryptor {
    publicKey: string;
    keyVersion: string;

    constructor(publicKey: string, keyVersion: string) {
        this.publicKey = publicKey;
        this.keyVersion = keyVersion;
    }

    encrypt(rawText: string, options?: EncryptOptions): EncryptContent {
        // console.log('encryptor-gm', { rawText, options });

        let contentText = rawText;
        if (options && options.isMix) {
            contentText = genMixContent(rawText, options);
        }

        const encryptResult = sm2.doEncrypt(contentText, this.publicKey, cipherMode);
        if (encryptResult) {
            return {
                encryptText: encryptResult,
                ignoreEncrypt: false,
            };
        }

        return {
            encryptText: rawText,
            ignoreEncrypt: true,
        };
    }

    sign(reqParams: any) {
        const { encryptHeaders, ...bodyData } = reqParams;
        const str = Object.keys(bodyData)
            .sort()
            .reduce((acc, i) => (acc += `${i}=${bodyData[i] === undefined ? '' : bodyData[i]}&`), '');
        const signature = sm3(str);

        return {
            encryptHeaders: {
                ...encryptHeaders,
                [ENCRYPT_HEADER_KEYS.VERIFY_SIGNATURE]: signature,
            },
            ...bodyData,
        };
    }
}

export const encryptor: Encryptor = new Encryptor(EncryptConfig.publicKey, EncryptConfig.keyVersion);
