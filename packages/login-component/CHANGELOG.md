# @ks/login-component

## 3.1.1-beta.3

### Patch Changes

- 协议更新

## 3.1.1-beta.2

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.2-beta.2

## 3.1.1-beta.1

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.2-beta.1

## 3.1.1-beta.0

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.2-beta.0

## 3.1.0

### Patch Changes

- Updated dependencies [8dd33263]
- Updated dependencies [264b2f32]
- Updated dependencies [15842a97]
- Updated dependencies [5372b11a]
- Updated dependencies [0b01d712]
- Updated dependencies [e467370c]
- Updated dependencies [5fe80378]
- Updated dependencies [0977e900]
- Updated dependencies [3884b7c4]
- Updated dependencies [c1cdb7dd]
  - @ks/sso@2.10.1

## 3.1.0-beta.16

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.1-beta.9

## 3.1.0-beta.15

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.1-beta.8

## 3.1.0-beta.14

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.1-beta.7

## 3.1.0-beta.13

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.1-beta.6

## 3.1.0-beta.12

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.1-beta.5

## 3.1.0-beta.11

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.1-beta.4

## 3.1.0-beta.10

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.1-beta.3

## 3.1.0-beta.9

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.1-beta.2

## 3.1.0-beta.8

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.1-beta.1

## 3.1.0-beta.7

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.1-beta.0

## 3.1.0-beta.6

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.0-beta.28

## 3.1.0-beta.5

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.0-beta.27

## 3.1.0-beta.4

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.0-beta.26

## 3.1.0-beta.3

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.0-beta.25

## 3.1.0-beta.2

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.0-beta.24

## 3.1.0-beta.1

### Minor Changes

- sig4 加签

## 3.0.6-beta.0

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.0-beta.23

## 3.0.5

### Patch Changes

- 3e32c8a6: 登录界面 UI 优化
- Updated dependencies [516791a5]
- Updated dependencies [516791a5]
- Updated dependencies [ba424490]
- Updated dependencies [989a069c]
- Updated dependencies [516791a5]
- Updated dependencies [eb692a6a]
- Updated dependencies [516791a5]
- Updated dependencies [516791a5]
- Updated dependencies [516791a5]
- Updated dependencies [36679a54]
- Updated dependencies [516791a5]
  - @ks/sso@2.9.0

## 3.0.5-beta.1

### Patch Changes

- Updated dependencies [e38e9b78]
  - @ks/sso@3.0.3-beta.3

## 3.0.5-beta.0

### Patch Changes

- Updated dependencies
  - @ks/sso@3.0.3-beta.2
- 登录界面 UI 优化

## 3.0.4

### Patch Changes

- Updated dependencies
  - @ks/sso@3.0.2

## 3.0.3

### Patch Changes

- Updated dependencies
  - @ks/sso@3.0.1

## 3.0.2

### Patch Changes

- fix: errorMsg 为 undefined

## 3.0.1(********)

### Patch Changes

- fixbug B1351812: pc 登录页触发滑动验证码点击取消按钮关闭弹窗，页面提示“未知错误”

## 3.0.0(2023-06-29/周四）

- 版本号变更: 2.1.3 → 3.0.0 by @suxinwei
- 更高版本的 CHANGELOG 另请参见[在线文档](https://docs.corp.kuaishou.com/k/home/<USER>/fcAA0jLUpiOj6MnHmhspZVzb8)。

## [2.1.3](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/login-component/2.1.2...@ks/login-component/2.1.3) (2022-08-01)

### Bug Fixes

- **login-component:** 修复初始化标志位的问题 ([f61870d](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/f61870d562dc3d45706b6c9daad9b7f20686167a))

## [2.1.2](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/login-component/2.1.1...@ks/login-component/2.1.2) (2022-08-01)

### Features

- **login-component:** 添加 refreshLoginStatus 方法 ([c38a114](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/c38a11437b67814b443cf8c2fc92bd8f6ea8fd9e))

## [2.1.1](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/login-component/2.1.0...@ks/login-component/2.1.1) (2022-07-28)

### Features

- **login-component:** 密码登录时密码长度改成 6~20 位 ([e283091](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/e283091a9a84c94f13bc71a7b78ea58ffb69d840))

# [2.1.0](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/login-component/2.0.17...@ks/login-component/2.1.0) (2022-07-06)

### Bug Fixes

- 修复第三方登录未校验 callbackUrl 的问题 ([7e65189](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/7e6518901083515a14f84882b26b8b708e724fed))

### Features

- 增加种植登录态 Cookie 的 saveTokenInIframe 方法和登出的 logoutV2 方法 ([e4bca98](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/e4bca980bb38bb57b8d73b445407f0240e4c1cb2))

## [2.0.17](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/login-component/2.0.16-beta.1...@ks/login-component/2.0.17) (2022-06-30)

### Bug Fixes

- 修改 login 方法弹窗样式问题, 修复扫码登录 tip 样式问题 ([5712d3e](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/5712d3e8e01c22d4ec897bba8a95e6ae3d9d7e04))

## 2.0.17-beta.1（因权限问题，暂未发包）

- appealCustomHref 参数增加域名白名单校验

## 2.0.16-beta.1 (2022-03-17)

升级依赖到@ks/sso@2.7.2-beta.1

### 2.0.15-beta.1 (2022-02-22)

升级依赖到@ks/sso@2.7.1-beta.1

### 2.0.14-beta.1 (2022-02-10)

增加自定义反馈功能

### 2.0.13-beta.1 (2021-10-21)

升级依赖到@ks/sso@2.6.1-beta.1

### 2.0.12 (2021-10-11)

添加车载逻辑

### 2.0.11 (2021-09-28)

修改登录组件的最小高度

### 2.0.10 (2021-09-15)

升级依赖到@ks/sso@2.6.0

## 2.0.7 (2021-08-31)

支持并列展示二维码形式

## 2.0.6 (2021-07-23)

完全修复 indexPage 为 login-qrcode 时，密码与验证码状态不对的问题

## 2.0.5 (2021-07-19)

修改 indexPage 为 login-qrcode 时，密码与验证码状态不对的问题
断网后 二维码不显示导致的结构塌陷的问题

## 2.0.4 (2021-07-19)

修改 indexPage 为 login-qrcode 时，密码与验证码状态不对的问题

## 2.0.3 (2021-07-16)

更改主题色
indexPage 指定为 login-password login-verify-sms 时会影响登录 tab 顺序

## 2.0.2 (2021-06-23)

扫码签名支持新的扫码流程 从 scanResult 取签名
老的扫码不变 从 start 里面取签名

## 2.0.1 (2021-06-21)

配置中的 showRegistry 改为 showRegister

## 2.0.0 (2021-06-17)

登录成功之后 增加登录方式字段 loginType

## 1.7.0-beta4 (2020-05-27)

- 支持第三方登录

## 1.7.0-beta3 (2020-05-17)

- fix:更改注册密码框到 20 位

## 1.7.0-beta2 (2020-05-04)

- logger 增加风控 sdk
- 更改注册密码要求到 8-20 位

## 1.7.0-beta1 (2020-04-19)

- 支持新版扫码登录、支持登录失败的回调和注册回调

## 1.6.6 (2020-11-21)

- 兼容新版@ks/sso 在 cancel 时 reject 内容的变化

## 1.6.5 (2020-11-19)

- 更新扫码登录示意图

## 1.6.4 (2020-09-22)

### Bug Fixes

- 修复测试环境电话号码验证规则问题

## 1.6.3 (2020-09-10)

### Bug Fixes

- 倒计时出现后点击倒计时仍能发送验证码的问题修复

## 1.6.1 (2020-09-07)

### Bug Fixes

- 解决 resolve()重复调用失效的问题

## 1.6.0 (2020-09-07)

### Features

- login-component 更新 logo 图标 ([62a60eb](https://git.corp.kuaishou.com/explore-frontend/general-project/commit/62a60ebe0c089a5b622ca6cb445358970f173c67))
- 更新 login-component 依赖 sso 的协议为 https ([e46cde8](https://git.corp.kuaishou.com/explore-frontend/general-project/commit/e46cde8e9c9e1e1b5d92c97dd2cb0d1832ae54b6))

### [1.5.3](https://git.corp.kuaishou.com/explore-frontend/general-project/compare/v2.1.2...v1.5.3) (2020-05-28)

### [1.5.2](https://git.corp.kuaishou.com/explore-frontend/general-project/compare/v1.5.0...v1.5.2) (2020-05-28)

## 1.5.1 (2020-05-14)

- 升级 sso，支持自研验证码

## 1.5.0 (2020-05-11)

### Features

- modal 支持 maskClosable 参数 ([5861d9d](https://git.corp.kuaishou.com/explore-frontend/general-project/commit/5861d9d1da98152e438579593a0194f134bea6bc))

### 1.4.1 (2020-04-03)

- 升级 sso 版本到 2.0.1，修复跨域请求问题

## 1.4.0 (2020-03-24)

- 升级 sso 版本到 2.0，减少本身体积依赖，登陆成功返回参数增加 stsUrl 与 followUrl

### [1.3.1](https://git.corp.kuaishou.com///compare/v1.3.0...v1.3.1) (2020-03-16)

### Bug Fixes

- onSuccess param ([b12ef4f](https://git.corp.kuaishou.com///commit/b12ef4fcb1de98aa6f367da15224b0e58fbde85b))

## [1.3.0](https://git.corp.kuaishou.com///compare/v1.2.0...v1.3.0) (2020-03-16)

### Features

- fix captcha ([b3fa641](https://git.corp.kuaishou.com///commit/b3fa6410ef55ecff16cd4f9edbd503e0a8e7d264))

## 1.2.1 (2020-03-02)

### Features

- 修复验证码问题 ([b3fa641](https://git.corp.kuaishou.com///commit/b3fa6410ef55ecff16cd4f9edbd503e0a8e7d264))

## 1.2.0 (2020-03-02)

### Features

- add typing ([54fb24a](https://git.corp.kuaishou.com///commit/54fb24aca6dd64b61f30b7a8140457f20298afe3))
- 添加非 Modal 模式的组件 & 升级依赖 ([f10380c](https://git.corp.kuaishou.com///commit/f10380c1bcff8ef7387b0712fbfb284b557df791))
