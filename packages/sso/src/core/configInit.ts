import { initKsDeviceInfo } from './deviceInfo'
import { getBaseUrl } from '@ks-passport/url-kit';
import { SSOConfig, Env, ICollectConfig } from '../types/common';
import { initRadar } from '@/core/radar';

let config: SSOConfig;

export const privateGetConfig = () => config;

export const setConfig = (conf: SSOConfig) => (config = conf);

export interface InitParam {
    // TODO STRING OR NUMBER
    sid: string;
    env: Env;
    callback?: string;
    // 快手中心账号域名 默认为 id.kuaihsou.com
    baseUrl?: string;
    useKsCaptcha?: boolean;
    // 业务账号中心域名 比如业务域名为A.com 那么业务账号中心域名一般为 id.A.com
    bizIdHost?: string;
    // 登出接口所在的host
    logoutHost?: string;
    // 登录成功之后的跳转页面
    followUrl?: string;
    // 未登录时的登录页面
    loginUrl?: string;
    // 在使用业务账号的时候是否自动同步登录态到中心账号域名
    syncToCenter?: boolean;
    // 新版验证码参数
    qrType?: string;
    kpn?: string;
    serviceOwnParams?: any;
    // 被使用方式渠道
    // (统一登录页使用: PC_PAGE, 登录弹窗: PC_MODAL, h5登录页:H5_PAGE)
    channelType?: string;
    platform?: 'PC' | 'H5' | 'OUTSIDE_H5';
    collectConfig?: ICollectConfig;
    kuaishouAuth?: boolean;
    // C端授权页不能调 quickLogin，需要跳回组件才能调
    disableQuickLogin?: boolean;
    // 是否开启sig4加签
    enableSig4?: boolean;
    // 业务自定义 request header
    customRequestHeader?: Record<string, any>;

}

export function init(params: InitParam): void {
    const {
        sid,
        env,
        callback,
        platform,
        kuaishouAuth,
        disableQuickLogin,
        enableSig4,
        customRequestHeader,
    } = params;
    if(params.env !== 'staging' && params.env !== 'production'){
        throw 'the "env" parameter must be either "staging" or "production"';
    }
    config = {
        ...config,
        platform,
        env,
        sid,
        baseUrl: params.baseUrl || getBaseUrl(env),
        // 业务账号中心域名
        bizIdHost: params.bizIdHost || '',
        // 登出接口host
        logoutHost: params.logoutHost || '',
        callback: callback || location.href,
        useKsCaptcha: params.useKsCaptcha || false,
        loginUrl: params.loginUrl,
        followUrl: params.followUrl,
        syncToCenter: params.syncToCenter,
        qrType: params.qrType,
        kpn: params.kpn,
        serviceOwnParams: params.serviceOwnParams,
        channelType: params.channelType || 'UNKNOWN',
        collectConfig: params.collectConfig,
        kuaishouAuth: kuaishouAuth ?? false,
        disableQuickLogin: disableQuickLogin ?? false,
        enableSig4: enableSig4 ?? false,
        customRequestHeader,
    };
    // devLog('初始化 config', config);
    initRadar(sid);
    if (config.collectConfig) { // 初始化风控
        initKsDeviceInfo(config.collectConfig);
    }
}
