<script>
    import {setContext, getContext, onMount, onDestroy, createEventDispatcher, afterUpdate} from 'svelte';
    import {get, writable} from 'svelte/store';
    const dispatch = createEventDispatcher();
    /** Index of the active tab (zero-based)
     * @svelte-prop {Number} [value=0]
     * */
    export let value = 0;
    /** Size of tabs
     * @svelte-prop {String} [size]
     * @values $$sizes$$
     * */
    export let size = '';
    /** Position of tabs list, horizontally. By default they're positioned to the left
     * @svelte-prop {String} [position]
     * @values is-centered, is-right
     * */
    export let position = '';
    /** Style of tabs
     * @svelte-prop {String} [style]
     * @values is-boxed, is-toggle, is-toggle-rounded, is-fullwidth
     * */
    export let style = '';
    export let expanded = false;

    const themeColor = getContext('themeColor');

    let activeTab = 0;
    $: changeTab(value);
    const tabs = writable([]);
    const tabConfig = {
        activeTab,
        tabs,
    };
    setContext('tabs', tabConfig);
    // 这么写是不行的，因为每个 tab 的 width 不一样
    // 还是要再 update之后找dom 然后拿到 offset 吗？
    let tabWidth = 0;
    let tabContainer;
    // make it reactive
    let sliderStyle = '';
    // This only runs as tabs are added/removed
    const unsubscribe = tabs.subscribe(ts => {
        if (ts.length > 0 && ts.length > value - 1) {
            ts.forEach(t => t.deactivate());
            if (ts[value]) ts[value].activate();
        }
    });
    function changeTab(tabNumber) {
        const ts = get(tabs);
        // NOTE: change this back to using changeTab instead of activate/deactivate once transitions/animations are working
        if (ts[activeTab]) ts[activeTab].deactivate();
        if (ts[tabNumber]) ts[tabNumber].activate();
        // ts.forEach(t => t.changeTab({ from: activeTab, to: tabNumber }))
        activeTab = tabConfig.activeTab = tabNumber;
        dispatch('activeTabChanged', tabNumber);
    }

    function updateTabSlider() {
        const activeTab = tabContainer && tabContainer.children[value];
        if (activeTab) {
            sliderStyle = `
        transform: translateX(${activeTab.offsetLeft}px);
        width: ${10}px;
      `;
        }
    }

    afterUpdate(() => {
        updateTabSlider();
    });

    onMount(() => {
        changeTab(activeTab);
        updateTabSlider();
    });
    onDestroy(() => {
        unsubscribe();
    });
</script>

<div class="tabs-wrapper" class:is-fullwidth={expanded}>
    <nav class="tabs {style}">
        <ul bind:this={tabContainer}>
            {#each $tabs as tab, index}
                <li
                    class:is-active={index === activeTab}
                    class="{themeColor}"
                >
                    <span on:click|preventDefault={() => changeTab(index)}>{tab.label}</span>
                </li>
            {/each}
        </ul>
        <div class="tab-slide" style={sliderStyle} />
    </nav>
    <section class="tab-content">
        <slot />
    </section>
</div>

<style type="text/stylus">
@import '../../common/variables.styl';
.tabs
  user-select none
  color font-color-primary
  font-size 14px
  font-weight bold
  ul
    display flex
    padding 0
    li
      list-style-type none
      cursor pointer
      vertical-align baseline
      line-height 20px
      &:hover,
      &.is-active
        color brand-primary
        &.blue
          color brand-primary-blue
      &.is-active
        font-size 18px
        transform translateY(-1px)
      & + li
        margin-left 24px
  .tab-slide
    display none
    background-color brand-primary
    transition transform .3s ease-in-out
    height 1px
.tabs-wrapper .tab-content
    display flex
    flex-direction row
    flex-wrap nowrap
    overflow-x hidden
</style>
