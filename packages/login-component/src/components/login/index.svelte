<script>
    import {Tabs, Tab} from '../Tabs';
    import QrLogin from './qrLogin.svelte';
    import MobileLogin from './mobile.svelte';
    import ChooseUser from './chooseUser.svelte';
    import {createEventDispatcher} from 'svelte';
    import {multiUserInfo} from './store';
    import {logger, sendCommonPV } from "../../common/logger";

    sendCommonPV('ACCOUNT_LOGIN_DEFAULT');

    const dispatch = createEventDispatcher();

    export let showThirdPartLogin = true;
    export let handleRegisterAction;
    export let sid;
    export let showRegister;
    export let indexPage;
    export let qrloginVersion;

    export let appealCustomTitle = '';
    export let appealCustomHref = '';

    // 自定义反馈按钮，允许的域名白名单
    // TODO: 此次临时写死，后续需要后端接口下发
    const appealCustomHostWhiteList = [
        // 快手开放平台 @chenyong05
        'open.kuaishou.com', // production
        'open-platform.staging.kuaishou.com', // staging
    ]
    const appealCustomHost = appealCustomHref && appealCustomHref.split('?')[0];
    const isAllowed = appealCustomHostWhiteList.some(host => {
        return appealCustomHost.indexOf(host) > -1;
    })

    if (!isAllowed) {
        appealCustomTitle = appealCustomHref = '';
    }

    const isVerifyIndexPage = indexPage === 'login-verify-sms'
        || indexPage === 'login-verify-sms-qrcode'

    const TypeChangeMap = isVerifyIndexPage
        ? [
            'verification',
            'password',
        ]
        : [
            'password',
            'verification',
        ]
    let phoneType = isVerifyIndexPage ? 'verification' : 'password';
    function changeTypeRegister() {
        if (typeof handleRegisterAction === 'function') {
            handleRegisterAction();
        } else {
            logger.sendImmediately(
                'CLICK',
                {
                    action: 'QUICK_REGISTER_BUTTON',
                }
            )
            dispatch('change-type', 'register')
        }
    }

    function goReport() {
        let reportHref = `https://csc.kuaishou.com/pass/visitor/checkToken?sid=passportFeedback.api&identificationSid=${sid}&followUrl=https%3A%2F%2Fcsc.kuaishou.com%2Ffeedback%3FentranceId%3D1083%26kpn%3DPASSPORT_FEEDBACK%26footerType%3D4%26navType%3D4%26sid%3DpassportFeedback.api%26identificationSid%3D${sid}`;
        if (appealCustomHref) {
            reportHref = appealCustomHref;
        }
        window.open(reportHref);
    }

    function onPhoneTypeChange(event) {
        phoneType = TypeChangeMap[event.detail];
        const tab_name = phoneType === 'password' ? phoneType : 'identity_code';
        logger.sendImmediately(
            'CLICK',
            {
                action: 'SWITCH_TAB',
                params: {
                    tab_name,
                },
                urlPage: {
                    page: 'ACCOUNT_LOGIN_DEFAULT'
                }
            }
        );
        // 相关元素的曝光
        logger.sendImmediately(
            'SHOW',
            {
                action: phoneType,
                urlPage: {
                    page: 'ACCOUNT_LOGIN_DEFAULT'
                }
            }
        );
    }
</script>

<div class="login">
    {#if !$multiUserInfo.userInfos.length}
        {#if indexPage !== "login-verify-sms" && indexPage !== "login-verify-sms-qrcode"}
            <Tabs on:activeTabChanged={onPhoneTypeChange}>
                <Tab label="密码登录" />
                <Tab label="验证码登录" />
            </Tabs>
        {:else}
            <Tabs on:activeTabChanged={onPhoneTypeChange}>
                <Tab label="验证码登录" />
                <Tab label="密码登录" />
            </Tabs>
        {/if}

        <MobileLogin
            on:login-success
            on:login-fail
            {phoneType}
            {qrloginVersion}
        />
        <div class="login-ops">
            {#if showRegister}
                <div
                    class="go-register go-register-{qrloginVersion}"
                    on:click={changeTypeRegister}
                >
                    立即注册
                </div>
            {/if}
            <div
                class="go-report go-report-{qrloginVersion}"
                on:click={goReport}
            >
                {appealCustomTitle || "登录反馈"}
            </div>
        </div>
    {:else}
        <ChooseUser
            phone={$multiUserInfo.phone}
            multiUserToken={$multiUserInfo.multiUserToken}
            userInfos={$multiUserInfo.userInfos}
            loginType={$multiUserInfo.loginType}
            on:login-success
            on:login-fail
        />
    {/if}
</div>

<style type="text/stylus">
@import '../../common/variables.styl'
.login
    position relative
    :global(.tabs)
        height 35px
        line-height 35px
    &-ops
        display flex
        justify-content center
        align-items center
.change-type
    position absolute
    top 31px
    right 40px
    z-index 1
    font-size 14px
    span
        cursor pointer
        color brand-primary
.go-register, .go-report
    font-size 14px
    text-align center
    color font-color-secondary
    height 36px
    line-height @height
    margin-top 17px
    cursor pointer
.go-register-v2, .go-report-v2
    font-size 12px
    margin-top 20px
.go-register::after
    content ''
    display inline-block
    width 1px
    height 12px
    margin 0 8px
    background-color font-color-disabled
    vertical-align -1px
</style>
