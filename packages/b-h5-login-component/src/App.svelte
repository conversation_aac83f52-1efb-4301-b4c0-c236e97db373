<script lang="ts">
    import { onMount } from 'svelte/internal';
    import { renderComponent } from '$lib/index';
    import { EnumDefaultLoginType } from "$lib/index";

    onMount(() => {
        renderComponent({
            target: document.body,
            env: 'staging',
            sid: 'kuaishou.shop.test',
            baseUrl: 'https://ksid.staging.kuaishou.com',
            onSuccess(res) {
                console.log('success', res);
            },
            onFail(res) {
                console.log('fail', res);
            },
            onLoginStart() {
                console.log('start');
            },
            appTitle: "快手本地生活商家版",
            loginPageConfig: {
                bAccountConfig: {
                    agreements: [
                        {
                            "title": "《隐私政策》",
                            "url": "https://www.kuaishou.com/about/policy?tab=privacy"
                        }
                    ],
                    baseAgreementOnClick: () => {
                        console.log('baseAgreementOnClick');
                    }
                },
                cAccountConfig: {
                    agreements: [
                        {
                            "title": "《用户协议》",
                            "url": "https://www.kuaishou.com/about/policy?tab=agreement"
                        },
                        {
                            "title": "《隐私政策》",
                            "url": "https://www.kuaishou.com/about/policy?tab=privacy"
                        }
                    ],
                }
            },
            enableSig4: true,
            defaultLoginType: EnumDefaultLoginType.code,
        })
    });
</script>
<!--<div class="w-screen h-screen flex items-center justify-center">-->
<!--</div>-->

<style>
    .container1 {
        width: 456px;
        border: 1px solid #000;
        /*height: 500px;*/
    }
</style>
