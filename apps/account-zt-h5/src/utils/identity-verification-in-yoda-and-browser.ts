// @ts-ignore 「Promise pending」
// import { startIdentityVerification } from '@ks/identity-verification/dist/verify-in-yoda-and-browser.esm.js';

// @ts-ignore 「卡 Loading」
// import { startIdentityVerification } from '@ks/identity-verification/dist/verify-in-browser.esm.js';

// @ts-ignore 「Promise pending」
// import { startIdentityVerification } from '@ks/identity-verification/dist/verify-in-ks-and-browser.esm.js';

// @ts-ignore 「可用」提示 Uncaught Error: Cannot find module '@ks/yoda-kuaishou-plugin', 安装后这个包后可用。
import { startIdentityVerification } from '@ks/identity-verification/dist/verify-in-ksyoda-and-browser.esm.js';

import { Toast } from '@ks/sharp-ui';

export interface ZtIdentityVerification {
    ztIdentityVerificationCheckToken: string | undefined;
    ztIdentityVerificationType: number | undefined;
}

/**
 * 获取验证参数
 */
let ztIdentityVerification: ZtIdentityVerification | null = null;

/**
 * 进行验证并保存验证参数用户回放
 * @param url
 * @returns
 */
export async function startIdentityVerifyInYodaAndBrowser(url: string) {
    let {
        // eslint-disable-next-line prefer-const
        result,
        // eslint-disable-next-line prefer-const
        token,
        // eslint-disable-next-line prefer-const
        type,
    } = await startIdentityVerification({
        url,
    });
    if (result !== 1) {
        Toast.error('验证失败，请重试');
        // 验证失败
        return false;
    }

    // 验证成功，返回token和type, 需要拿着这俩参数回放请求
    ztIdentityVerification = {
        ztIdentityVerificationCheckToken: token,
        ztIdentityVerificationType: type,
    };
    return true;
}

/**
 * 获取验证参数进行接口回放
 * @returns {ZtIdentityVerification | null}
 */
export function getZtIdentityVerificationInYodaAndBrowser() {
    const identityVerification = ztIdentityVerification;
    ztIdentityVerification = null;
    return identityVerification;
}
