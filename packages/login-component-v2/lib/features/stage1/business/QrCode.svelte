<script lang="ts">
    import { getContext, createEventDispatcher, onDestroy } from 'svelte';
    import { cancelQrLogin, getQRLoginInfo, getUserLoginInfo, scanQRLoginResultWeb } from '@ks/general-sso';
    import { onMount } from "svelte";
    import { bindPhoneInfo } from "$lib/features/stage1/kuaishou/store";
    import { EnumLoginType } from "$lib/shared/const";
    import IconPass from "$lib/utils/IconPass.svelte";
    import { twMerge } from "tailwind-merge";


    const configStore = getContext('loginPageConfig');
    const isMainAccountPage = getContext('isMainAccountPage');
    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    const onLoginStart = getContext('onLoginStart');
    export let height:number;
    // 入口的展示，一种是在右上角绝对定位的形式，一种是以 Tab 的形式切换展示
    export let displayType = 'absolute';
    let scanTextClass = twMerge(
        'text-[12px] font-[400] leading-[20px] flex flex-col items-center',
        displayType === 'absolute' ? 'mt-[24px]' : 'mt-[16px]',
    );

    // let containerClass = twMerge(
    //     'flex flex-col items-center',
    //     // displayType === 'absolute' ? '' : 'pt-[28px]',
    // );


    export let showTitle = true;

    let scanConfig;
    if (isMainAccountPage) {
        scanConfig = $configStore.bAccountConfig.scanConfig;
    } else {
        scanConfig = $configStore.bSubAccountConfig.scanConfig;
    }
    let appName = scanConfig?.appName || '快手小店';
    let status = 'scan';
    let qrLoginInfo = {};
    let errorMsg = '';


    async function start() {
        console.log('start', start);
        // 重新开始的时候 清除一下错误
        // setErrorMsg('');
        cancelQrLogin();
        try {
            onLoginStart({
                name: EnumLoginType.scanB,
            });
            qrLoginInfo = await getQRLoginInfo();
            status = 'scan';
            // dispatch('code-need-refresh', status);
            // 这块的res可能为user(老扫码) 或者 {qrLoginSignature: 'xxx'}(新扫码)
            console.log('getUserLoginInfo start');
            const res = await getUserLoginInfo({
                qrLoginSignature: qrLoginInfo.qrLoginSignature,
                qrLoginToken: qrLoginInfo.qrLoginToken,
            });
            console.log('getUserLoginInfo', res);
            status = 'confirm';
            const result = await scanQRLoginResultWeb({
                // 这块扫码有两套 新的扫码优先使用 scanResult 返回的签名
                // 老的扫码用 start 返回的签名
                qrLoginSignature: res.qrLoginSignature || qrLoginInfo.qrLoginSignature,
                qrLoginToken: qrLoginInfo.qrLoginToken,
            });
            console.log('result', result);
            onSuccess({
                loginType: EnumLoginType.scanB,
                result,
            });
        } catch (e) {
            console.log('e', e);
            onFail({
                loginType: EnumLoginType.scanB,
                error: e,
            });
            status = 'timeout';
        }
    }
    onMount(() => {
        start();
    });
    onDestroy(() => {
        console.log('onDestroy cancel');
        cancelQrLogin();
    });
    // 如果组件高度小于预设高度
</script>

<div class="qrcode-container"
     style="height: {height}px"
>
    {#if displayType === 'absolute'}
        <div class="qrcode-title">扫码登录</div>
    {/if}
    <div class="qrcode-img-wrapper">
        {#if qrLoginInfo.imageData}
            <img
                    class="qrcode-img"
                    src="data:image/png;base64,{qrLoginInfo.imageData}"
                    alt="qrcode"
                    class:mask={errorMsg || ['confirm', 'timeout'].includes(status)}
            />
        {/if}
        <div class="qrcode-status qrcode-status-{status}"
        >
            {#if errorMsg}
                <div class="text-[14px] font-[600] leading-[22px]">{errorMsg+'二维码已过期'}</div>
                <div class="scan-button"
                     on:click={start}>
                    刷新
                </div>
            {:else if status === 'confirm'}
                <IconPass />
                <p class="qrcode-desc">扫码成功，请确认登录</p>
            {:else if status === 'timeout'}
                <div class="timeout">二维码已过期</div>
                <div class="scan-button"
                     on:click={start}>
                    刷新
                </div>
            {/if}
        </div>
    </div>
    <div class={scanTextClass}>
        {#if scanConfig?.rawDescHTML}
            {@html scanConfig.rawDescHTML}
        {:else}
            <div>打开 <span class="appName">{appName}</span> APP</div>
            <div>点击左上角侧边栏扫一扫</div>
        {/if}
    </div>
</div>

<style>
    span.appName {
        color: var(--colorB);
    }
    .qrcode-status {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;

        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 8px;
    }
    .mask {
        opacity: 0.05;
    }
    .qrcode-desc {
        color: #000000;
        font-size: 14px;
        font-weight: 600;
        line-height: 22px;
        text-align: left;
        vertical-align: top;
    }
    .currentC {
        background: var(--colorC);
    }
    .currentB {
        background: var(--colorB);
    }
    .qrcode-container {
        width: 100%;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
    }
    .qrcode-title {
        color: #000000;
        font-size: 20px;
        font-weight: 600;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 28px;
        text-align: left;
        vertical-align: top;
        margin-bottom: 22px;
    }
    .qrcode-img-wrapper {
        position: relative;
        height: 100%;
        aspect-ratio: 1;
        max-height: 240px;
        border: 1px solid #E6E9ED;
        border-radius: var(--buttonRadius);
    }
    .qrcode-img {
        width: 100%;
        max-width: 240px;
        border-radius: var(--buttonRadius);
    }
    .scan-button {
        background: var(--colorB);
        border-radius: var(--buttonRadius);
        padding-top: 2px;
        padding-bottom: 2px;
        padding-left: 20px;
        padding-right: 20px;

        color: #FFFFFF;
        font-size: 12px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: left;
        vertical-align: middle;
    }
    .scan-button:hover {
        cursor: pointer;
    }
    .timeout {
        color: #222222;
        font-size: 14px;
        font-weight: 600;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 22px;
        text-align: left;
        vertical-align: top;
    }
</style>
