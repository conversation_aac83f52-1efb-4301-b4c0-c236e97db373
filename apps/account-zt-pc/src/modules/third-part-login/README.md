# 第三方登录页面
底部的第三方登录按钮：
https://zt-passport.prt.kuaishou.com/pc/account/login?sid=kuaishou.jh.test2&redirectURL=https%3A%2F%2Fksid.test.gifshow.com

## 流程梳理（原经过 account-pc-server 的流程）

以`@ks/login-component`组件上的第三方登录按钮为入口。

1、点击按钮，`window.open`跳转到

```js
`https://passport.kuaishou.com/pc/account/third-part-login-page/${type}?followUrl=${thirdPartLoginFollowUrl ? encodeURIComponent(thirdPartLoginFollowUrl) : encodeURIComponent(location.href)}&sid=${sid}`
```

2、访问`/pc/account/third-part-login-page/${type}`页面时，在 Nodejs 里会走到`account.controller.ts`下的`@Get('/third-part-login-page/:type')`路由

- 请求`/pass/kuaishou/pc/pageInfo`接口，返回数据结构如下：

```js
// https://id.kuaishou.com/pass/kuaishou/pc/pageInfo?sid=kuaishou.account.test，该 sid 未做配置
{
    result: 1,
    background: [ ],
    thirdPartyLogin: false,
    callbackUrls: [ ],
    logo: [ ],
    type: 0
}

// https://id.kuaishou.com/pass/kuaishou/pc/pageInfo?sid=kuaishou.account.test，该 sid 做了配置
{
    result: 1,
    background: [ ],
    thirdPartyLogin: true,
    callbackUrls: [
        "gifshow.com",
        "live.kuaishou.com",
        "passport.kuaishou.com",
        "music.kuaishou.com",
        "id.kuaishou.com",
        "www.kuaishou.com"
    ],
    logo: [ ],
    type: 0
}
```

- 不符合 url 格式的 followUrl 参数，会从 query 里移除掉；符合的话，返回 html

3、 进入到第 1 步的页面

- 获取 query.followUrl，组成新的 followUrl，`https://passport.kuaishou.com/pc/account/third-part-login-page/${type}?callback=${encodeURIComponent(query.followUrl)}&followUrl=${query.followUrl}`，调用 @ks/sso 的 thirdPartLogin 方法

4、@ks/sso 的 thirdPartLogin 方法会跳转到微信二维码登录页面，登录成功或失败后，302 到新的 followUrl 上，并携带 data 数据

```js
// 登录成功时携带的 data 为：
data = {
    authToken: "ChNrdWFpc2hvdS53ZWIuYXBpLmF0ErABk2677ldW4mQO8vmJ-87J9MUYdiBlOgjVqvxvIv7eamqpxVhpFIEgDnZHtsw2z-vX8owmKZ72RkhPlD5rGwRy7bd0JrQmbCwASW72PU1EZAM7HLEfj-ZHuKWm2SUsnkGtluQji6ZAlxUvuRjFaTIGxVU1DCV-1dx1zelO_n_mGokNECExHB-fj-ntPPFrMO1d3O7YIscYHBqRuQlpxvn7cxYd7jgXarnbuNe3uh_PyMgaEnnElT1oFFXHvnUhAyPpqkI7JyIgoSXkA6m9OTygJYbqgKgRr40WwTJgXZn6sPwd6gsx19AoBTAB"
    result: 1
    sid: "kuaishou.web.api"
}

// 登录失败，携带的 data 为：
data = {
    result:*********,
    error_msg:"检测到跳转地址不合法，请访问该链接申请添加callback白名单:https://passport-portal.corp.kuaishou.com/#/apply/sidWhitelist?sid=kuaishou.account.test&callBackUrl=https://www.baidu.com"
}
```

5、请求新 followUrl 时，会再次在 nodejs 里走到`account.controller.ts`下的`@Get('/third-part-login-page/:type')`路由

- 因为新 followUrl 上存在请求参数 callback，会跟第 2 步一样再次请求`/pass/kuaishou/pc/pageInfo`接口，并校验 callback 是否合法
  - 不合法的话，会移除 callback 参数，再 302 到新 followUrl 上（没有 callback 参数）
  - 若合法，则直接返回 html

6、再次进入`/pc/account/third-part-login-page/${type}`页面，处理`data`参数，跳转到新`followUrl`里的`followUrl`参数

## 存在的问题

目前第三方登录存在比较多的问题：

1、整个流程过长，都是先跳转到 third-part-login-page 这个中间页后再跳转到微信/QQ 进行授权，授权完成后再跳转回 third-part-login-page 页面，再跳转到业务方页面。如果可以，可以将 third-part-login-page 这个页面移除掉。

2、即使第三方授权完成后，也只是种植了帐号中心的全局登录态，而业务方局部登录态还没有。需要业务方使用`@ks/sso`里的`refreshLoginStatus`或类似方式将全局登录态转换成局部登录态。

注意，如果有业务方要使用`@ks/login-component`上的第三方登录功能，一定要让业务方在打开他们页面的第一时间调用`@ks/sso`的`refreshLoginStatus`方法
