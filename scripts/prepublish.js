/* eslint-disable no-undef */

const PKN = process.env.npm_package_name;
const VERSION = process.env.npm_package_version;
const TAG = (VERSION.match(/alpha|beta|rc/) || [])[0]||'';
const pluginTag = process.env.NPM_TAG || '';

console.log(`准备发布 ${PKN}@${VERSION}\n`);

if(TAG !== pluginTag) {
    console.error(`tag 不一致，发布插件 tag 为 ${pluginTag} ，package.json tag 为 ${TAG}`);
    process.exit(1);
}

const pipelineMap = {
    "@ks/general-sso": "https://halo.corp.kuaishou.com/devcloud/pipeline/history/465310",
    "@ks/identity-verification": "https://halo.corp.kuaishou.com/devcloud/pipeline/history/465373",
    "@ks/login-component": "https://halo.corp.kuaishou.com/devcloud/pipeline/history/465560",
    "@ks/passport-iframe-client": "https://halo.corp.kuaishou.com/devcloud/pipeline/history/465562",
    "@mfe/sms-verification": "https://halo.corp.kuaishou.com/devcloud/pipeline/history/465568",
    "@ks/sso": "https://halo.corp.kuaishou.com/devcloud/pipeline/history/465363",
    "@ks/sso-logger": "https://halo.corp.kuaishou.com/devcloud/pipeline/history/465569"
};

// CI_JOB_ID 流水线ID
if (!process.env.CI_JOB_ID) {
    console.error(`本地发布失败，请使用流水线发布：${pipelineMap[PKN]}\n`);
    process.exit(1); //which terminates the publish process
}
