<script>
    import {setContext, onMount} from 'svelte';
    import { isLegalCallbackUrl } from '@ks/sso';
    import Login from './components/login/index.svelte';
    import Register from './components/register/index.svelte';
    import QrLogin from './components/login/qrLogin.svelte';
    import QrLoginV2 from './components/login/qrLoginV2.svelte';
    import { logger } from './common/logger';
    import { isLoginQrcodeType } from './common/config.ts';

    export let isProduction;
    export let onSuccess;
    /**
     * @type {
     *  'login-qrcode' | 'login-password' | 'login-verify-sms' | 'register'
     *  | 'register-qrcode' | 'login-password-qrcode' | 'login-verify-sms-qrcode'
     * }
     * x-qrcode 是并列显示二维码
     */
    export let indexPage;
    export let onFail;
    export let handleRegisterAction;
    export let showRegister;
    export let showPlatformSwitch;
    export let showThirdPartLogin;
    export let thirdPartLoginFollowUrl;

    export let sid;

    export let appealCustomTitle;
    export let appealCustomHref;
    export let themeColor;

    let qrloginVersion;
    let isThirdPartLoginCallbackUrlLegal = true;

    setContext('isProduction', isProduction);
    setContext('themeColor', themeColor);
    let platform = indexPage === 'login-qrcode' ? 'qrcode' : 'pc';
    let type = indexPage === 'register' || indexPage === 'register-qrcode' ? 'register' : 'login';
    function changeType(e) {

        type = e.detail;
    }
    function togglePlatform() {
        if (platform === 'pc') {
            logger.sendImmediately(
                'CLICK',
                {
                    action: 'SCAN_CODE_LOGIN',
                }
            )
            logger.sendImmediately(
                'SHOW',
                {
                    action: 'ACCOUNT_LOGIN',
                }
            )

            platform = 'qrcode';
        } else {
            platform = 'pc'
            logger.sendImmediately(
                'CLICK',
                {
                    action: 'ACCOUNT_LOGIN',
                }
            )
            logger.sendImmediately(
                'SHOW',
                {
                    action: 'SCAN_CODE_LOGIN',
                }
            )

        }
    }
    async function handleThirdPartLogin(type) {
        logger.sendImmediately('CLICK', {
            action: 'THIRD_PART',
            params: { type },
            urlPage: {
                page: 'ACCOUNT_LOGIN_DEFAULT'
            }
        });
        const followUrl = thirdPartLoginFollowUrl || location.href;

        try {
            isThirdPartLoginCallbackUrlLegal = await isLegalCallbackUrl(followUrl);

            if (isThirdPartLoginCallbackUrlLegal) {
                window.open(`https://passport.kuaishou.com/pc/account/third-part-login-page/${type}?followUrl=${encodeURIComponent(followUrl)}&sid=${sid}`);
            }
        } catch {
            isThirdPartLoginCallbackUrlLegal = false;
        }
    }
    onMount(() => {
        if (platform === 'pc') {
            logger.sendImmediately(
                'SHOW',
                {
                    action: 'SCAN_CODE_LOGIN',
                }
            )
        }

        qrloginVersion = 'v1'
        if (isLoginQrcodeType(indexPage)) {
            qrloginVersion = 'v2'
        }
    })
</script>

<div class="container container-{qrloginVersion}">
    {#if qrloginVersion === "v2"}
        <div class="login-wrap-{qrloginVersion}">
            {#if type === "login"}
                <Login
                    on:change-type={changeType}
                    on:login-success={onSuccess}
                    on:login-fail={onFail}
                    {handleRegisterAction}
                    {sid}
                    {showRegister}
                    {indexPage}
                    {qrloginVersion}
                    {appealCustomTitle}
                    {appealCustomHref}
                />
                {#if showThirdPartLogin}
                    <div
                        class="third-part-login third-part-login-{qrloginVersion}"
                    >
                        第三方登录
                        <img
                            src="https://s2-10623.kwimgs.com/udata/pkg/cloudcdn/img/wechat.7c26c5cd.svg"
                            on:click={() => handleThirdPartLogin("wechat")}
                        />
                        <img
                            src="https://s2-10623.kwimgs.com/udata/pkg/cloudcdn/img/qq.0f70559b.svg"
                            on:click={() => handleThirdPartLogin("qq")}
                        />
                    </div>
                    {#if !isThirdPartLoginCallbackUrlLegal}
                        <div class="third-part-login-error-msg">
                            跳转地址不合法，请检查
                        </div>
                    {/if}
                {/if}
            {:else}
                <Register
                    {qrloginVersion}
                    on:change-type={changeType}
                    on:register-sucess={onSuccess}
                />
            {/if}
        </div>
        <div class="qrcode-wrap">
            <QrLoginV2 on:login-success={onSuccess} on:login-fail={onFail} />
        </div>
    {:else}
        <div class="login-wrap">
            {#if type === "login"}
                {#if showPlatformSwitch}
                    <div
                        class="platform-switch platform-switch-{platform} {themeColor}"
                        on:click={togglePlatform}
                    >
                        {#if platform === "pc"}
                            <div class="platform-switch-tips {themeColor}">
                                扫码登录
                            </div>
                        {/if}
                    </div>
                {/if}
                {#if platform === "pc"}
                    <Login
                        on:change-type={changeType}
                        on:login-success={onSuccess}
                        on:login-fail={onFail}
                        {handleRegisterAction}
                        {sid}
                        {showRegister}
                        {indexPage}
                        {appealCustomTitle}
                        {appealCustomHref}
                    />
                {:else}
                    <div class="title">快手APP，扫码登录</div>
                    <QrLogin
                        on:login-success={onSuccess}
                        on:login-fail={onFail}
                    />
                {/if}
                {#if showThirdPartLogin}
                    <div class="third-part-login">
                        第三方登录
                        <img
                            src="https://s2-10623.kwimgs.com/udata/pkg/cloudcdn/img/wechat.7c26c5cd.svg"
                            on:click={() => handleThirdPartLogin("wechat")}
                        />
                        <img
                            src="https://s2-10623.kwimgs.com/udata/pkg/cloudcdn/img/qq.0f70559b.svg"
                            on:click={() => handleThirdPartLogin("qq")}
                        />
                    </div>
                    {#if !isThirdPartLoginCallbackUrlLegal}
                        <div class="third-part-login-error-msg">
                            跳转地址不合法，请检查
                        </div>
                    {/if}
                {/if}
            {:else}
                <Register
                    on:change-type={changeType}
                    on:register-sucess={onSuccess}
                />
            {/if}
        </div>
    {/if}
</div>

<style type="text/stylus">
.container
    position relative
    width 100%
    min-height 336px
.container-v2
    display flex
.platform-switch
    background red
    position absolute
    top 12px
    right @top
    width 40px
    height @width
    background top right / 64px no-repeat
    z-index 1
    cursor pointer
    // 遮盖层
    &:after
        content ""
        position absolute
        top 0
        right 0
        width 0
        height 0
        border solid 20px
        border-color transparent transparent white white
        z-index 100
    &-pc
        background-image: url("https://ali.static.yximgs.com/kos/nlav10758/pc/login/img/icon-qrcode.83c73a9d02df3de9.svg")
        background-size: 40px
        &.blue
            background-image: url("https://ali.static.yximgs.com/kos/nlav10761/login-icon/icon-qrcode.blue.svg")
    &-qrcode
        background-image: url("https://ali.static.yximgs.com/kos/nlav10758/pc/login/img/icon-pc.c57030114fadc8b2.svg")
        background-size: 29px
        &.blue
            background-image: url("https://ali.static.yximgs.com/kos/nlav10761/login-icon/icon-pc.blue.svg")
    &-tips
        position absolute
        top: 50%
        right: 106%
        border 1px solid #fe3666;
        border-radius 4px
        background rgba(254,54,102,0.10);
        color: #fe3666;
        width: 60px;
        text-align center
        padding: 10px;
        transform translateY(-50%);
        z-index 200
        user-select none
        font-size: 14px
        &.blue
            color #326BFB;
            border-color #326BFB;
            background rgba(50,107,251,0.10);
        &:before
        &:after
            content ""
            position absolute;
            top: 50%;
            left: 100%;
            transform translate(0px, -50%);
            border solid 4px transparent;
            border-left-color #FFECE6;
            z-index 101;
        &:before
            transform translate(1px, -50%);
            border-width: 5px
            border-left-color #ff4906
            z-index 100
        &.blue:before
            border-left-color #326BFB;
.login-wrap
    padding: 25px 32px 15px;
.login-wrap-v2
    padding: 24px 32px 20px;
    width: 316px;
    position relative
    z-index 300
    :after
        position absolute
        content ''
        width 1px
        height 284px
        top 24px
        right 0
        background rgba(225,225,225,0.40)
.qrcode-wrap
    position: relative;
    width: 294px;
.title
    font-size 18px
    font-weight 500
    color #222222
.third-part-login
    font-size 14px
    color #666
    height 64px
    display flex
    align-items center
    border-top 1px solid #eee
    margin 16px 0 -15px
    img
        width 24px
        height 24px
        cursor pointer
        margin-left 16px
.third-part-login-error-msg
    font-size 14px
    line-height 20px;
    color #fe3666;
.third-part-login-v2
        font-size 12px
        color #898A8C
        border-top 0
        &:before
            position absolute
            content ''
            width 675px
            height 1px
            bottom 68px
            left 0
            background rgba(225,225,225,0.40)
</style>
