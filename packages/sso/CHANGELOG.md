# @ks/sso

## 2.10.2-beta.2

### Patch Changes

- 增加 customRequestHeader 字段，并更正 userId types

## 2.10.2-beta.1

### Patch Changes

- 更正 types 路径

## 2.10.2-beta.0

### Patch Changes

- 透出 passToken 和 bUserId，并锁死 sig4 版本

## 2.10.1

### Patch Changes

- 8dd33263: sso-logger fix
- 264b2f32: getQRLoginInfo 增加函数参数 serviceOwnParams，qrType
- 15842a97: 解析@符号
- 5372b11a: 增加 scanQRAcceptResult
- 0b01d712: 使用 father 打包 nobundle 产物
- e467370c: 提供 nobundle 产物，放在 xiaodian 目录下
- 5fe80378: 删除 logger 相关逻辑
- 0977e900: sso-logger 写死版本
- 3884b7c4: 增加 logoutV1
- c1cdb7dd: 增加不包含登录态重置密码接口

## 2.10.1-beta.9

### Patch Changes

- 增加不包含登录态重置密码接口

## 2.10.1-beta.8

### Patch Changes

- 删除 logger 相关逻辑

## 2.10.1-beta.7

### Patch Changes

- sso-logger 写死版本

## 2.10.1-beta.6

### Patch Changes

- sso-logger fix

## 2.10.1-beta.5

### Patch Changes

- 使用 father 打包 nobundle 产物

## 2.10.1-beta.4

### Patch Changes

- 解析@符号

## 2.10.1-beta.3

### Patch Changes

- 提供 nobundle 产物，放在 xiaodian 目录下

## 2.10.1-beta.2

### Patch Changes

- 增加 logoutV1

## 2.10.1-beta.1

### Patch Changes

- 增加 scanQRAcceptResult

## 2.10.1-beta.0

### Patch Changes

- getQRLoginInfo 增加函数参数 serviceOwnParams，qrType

## 2.10.0

### Patch Changes

- 636fadc8: sig4 修改
- 611afb89: sig4 加签
- 314bd4fd: SNSLoginCode 抛出错误
- d97835cc: 登录和发送验证码兼容 account
- 87404c9a: 修改 cancelRequest 以适配 sig4
- ac613c6f: sig4 过滤 null
- 16df36bd: 增加 isWebSig4 参数
- 3c8008da: 扫码接口加上风控
- 167faf3c: debug
- 87b9cee7: 去除 ksBid 限制
- 9ae9f5b4: 删除无用代码
- dfff78eb: sig4 fix
- 5b9b30fb: 授权登录成功后，返回 callback 给业务方
- a5b70477: 增加旧版扫码授权登录逻辑
- df987171: bugfix
- fe4814fc: sig4 名单配置
- 175e95f8: sig4 兼容加密逻辑
- 8337a133: quickLoginByKsAuth 接入风控
- 42e50931: 修改 quickLoginUri
- 4470ff32: 增加 SNSLoginCode
- c92f5b78: sig4 调整
- 0a22791c: 上传版本号
- 7686da94: 增加雷达上报
- 7a7ee215: sig4 日志上传

## 2.10.0-beta.28

### Patch Changes

- 修改 cancelRequest 以适配 sig4

## 2.10.0-beta.27

### Patch Changes

- 登录和发送验证码兼容 account

## 2.10.0-beta.26

### Patch Changes

- 增加旧版扫码授权登录逻辑

## 2.10.0-beta.25

### Patch Changes

- sig4 过滤 null

## 2.10.0-beta.24

### Patch Changes

- 增加 isWebSig4 参数

## 2.10.0-beta.23

### Patch Changes

- sig4 日志上传

## 2.10.0-beta.22

### Patch Changes

- 授权登录成功后，返回 callback 给业务方

## 2.10.0-beta.21

### Patch Changes

- bugfix

## 2.10.0-beta.20

### Patch Changes

- 扫码接口加上风控

## 2.10.0-beta.19

### Patch Changes

- e0a7575a: quickLoginByKsAuth 接入风控

## 2.10.0-beta.18

### Patch Changes

- 删除无用代码

## 2.10.0-beta.17

### Patch Changes

- debug

## 2.10.0-beta.16

### Patch Changes

- sig4 调整

## 2.10.0-beta.15

### Patch Changes

- sig4 修改

## 2.10.0-beta.14

### Patch Changes

- sig4 兼容加密逻辑

## 2.10.0-beta.13

### Patch Changes

- sig4 名单配置

## 2.10.0-beta.12

### Patch Changes

- sig4 fix

## 2.10.0-beta.11

### Patch Changes

- sig4 加签

## 2.10.0-beta.10

### Patch Changes

- SNSLoginCode 抛出错误

## 2.10.0-beta.9

### Patch Changes

- 去除 ksBid 限制

## 2.10.0-beta.8

### Patch Changes

- 修改 quickLoginUri

## 2.10.0-beta.7

### Patch Changes

- 上传版本号

## 2.10.0-beta.6

### Patch Changes

- 增加雷达上报

## 2.10.0-beta.5

### Patch Changes

- 增加 SNSLoginCode

## 2.10.0-beta.4

### Patch Changes

- 增加 disableQuickLogin 参数

## 2.10.0-beta.3

### Patch Changes

- 升级至最新版本号

## 2.10.0-beta.2

### Patch Changes

- 升级至最新版本号

## 2.10.0-beta.1

- 合并 2.9.1-beta.13 代码

## 2.10.0-beta.0

- e8f7cfda6: 优化代码结构，现有功能、业务逻辑不变

## 2.9.1-beta.13

### Patch Changes

- 新增对 sid 为 ksBid 开头的处理逻辑

## 2.9.1-beta.12

### Patch Changes

- 删除无用代码及 log

## 2.9.1-beta.11

### Patch Changes

- 删除 quickLogin 逻辑

## 2.9.1-beta.10

### Patch Changes

- B 端登录删除 quickLogin 逻辑

## 2.9.1-beta.9

### Patch Changes

- 只对快手的接口增加 kuaishouAuth 参数

## 2.9.1-beta.8

### Patch Changes

- 给接口统一增加 kuaishouAuth 参数

## 2.9.1-beta.7

### Patch Changes

- requestMobileCode 接口增加 kuaishouAuth 参数

## 2.9.1-beta.6

### Patch Changes

- 修改 quickLogin 字段名

## 2.9.1-beta.5

### Patch Changes

- 邮箱注册登录接口支持 B 端

## 2.9.1-beta.3

### Patch Changes

- 支持扫码登录 B 端

## 2.9.1-beta.0

### Patch Changes

- 支持 B 端账号登录

## 2.9.0

### Minor Changes

- ba424490: 发布 2.8.10 正式包
- 516791a5: 发布 2.9.0-beta.0

### Patch Changes

- 516791a5: 去掉 workspace
- 516791a5: 合并 @ks/sso v2 和 v3
- 989a069c: 修改 @ks/sso 依赖 jsencrypt 的 patch 代码
- eb692a6a: fix: 类型声明文件路径问题
- 516791a5: 发布测试
- 516791a5: 接入风控采集 SDK
- 516791a5: 增加 identityVerificationAdapter 兼容 0.x 和 1.x 逻辑
- 36679a54: 更新 jsencrypt patch 代码
- 516791a5: 修改脚本，不影响生产

## 2.9.0-beta.1

### Patch Changes

- 接入风控采集 SDK

## 2.9.0-beta.0

### Minor Changes

- 发布测试

## 2.8.10

### Minor Changes

- 发布 2.8.10 正式包

## 2.8.10-beta.3

### Patch Changes

- 7ddbdd8a: 去掉 workspace
- 合并 @ks/sso v2 和 v3
- 7ddbdd8a: 发布测试
- e38e9b78: 增加 identityVerificationAdapter 兼容 0.x 和 1.x 逻辑
- 5ec32ec1: 修改脚本，不影响生产

## 3.0.3-beta.3

### Patch Changes

- e38e9b78: 增加 identityVerificationAdapter 兼容 0.x 和 1.x 逻辑

## 3.0.3-beta.2

### Patch Changes

- 修改脚本，不影响生产

## 3.0.3-beta.1

### Patch Changes

- "@ks/sso-logger": "workspace:^0.2.4", 去掉 workspace

## 3.0.3-beta.0

### Patch Changes

- 发布测试

## 2.8.10-beta.2

### Patch Changes

- 修改 @ks/sso 依赖 jsencrypt 的 patch 代码

## 2.8.10-beta.1

### Patch Changes

- 更新 jsencrypt patch 代码

## 2.8.10-beta.0

### Patch Changes

- fix: 类型声明文件路径问题

更高版本的 CHANGELOG 另请参见[在线文档](https://docs.corp.kuaishou.com/k/home/<USER>/fcACOr52xxtq96dq_4FwlhtM_)。

## [2.8.9]

- 代码仓库拆分、迁移至新地址。修复 2.8.8 的 jsencrypt+3.2.1 补丁丢失问题。

## [2.8.9-alpha.1]

- 依赖升级 @ks/sso-logger@0.2.4

## [2.8.7](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/sso/2.8.6...@ks/sso/2.8.7) (2022-09-20)

### Bug Fixes

- **sso:** 升级 @ks/sso-logger, 修复 development 环境 /pass/kuaishou/getCdns 接口报跨域问题 ([eb1f58b](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/eb1f58bbfc59c71557015b0867970061619a4c83))

## [2.8.6](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/sso/2.8.5...@ks/sso/2.8.6) (2022-09-20)

### Features

- **@ks/sso:** getQRLoginInfo 方法返回 qrUrl 参数 ([db942a4](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/db942a47b8e99493be5c83c37f13a74ce7c98c78))

## [2.8.5](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/sso/2.8.4...@ks/sso/2.8.5) (2022-09-15)

### Features

- **@ks/sso:** 增加 platform 参数用于 @ks/identity-verification

## [2.8.4](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/sso/2.8.3...@ks/sso/2.8.4) (2022-09-05)

### Bug Fixes

- **@ks/sso:** 修复在 SSR import 包时浏览器全局变量报错的问题 ([63dca57](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/63dca5785f7bb6fa130383a2063216e6de10f3fa))

## [2.8.3](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/sso/2.8.1...@ks/sso/2.8.3) (2022-07-25)

### Features

- 暴露方法参数类型 ([3b981a7](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/3b981a7665f19114d041ec6a774b14106c5d29c7))

## [2.8.2](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/sso/2.8.1...@ks/sso/2.8.2) (2022-07-13)

### Bug Fixes

- 修复未构建依赖包 ([bb36192](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/bb36192c77f698ce6c4f1ea28f82d56f1cb2728a))

## [2.8.1](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/sso/2.8.0...@ks/sso/2.8.1) (2022-07-06)

### Fix

- 修复 2.8.0 未构建就发布的问题，2.8.1 的功能同 2.8.0 相同

# [2.8.0](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/sso/2.7.2...@ks/sso/2.8.0) (2022-07-06)

### Features

- 增加判断第三方登录时 callbackUrl 是否有效的方法，增加新的登出方法 ([0d5a842](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/0d5a8429b786124d157b3a210bca12e066cdf2a7))

## [2.7.2](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/sso/2.7.2-beta.6...@ks/sso/2.7.2) (2022-06-30)

2.7.2 之前发版不规范，2.7.2 以后规范发版

### Features

- 为 @ks/identity-verification 添加 qrcodeEnv 以启用 PC 二维码，[2.7.2-beta.6] (2022-06-24)
- 透传 @ks/identity-verification 的 -999，[2.7.2-beta.5] (2022-06-23)
- `refreshLoginStatus`方法增加`openFollowUrlInNewWindow`参数，[2.7.2-beta.4] (2022-06-02)
- `refreshLoginStatus`方法添加`setRootDomain`参数，支持在根域种 Cookie，[2.7.2-beta.3] (2022-05-31)
- 升级动态日志上报 SDK，[2.7.2-beta.1] (2022-03-17)

## [2.7.1-beta.1] (2022-02-22)

升级行文验证 sdk(@ks/identity-verification 至 0.2.8)

## [2.7.0-beta.3] (2022-02-15)

密码的加密

## [2.6.7-beta.2] (2022-01-05)

idc-占位符注入

## [2.6.3] (2021-11-25)

加入 sso-logger 进行接口日志上报

## [2.6.0] (2021-08-25)

xhr 网络错误（断网）时，添加标识，填充默认文案"网络错误"

## [2.5.4] (2021-06-23)

扫码签名支持新的扫码流程 从 scanResult 会返回 user（老扫码）或者签名（新扫码）

## [2.5.3] (2021-05-16)

增加新版扫码登录

## [2.5.1] (2021-05-12)

修复 captcha 中的 request host 问题

## [2.5.0] (2021-04-12)

- https://docs.corp.kuaishou.com/d/home/<USER>
- 支持业务账号域名，如 A.com 为业务域名（域名 不是 以 kuaishou.com 结尾的），那么业务账号域名一般为 id.A.com，具体登录逻辑见上面的 doc
- 这个方案主要解决了当业务域名不以 kuaishou.com 时，浏览器隐私模式下跨越种 cookie 的问题，具体背景参见 https://docs.corp.kuaishou.com/d/home/<USER>

- 增加了 3 个接口
- \_pullLoginToken 当从业务账号域名没有拉到 passToken 的时候，需要从中心账号域名拉 token
- \_syncLoginStatus 当业务登录成功之后，需要同步登录态到业务账号域名下
- \_logoutUsingBizHost 当使用业务账号域名的 case，登出时使用

在这种业务场景下（即 config 中的 bizIdHost 不为空的时候），下面 api 做了一些逻辑变更

- init 增加了一些业务账号域名以及相关参数配置，api 说明见 README
- passToken 拉取失败时，增加了自动调用\_pullLoginToken 的逻辑（业务可以根据自身情况禁用这个默认操作），api 说明见 README
- scanQRLoginResult、passwordLogin、register、chooseUser、login、emailRegister 增加了获取 authToken 成功后，自动同步中心账号域名登录态的操作，会自动调用\_syncLoginStatus 方法（业务可以根据自身情况禁用这个默认操作），api 说明见 README
- logout 会自动使用\_logoutUsingBizHost 的登出逻辑，业务可以根据自身情况禁用这个默认操作，api 说明见 README

## [2.4.7] (2021-02-25)

- `login`，`chooseUser`和`emailRegister`增加`setCookie`参数，表示注册后是否要设置登录态 cookie（是的话就会设置登录态，原来有登录态的话旧登录态就会失效）

## [2.4.6] (2020-12-02)

- decalre global 中去掉对标准 HTMLScriptElement 的扩展，避免与其他包冲突

## [2.4.5] (2020-11-24)

- 对 injectScript 传入的 url 进行安全校验
- 去掉默认的 cancelRequest，只对主动调用 cancelRequest 的请求进行 cancel

## [2.4.4] (2020-11-17)

- 部分 API 增加对 account 参数的支持

## [2.4.3] (2020-10-15)

- 增加邮箱注册、邮箱登录、发送邮箱验证码、密码重置、校验密码强度、获取国家码、换绑等功能

## [2.4.2] (2020-09-22)

- requestMobileCode 的抛错被吞掉的问题修复

## [2.4.1] (2020-09-15)

- getLoginToken 的返回类型问题修复

## [2.4.0] (2020-09-10)

- 发送短信验证码接入统一身份认证
- 登录注册时根据后端新的错误码自动进行验证码验证流程

## [2.2.1] (2020-08-06)

sso 域名协议统一补全为 https

## [2.1.3](https://git.corp.kuaishou.com/explore-frontend/general-project/compare/v1.5.3...v2.1.3) (2020-05-28)

## [2.1.2](https://git.corp.kuaishou.com/explore-frontend/general-project/compare/v1.5.2...v2.1.2) (2020-05-28)

## [2.1.1] (2020-05-27)

env 增加 staging 环境信息设置

## [2.1.0] (2020-05-14)

支持快手自研验证码

## [2.0.1](https://git.corp.kuaishou.com/explore-frontend/general-project/compare/v1.4.1...v2.0.1) (2020-04-03)

修复跨域 cookie 问题

## [2.0.0] (2020-03-24)

修改代码引入方式（tree shaking 支持），移除对 qs 和 axios 的依赖，减少代码体积

## [1.0.6] (2020-03-16)

## [1.0.5] (2020-03-16)
