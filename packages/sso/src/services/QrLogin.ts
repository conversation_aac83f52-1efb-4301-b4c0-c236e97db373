import { request, cancelRequest } from '../core/request';
import { privateGetConfig } from '../core/configInit';
import { getLoginToken } from '../core/loginToken';
import { AuthTokenResult, BaseRes } from '../types/services';
import { identityVerificationInterceptor } from '@/core/verificationInterceptor';
import { wrapLoginRequest } from '@/core/bidLoginAdapter';
// import {
//     getStsUrl,
//     getPassTokenUrl,
//     url_stringify,
// } from '@ks-passport/url-kit';
// import { saveTokenInIframe } from './saveToken';

export interface QRCodeResult {
    qrLoginSignature: string;
    status: string;
    result: number;
    next: string;
}

export interface BaseUserInfo {
    eid: string;
    headurl: string;
    headurls: Array<{
        cdn: string;
        url: string;
        urlPattern: string;
    }>;
    user_id: number;
    user_name: string;
    user_sex: string;
}

export interface QRCodeInfo {
    qrLoginToken: string;
    qrLoginSignature: string;
}

export interface QRLoginInfo extends QRCodeInfo {
    imageData: string;
    qrUrl: string;
}

export interface AcceptResult {
    callback: string;
    qrToken: string;
    result: number;
    sid: string;
    serviceOwnToken?: string;
}

function useNewQrAuth() {
    const config = privateGetConfig();
    // 是否使用新版的验证码登录流程
    return config.qrType && config.kpn;
}

/**
 * 手机扫码后会触发本接口的返回
 *
 * @param QRCodeInfo
 * @returns QRCodeResult | BaseUserInfo
 * @description 这里有两套扫码 老的扫码返回BaseUserInfo 新的扫码返回 QRCodeResult
 */
export function getUserLoginInfo({qrLoginToken, qrLoginSignature}: QRCodeInfo) {
    const config = privateGetConfig();
    const url = useNewQrAuth() ? config.baseUrl + '/rest/c/infra/ks/new/qr/scanResult' : config.baseUrl + '/rest/c/infra/ks/qr/scanResult';
    cancelRequest(url);
    return identityVerificationInterceptor<{ user: BaseUserInfo } | QRCodeResult>(
        url,
        {
            qrLoginToken,
            qrLoginSignature,
        },
    ).then(res => {
        // 这里有两套扫码 老的扫码返回 res.user
        // 新的扫码直接返回res {qrLoginSignature: 'xxx', status: 'SCANNED', result: 1, next: 'acceptResult'}
        if (useNewQrAuth()) {
            // 新扫码
            return res;
        }
        // 老扫码
        return (res as { user: BaseUserInfo }).user;
    });
}

export const SNSLoginCode = async (code: string) => {
    const config = privateGetConfig();
    return await getLoginToken('/pass/bid/web/sns/login/code', {
        sid: config.sid,
        // 写死
        state: 'bid_kuaishou',
        code,
        // 必填参数，这里需要传字符串
        callback: '',
        registerOpen: true,
    });
}

export function scanQRAcceptResult({qrLoginToken, qrLoginSignature}: QRCodeInfo) {
    const config = privateGetConfig();
    const url = useNewQrAuth() ? '/rest/c/infra/ks/new/qr/acceptResult' : '/rest/c/infra/ks/qr/acceptResult';
    return identityVerificationInterceptor<AcceptResult>(
        config.baseUrl + url,
        {
            qrLoginToken,
            qrLoginSignature,
            sid: config.sid,
        },
    );
}

export function scanQRLoginResult({qrLoginToken, qrLoginSignature}: QRCodeInfo) {
    const config = privateGetConfig();
    const url = useNewQrAuth() ? '/rest/c/infra/ks/new/qr/acceptResult' : '/rest/c/infra/ks/qr/acceptResult';
    return identityVerificationInterceptor<AcceptResult>(
        config.baseUrl + url,
        {
            qrLoginToken,
            qrLoginSignature,
            sid: config.sid,
        },
    ).then(async (res) => {
        if (config.kuaishouAuth && useNewQrAuth()) {
            // console.log('res in scanQRLoginResult', res);
            const code = JSON.parse(res.serviceOwnToken!).code;
            if (!config.disableQuickLogin) {
                const r = await SNSLoginCode(code);
                return r;
            }
            // C端授权PC页面需要跳回组件，再调 SNSLoginCode
            return res;
        }
        const r = getLoginToken('/pass/kuaishou/login/qr/callback', {
            qrToken: res.qrToken,
            sid: config.sid,
        });
        return wrapLoginRequest(r).then(cbRes => {
            // 这块的逻辑是给开放平台账号打通用的，这块透传serviceOwnToken
            let r = cbRes;
            r.serviceOwnToken = res.serviceOwnToken;
            return r;
        });
    });
}

export interface qrLoginInfoOption {
    serviceOwnParams?: any;
    qrType?: string;
}
export function getQRLoginInfo(option?: qrLoginInfoOption) {
    const config = privateGetConfig();
    const url = useNewQrAuth() ? '/rest/c/infra/ks/new/qr/start' : '/rest/c/infra/ks/qr/start';
    const param: Record<string, any> = {
        sid: config.sid,
    };
    if (useNewQrAuth()) {
        param.qrType = option?.qrType || config.qrType;
        param.kpn = config.kpn;
        if (config.serviceOwnParams) {
            param.serviceOwnParams = option?.serviceOwnParams || config.serviceOwnParams;
        }
        if (config.kuaishouAuth) {
            param.kuaishouAuth = config.kuaishouAuth;
        }
    }
    // devLog('扫码参数', param, config.serviceOwnParams, param.serviceOwnParams);
    return identityVerificationInterceptor<QRLoginInfo>(
        config.baseUrl + url,
        param,
    ).then(({qrLoginSignature, qrLoginToken, imageData, qrUrl}) => ({
        qrLoginSignature,
        qrLoginToken,
        imageData,
        qrUrl,
    }));
}

export function qrScanned(params: { qrLoginToken: string }) {
    const config = privateGetConfig();
    return request<BaseRes>(
        config.baseUrl + '/pass/kuaishou/login/qr/scanned',
        params,
    );
}

export function qrAccept(params: { qrLoginToken: string }) {
    const config = privateGetConfig();
    return request<{
        result: number;
        redirectUri?: string;
        hasUri?: boolean;
    }>(
        config.baseUrl + '/pass/kuaishou/login/qr/accept',
        params,
    );
}

export function qrScannedInfo(params: { qrLoginToken: string }) {
    const config = privateGetConfig();
    return request<{
        result: number;
        icon: string;
        dis_msg: string;
    }>(
        config.baseUrl + '/pass/kuaishou/login/qr/getInfo',
        params,
    );
}

export function cancelQrScanLogin(params: { qrLoginToken: string }) {
    const config = privateGetConfig();
    return request<BaseRes>(
        config.baseUrl + '/pass/kuaishou/login/qr/cancelled',
        params,
    );
}

export function cancelQrLogin() {
    const config = privateGetConfig();
    // 因为扫码登录一定是串行的逻辑
    cancelRequest(config.baseUrl + '/rest/c/infra/ks/qr/scanResult');
    cancelRequest(config.baseUrl + '/rest/c/infra/ks/new/qr/scanResult');
}
