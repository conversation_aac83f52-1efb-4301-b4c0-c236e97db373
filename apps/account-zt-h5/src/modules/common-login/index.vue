<template>
    <div class="common-login" :class="{ 'small-webview': isSmallWebview }">
        <template v-if="whichPageState !== PAGESTATE.choose">
            <div class="common-login-logo-default" v-if="logoUrl === ''"></div>
            <div class="common-login-logo"
                 :style="{ backgroundImage: `url(${logoUrl})`}"
                 v-else
            ></div>
            <div class="common-login-ways">
                <!-- 短信验证码登录 -->
                <div class="common-login-verifycode" v-if="whichWay === WAYS.verifycode">
                    <div :class="{['common-login-verifycode-phone']: true, 'common-login-error': errors.verifycodePhoneError.value}">
                        <input
                            placeholder="请输入手机号"
                            v-model="inputs.verifycodePhone.value"
                            type="tel"
                            pattern="\d*"
                            maxlength="11"
                            @input="limitNumber($event, 'verifycodePhone')"
                            @change="limitNumber($event, 'verifycodePhone')"
                            @focus="handleInputFocus('phone')"
                            @blur="handleInputBlur($event, 'phone')"
                        >
                        <div
                            v-show="isPhoneClearIconVisible"
                            class="common-login-verifycode-clear"
                            @mousedown.prevent
                            @click="clear('verifycodePhone')"
                        ></div>
                    </div>

                    <div :class="{['common-login-verifycode-code']: true, 'common-login-error': errors.verifycodeCodeError.value}">
                        <div class="common-login-verifycode-box">
                            <input
                                placeholder="请输入验证码"
                                v-model="inputs.verifycodeCode.value"
                                type="tel"
                                pattern="\d*"
                                maxlength="6"
                                @focus="handleInputFocus('code')"
                                @blur="handleInputBlur($event, 'code')"
                            >
                            <div
                                v-show="isCodeClearIconVisible"
                                class="common-login-verifycode-clear"
                                @mousedown.prevent
                                @click="clear('verifycodeCode')"
                            ></div>
                        </div>
                        <div class="common-login-verifycode-verify">
                            <div class="common-login-verifycode-timeout" v-if="isSended">
                                重新发送({{Countdown}})
                            </div>
                            <div
                                :class="{['common-login-verifycode-send']: true, ['common-login-verifycode-can-send']: verifycodePhoneIsValid}"
                                v-else
                                @click="send"
                            >
                                发送验证码
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 账号密码登录 -->
                <div class="common-login-password" v-if="whichWay === WAYS.password">
                    <div :class="{['common-login-password-phone']: true, 'common-login-error': errors.verifycodePhoneError.value}">
                        <input
                            placeholder="请输入手机号"
                            v-model="inputs.verifycodePhone.value"
                            type="tel"
                            pattern="\d*"
                            maxlength="11"
                            @input="limitNumber($event, 'verifycodePhone')"
                            @change="limitNumber($event, 'verifycodePhone')"
                            @focus="handleInputFocus('phone')"
                            @blur="handleInputBlur($event, 'phone')"
                        >
                        <input type="text" :style="{width: 0, opacity: 0}">
                        <div
                            v-show="isPhoneClearIconVisible"
                            class="common-login-password-clear"
                            @mousedown.prevent
                            @click="clear('verifycodePhone')"
                        ></div>
                    </div>
                    <div :class="{['common-login-password-password']: true, 'common-login-error': errors.passwordPwdError.value}">
                        <div class="common-login-password-box">
                            <input type="text" :style="{width: 0, opacity: 0}">
                            <input
                                :type="showPassword ? 'text' : 'password'"
                                placeholder="请输入密码"
                                v-model="inputs.passwordPwd.value"
                                @focus="handleInputFocus('password')"
                                @blur="handleInputBlur($event, 'password')"
                            >
                            <div
                                v-show="isPasswordClearIconVisible"
                                class="common-login-password-clear common-login-password-pwdclear"
                                @mousedown.prevent
                                @click="clear('passwordPwd')"
                            ></div>
                        </div>
                        <div
                            :class="{['common-login-password-toggle-hide']: true, ['common-login-password-toggle-show']: showPassword}"
                            @click="togglePassword"
                        >
                        </div>
                    </div>
                    <div class="common-login-password-error">
                        <div class="right-forget-pwd" @click="forgetPwdEvent">
                            忘记密码？
                        </div>
                    </div>
                </div>
                <!-- 用户协议 -->
                <div class="common-login-agreement">
                    <div
                        :class="{['common-login-agreement-icon']: true, ['common-login-agreement-checked']: agreementState}"
                        @click="toggleAgreement"
                    ></div>
                    <div class="common-login-agreement-text">
                        <b @click="toggleAgreement">我已阅读并同意</b>
                        <span><a :href="servicePageUrl">用户协议</a></span>
                        <b @click="toggleAgreement">和</b>
                        <span><a :href="privacyPageUrl">隐私政策</a></span>
                    </div>
                </div>
                <!-- 登录按钮 -->
                <div class="common-login-btns">
                    <div
                        v-if="whichPageState === PAGESTATE.normal"
                        :class="{['common-login-btns-login']: true, 'common-login-btns-login-bright': isLoginBtnBright}"
                        @click="login"
                    >
                        登录
                    </div>
                    <div
                        v-if="whichPageState === PAGESTATE.logging"
                        class="common-login-btns-logging"
                    >
                        <div class="common-login-btns-logging-inner"></div>
                    </div>
                    <div class="common-login-btns-changed-way" @click="changeWay">
                        {{ changedWayText }}
                    </div>
                </div>
            </div>
            <!-- 微信登录按钮 -->
<!--            <div class="common-login-wx" v-if="isInWeixin">-->
			<!--	暂时隐藏      -->
            <div class="common-login-wx" v-if="false">
                <div class="common-login-wx-tips">
                    其他登录方式
                </div>
                <div class="common-login-wx-other-ways">
                    <div class="common-login-wx-other-way" @click="wxLogin"></div>
                </div>
            </div>
        </template>
        <MultiAccounts
            v-if="whichPageState === PAGESTATE.choose"
            :accounts="accounts.value"
            :is-small-webview="isSmallWebview"
            @back="back"
            @choose-account="chooseToLogin"
        ></MultiAccounts>
        <forget-pwd-dialog :show.sync="showForgetPwdModal"></forget-pwd-dialog>
        <AgreementPopup
            :show.sync="showAgreementPopup"
            :service-page-url="servicePageUrl"
            :privacy-page-url="privacyPageUrl"
            @agree="agree"
        ></AgreementPopup>
    </div>
</template>

<script lang="ts">
// import Cookies from 'js-cookie';
import { defineComponent, ref, computed, watch, Ref, reactive, onMounted } from '@vue/composition-api';
import { init, requestMobileCode, login as ssoLogin, passwordLogin, chooseUser } from '@ks/sso';
import { parse, stringify } from 'qs';
import { Toast } from '@ks/sharp-ui';

import getAuthUrl from '../../utils/getAuthUrl';
import toParam, { ToParam } from '../../utils/toParam';
import { isInWeixin } from '../../utils/inWhichApp';
import { isDev } from '@/common/config/base';
import webLog, { sendTaskEvent } from '../../utils/weblog';
import { LOGININFO, WAYS, update, get } from '../../utils/cache';
import ksLog from '../../utils/kslog';
import WeblogRiskPlugin from '@ks/weblogger/lib/plugins/riskMgt';
import { updateLoggerLoginChannel, LoginChannelType } from '@/utils/weblog';
import { loginViaWx, getLoginPageInfoFetcher, LoginConfig } from '../../api/common-login';
import useSWRV from 'swrv';
import ksBridge from '@ks/ks-bridge';

import MultiAccounts from './components/MultiAccounts.vue';
import AgreementPopup from './components/AgreementPopup.vue';
import ForgetPwdDialog from './components/forget-pwd-dialog.vue';
import { useSendFmp } from '@/common/hooks/useSendFmp';
import { ACCOUNTS, ACCOUNT } from './types';
import {
    KUAISHOU_PROTOCOL_POLICY,
    KUAISHOU_PROTOCOL_POLICY_SMALL,
    KUAISHOU_PROTOCOL_SERVICE,
    KUAISHOU_PROTOCOL_SERVICE_SMALL
} from '@/common/config/links';
import { MUTI_ACCOUNTS_CODE, COUNTRY_CODE } from '@/common/config/constants'

const loginInfoCache:LOGININFO = get();

// 更新login用法为pc登录页
updateLoggerLoginChannel(LoginChannelType.H5_PAGE);

enum ERRORS {
    PhoneError = 'PhoneError',
    CodeError = 'CodeError',
    PwdError = 'PwdError',
}

enum PAGESTATE {
    normal,
    logging,
    choose,
}

const wechatConfig = {
    // // 授权登录测试公众号appid，淮安康祥福
    // appId: 'wxb608a69f693177ad',
    // // 测试公众号网页授权域名
    // wxOauthOrigin: 'https://gw-test.kuaishoupay.com',
    // wxOauthHost: 'gw-test.kuaishoupay.com',

    // 快手公众号 appid
    appId: 'wx9227d48257374438',
    // appId: 'wx2107eed138e272f1',
    // 快手公众号网页授权域名
    wxOauthOrigin: 'https://id.kuaishou.com',
    wxOauthHost: 'id.kuaishou.com',
};

const ks_appid = 'ks_kuaishou_sns_oauth_wechat2';

function getQuery(): Object {
    let query: any = location.search;
    if (query) {
        query = parse(query.slice(1));
    } else {
        query = {};
    }
    return query;
}

const query: any = getQuery();
const createAccount = query.createAccount !== 'false';
const code = query.code;
// 用于回跳的，下面是等价和优先级关系
// callback == stsUrl + followUrl == stsUrl + redirectUrl
// callback > sts
// redirectUrl > followUrl
const callback = query.callback;
const redirectURL = query.redirectURL;
// 唯一id，记录从调起登录页的点击到登录完成（含成功和失败），登录完成后销毁，调起登录页的点击时候由h5站外创建，账号中台在登录页面流程过程中透传
const startLoginSessionId = query.startLoginSessionId || '';
const sid = query.sid || 'kuaishou.web.api';
const source = query.source;

webLog.addPluginInstance(
    new WeblogRiskPlugin({
        env: isDev() ? 'test' : 'production',
        bussType: sid,
        taskType: '8',
        subTaskType: '8-1',
    }),
);

init({
    env: isDev() ? 'staging' : 'production',
    sid: sid || 'kuaishou.web.api',
    channelType: LoginChannelType.H5_PAGE,
    enableSig4: true,
});

interface INJECTQUERY {
    followUrl?: string;
    authToken: string;
    sid: string;
}

function genRedirectUrl(token: Ref<string>, res: any) {
    const stsUrl = callback ? callback : res.stsUrl;
    const followUrl = redirectURL || res.followUrl;
    const injectQuery:INJECTQUERY = {
        authToken: token.value,
        sid: sid,
    };
    if (followUrl) {
        injectQuery.followUrl = followUrl;
    }
    const urlInfo = stsUrl.split('?');
    return `${urlInfo[0]}?${stringify({ ...parse(urlInfo[1]), ...injectQuery })}`;
}

function getWXRedirectUrl(query: ToParam) {
    const authHost = wechatConfig.wxOauthOrigin + '/pass/kuaishou/login/sns/passcode';

    const params = query;
    params.code = '';
    const code = toParam({ code: '' });
    // 删除code参数原因：
    // 进入页面的时候如果带有code参数，当没有用户信息cookie的时候会通过code获取unionId
    // 如果页面的code非法：过期或错误 将会再一次重定向到授权页面，重定向回来后url会带上新的code参数
    // 如果不删除url中非法的code，授权页加上的新code参数后，返回的时候url可能解析出2个code参数（一个新获取的，一个非法不可用的）
    // 需要重定向的原因就是获取新的合法code，故删除旧code参数

    const paramsStr = toParam(params);

    // 这块与当前origin保持统一吧 测试环境、staging和线上环境
    const base = `${window.location.origin}/account-h5/login`;
    const landUri = paramsStr
        ? `${authHost}?redirectUrl=${base}?${encodeURIComponent(paramsStr)}`
        : `${authHost}?redirectUrl=${base}?${encodeURIComponent(code)}`;

    const wxRedirectUrl = getAuthUrl({
        redirectUri: landUri,
        appid: wechatConfig.appId,
    });

    return wxRedirectUrl;
}

type CLEARTYPE = 'verifycodePhone' | 'verifycodeCode' | 'verifycodePhone' | 'passwordPwd';

const CountdownNum = 60;

export default defineComponent({
    components: {
        AgreementPopup,
        ForgetPwdDialog,
        MultiAccounts,
    },
    setup(props: any, { root }: { root: any }) {
        useSendFmp();
        // 请求 sid 对应的 logo和协议 等信息
        const { data } = useSWRV(sid, getLoginPageInfoFetcher(sid), {
            revalidateOnFocus: false,
        });
        const logoUrl = computed(() => data.value?.logoUrl);
        const privacyPolicy = computed(() => data.value?.privacyPolicy);
        const userAgreement = computed(() => data.value?.userAgreement);
        watch(data, (value) => {
            if (value?.title) {
                document.title = value.title;
                ksBridge.isSupportAsync({
                    api: 'setPageTitle',
                    complete: (isSupport: Boolean) => {
                        if (isSupport) {
                            ksBridge.setPageTitle(value.title);
                        }
                    },
                });
            }
        });
        onMounted(() => {
            window.addEventListener('visibilitychange', () => {
                console.log('visibilitychange 重新设置', data.value?.title);
                if (data.value?.title) {
                    document.title = data.value.title;
                    ksBridge.isSupportAsync({
                        api: 'setPageTitle',
                        complete: (isSupport: Boolean) => {
                            if (isSupport) {
                                ksBridge.setPageTitle(data.value?.title);
                            }
                        },
                    });
                }
            })
        });

        const whichWay = ref<WAYS>(typeof loginInfoCache.way === 'undefined' ? WAYS.verifycode : loginInfoCache.way);
        const whichPageState = ref<PAGESTATE>(PAGESTATE.normal);
        const showPassword = ref<boolean>(false);
        const agreementState = ref<boolean>(!!loginInfoCache.agreement);
        const showAgreementPopup = ref<boolean>(false);
        const inputs = {
            verifycodePhone: ref<string>(loginInfoCache.phone || ''), // 验证码登录手机号
            verifycodeCode: ref<string>(loginInfoCache.code || ''), // 验证码登录验证码
            passwordPwd: ref<string>(loginInfoCache.password || ''), // 密码登录密码
        };

        function checkPhoneNum(phoneNum: string) {
            if (!(/^1[3-9]\d{9}$/.test(phoneNum))) {
                return false;
            }
            return true;
        }

        function wxLogin() {
            webLog.sendImmediately('CLICK', {
                action: 'THIRD_PART_LOGIN',
                params: {
                    type: 'wechat',
                    is_agree: agreementState.value ? 'TRUE' : 'FALSE',
                },
            });
            if (agreementState.value) {
                const wxRedirectUrl = getWXRedirectUrl(query);
                location.href = wxRedirectUrl;
            } else {
                showAgreementPopup.value = true;
            }
        }


        const verifycodePhoneIsValid = computed(() => {
            return checkPhoneNum(inputs.verifycodePhone.value.trim());
        });

        const isSended = ref<boolean>(false);
        const Countdown = ref<number>(CountdownNum);

        watch(isSended, (now, old) => {
            if (!old && now) {
                const timer = setInterval(() => {
                    Countdown.value -= 1;
                    if (Countdown.value === 0) {
                        clearInterval(timer);
                        isSended.value = !isSended.value;
                        Countdown.value = CountdownNum;
                    }
                }, 1000);
            }
        });

        const isSmallWebview = computed(() => {
            return root.$route.query.smallWebview === 'true';
        });

        const privacyPageUrl = computed(() => {
            if (data.value?.privacyPolicy) {
                return data.value.privacyPolicy;
            }
            return isSmallWebview.value
                ? KUAISHOU_PROTOCOL_POLICY_SMALL
                : KUAISHOU_PROTOCOL_POLICY;
        });

        const servicePageUrl = computed(() => {
            if (data.value?.userAgreement) {
                return data.value.userAgreement;
            }
            return isSmallWebview.value
                ? KUAISHOU_PROTOCOL_SERVICE_SMALL
                : KUAISHOU_PROTOCOL_SERVICE;
        });

        // 手机号、密码/验证码 输入正确，即可点亮登录按钮
        const isLoginBtnBright = computed(() => {
            let res: any = false;
            switch (whichWay.value) {
                case WAYS.verifycode:
                    res = verifycodePhoneIsValid.value
                        && inputs.verifycodeCode.value.trim()
                        && inputs.verifycodeCode.value.trim().length >= 4
                        && inputs.verifycodeCode.value.trim().length <= 6;
                    break;
                case WAYS.password:
                    res = verifycodePhoneIsValid.value && inputs.passwordPwd.value.trim();
                    break;
            }
            return res;
        });

        const canLogin = computed(() => {
            return agreementState.value && isLoginBtnBright.value;
        });

        const changedWayText = computed(() => {
            switch (whichWay.value) {
                case WAYS.verifycode:
                    return '使用账号密码登录';
                case WAYS.password:
                    return '使用验证码登录';
            }
            return '';
        });

        const errors = {
            verifycodePhoneError: ref<boolean>(false), // 验证码登录手机号
            verifycodeCodeError: ref<boolean>(false), // 验证码登录验证码
            passwordPwdError: ref<boolean>(false), // 密码登录密码
        };


        async function send() {
            const start = Date.now();
            try {
                if (!verifycodePhoneIsValid.value) {
                    return;
                }
                webLog.sendImmediately('CLICK', {
                    action: 'GET_AUTH_CODE_BUTTON',
                    params: {},
                });
                isSended.value = !isSended.value;
                const res = await requestMobileCode({
                    countryCode: COUNTRY_CODE,
                    phone: inputs.verifycodePhone.value.trim(),
                    type: 53,
                });

                sendTaskEvent({
                    action: 'GET_AUTH_CODE_RESULT',
                    params: {
                        package: 'result_package',
                        result_code: 1,
                        cost_time: Date.now() - start,
                    },
                });
                Toast.info('验证码已发送');
            } catch (e) {
                sendTaskEvent({
                    action: 'GET_AUTH_CODE_RESULT',
                    params: {
                        package: 'result_package',
                        result_code: e.result,
                        cost_time: Date.now() - start,
                    },
                });
                Toast.error(e.error_msg || (e.result === -999 ? '用户已取消' : '出错了请重试'));
            }
        }

        const multiUserToken = ref<string>('');

        // loginViaCode, loginViaPwd, loginViaChoose
        async function loginViaCodeAction() {
            return await ssoLogin({
                countryCode: COUNTRY_CODE,
                phone: inputs.verifycodePhone.value,
                smsCode: inputs.verifycodeCode.value,
            });
        }

        async function loginViaPwdAction() {
            return await passwordLogin({
                countryCode: COUNTRY_CODE,
                phone: inputs.verifycodePhone.value,
                password: inputs.passwordPwd.value,
            });
        }

        async function loginViaChooseAction(accountInfo: ACCOUNT) {
            return await chooseUser({
                multiUserToken: multiUserToken.value,
                userId: accountInfo.userId,
                phone: inputs.verifycodePhone.value,
                countryCode: COUNTRY_CODE,
            });
        }

        async function loginViaWxAction() {
            return await loginViaWx({
                sid,
                appId: ks_appid,
                code,
                createId: createAccount,
            });
        }
        const token = ref<string>('');
        onMounted(async () => {
            try {
                webLog.sendImmediately('PV', {
                    type: 'enter',
                    page: whichWay.value === WAYS.verifycode ? 'AUTH_CODE_LOGIN_H5' : 'PASSWORD_LOGIN_H5',
                    params: {
                        sid,
                        sub_biz: source,
                        has_wechat: isInWeixin ? 'TRUE' : 'FALSE',
                        start_login_session_id: startLoginSessionId,
                    },
                });
                if (code) {
                    const start = Date.now();
                    webLog.sendImmediately('CLICK', {
                        action: 'USER_LOGIN',
                        params: {
                            click_area: 'wechat',
                        },
                    });
                    const res: any = await loginViaWxAction();
                    token.value = res[`${sid}.at`];

                    sendTaskEvent({
                        action: 'USER_LOGIN_RESULT',
                        params: {
                            sid,
                            account_type: WAYS.wx,
                            start_login_session_id: startLoginSessionId,
                            click_area: 'wechat',
                            package: 'result_package',
                            result_code: res.result,
                            cost_time: Date.now() - start,
                        },
                    });
                    location.href = genRedirectUrl(token, res);
                }
            } catch (e) {
                Toast.error(e.error_msg || (e.result === -999 ? '用户已取消' : '出错了请重试'));
            }
        });
        // multiUserToken | {sid}.at作为sts的authToken  还有sid
        const accounts = reactive<ACCOUNTS>({
            value: [],
        });


        // 登录这块的处理流程
        async function login() {
            let res: any;
            const start = Date.now();
            try {
                if (canLogin.value) {
                    webLog.sendImmediately('CLICK', {
                        action: 'USER_LOGIN',
                        params: {
                            click_area: 'login_button',
                        },
                    });
                    whichPageState.value = PAGESTATE.logging;
                    if (whichWay.value === WAYS.verifycode) {
                        res = await loginViaCodeAction();
                    } else {
                        res = await loginViaPwdAction();
                    }
                    ksLog(`zt-login-login_${res.result}`);
                    sendTaskEvent({
                        action: 'USER_LOGIN_RESULT',
                        params: {
                            sid,
                            account_type: whichWay.value,
                            start_login_session_id: startLoginSessionId,
                            click_area: 'login_button',
                            package: 'result_package',
                            result_code: 1,
                            cost_time: Date.now() - start,
                        },
                    });
                    whichPageState.value = PAGESTATE.normal;
                    token.value = res[`${sid}.at`];
                    location.href = genRedirectUrl(token, res);
                } else if (!agreementState.value) {
                    showAgreementPopup.value = true;
                    webLog.sendImmediately('CLICK', {
                        action: 'CHOOSE_AGREEMENT_BINT_BUTTON',
                        params: {},
                    });
                }
            } catch (e) {
                ksLog(`zt-login-login_${e.result}`);
                if (e.result === MUTI_ACCOUNTS_CODE) {
                    whichPageState.value = PAGESTATE.choose;
                    multiUserToken.value = e.multiUserToken;
                    accounts.value = e.userInfos;
                } else {
                    whichPageState.value = PAGESTATE.normal;
                    Toast.error(e.error_msg || (e.result === -999 ? '用户已取消' : '出错了请重试'));
                }
                sendTaskEvent({
                    action: 'USER_LOGIN_RESULT',
                    params: {
                        sid,
                        account_type: whichWay.value,
                        start_login_session_id: startLoginSessionId,
                        click_area: 'login_button',
                        package: 'result_package',
                        result_code: e.result,
                        cost_time: Date.now() - start,
                    },
                });
            }
        }

        async function chooseToLogin(accountInfo: ACCOUNT) {
            const start = Date.now();
            try {

                webLog.sendImmediately('CLICK', {
                    action: 'CHOOSE_ACCOUNT_BUTTON',
                    status: 'START',
                    params: {},
                });
                const res: any = await loginViaChooseAction(accountInfo);
                sendTaskEvent( {
                    action: 'CHOOSE_ACCOUNT_RESULT',
                    status: 'SUCCESS',
                    params: {
                        // res 上没有 result
                        // 暂且按成功传1
                        result_code: 1,
                        sid,
                        account_type: whichWay.value,
                        start_login_session_id: startLoginSessionId,
                        cost_time: Date.now() - start,
                    },
                });
                ksLog('zt-login-loginViaChooseAction_success');
                token.value = res[`${sid}.at`];
                location.href = genRedirectUrl(token, res);
            } catch (e) {

                sendTaskEvent({
                    action: 'CHOOSE_ACCOUNT_RESULT',
                    status: 'FAIL',
                    params: {
                        result_code: e.data?.result || e.result,
                        sid,
                        account_type: whichWay.value,
                        start_login_session_id: startLoginSessionId,
                        cost_time: Date.now() - start,
                    },
                });

                ksLog(`zt-login-loginViaChooseAction_${e.result}`);
                Toast.error(e.error_msg || (e.result === -999 ? '用户已取消' : '出错了请重试'));
            }
        }

        function back() {
            whichPageState.value = PAGESTATE.normal;
        }

        function togglePassword() {
            webLog.sendImmediately('CLICK', {
                action: 'PASSWORD_VISIBLE_BUTTON',
                params: {
                    is_visible: showPassword.value ? 'TRUE' : 'FALSE',
                },
            });
            showPassword.value = !showPassword.value;
        }

        function limitNumber(e: InputEvent, type: CLEARTYPE) {
            const val = (e.target as HTMLInputElement).value.replace(/\s/g, '');
            inputs[type].value = val;
        }
        function cache(e: InputEvent | null, which: string) {
            let val;
            if (e) {
                val = ((e as InputEvent).target as HTMLInputElement).value.replace(/\s/g, '');
            }
            switch (which) {
                case 'phone':
                case 'code':
                case 'password':
                    loginInfoCache[which] = val as string;
                    break;
                case 'agreement':
                    loginInfoCache.agreement = +agreementState.value;
                    break;
                case 'way':
                    loginInfoCache.way = whichWay.value;
                    break;
            }
            update(loginInfoCache);
        }

        function changeWay() {
            if (whichWay.value === WAYS.verifycode) {
                webLog.sendImmediately('CLICK', {
                    action: 'PASSWORD_LOGIN_BUTTON',
                    params: {},
                });
                webLog.sendImmediately('PV', {
                    type: 'enter',
                    page: 'PASSWORD_LOGIN_H5',
                    params: {
                        sid,
                        sub_biz: source,
                        has_wechat: isInWeixin ? 'TRUE' : 'FALSE',
                        start_login_session_id: startLoginSessionId,
                    },
                });
                whichWay.value = WAYS.password;
            } else {
                whichWay.value = WAYS.verifycode;
                webLog.sendImmediately('CLICK', {
                    action: 'AUTH_CODE_LOGIN_BUTTON',
                    params: {},
                });
                webLog.sendImmediately('PV', {
                    type: 'enter',
                    page: 'AUTH_CODE_LOGIN_H5',
                    params: {
                        sid,
                        sub_biz: source,
                        has_wechat: isInWeixin ? 'TRUE' : 'FALSE',
                        start_login_session_id: startLoginSessionId,
                    },
                });
            }
            cache(null, 'way');
        }
        function toggleAgreement() {
            if (!agreementState.value) {
                showAgreementPopup.value = false;
            }
            agreementState.value = !agreementState.value;
            cache(null, 'agreement');
            if (!agreementState.value) {
                return;
            }
            webLog.sendImmediately('CLICK', {
                action: 'AGREE_PROTOCOL_BUTTON',
                params: {},
            });
        }
        const showForgetPwdModal = ref<Boolean>(false);
        // 忘记了密码
        function forgetPwdEvent() {
            showForgetPwdModal.value = true;
            webLog.sendImmediately('CLICK', {
                action: 'FORGET_PASSWORD_BUTTON',
                params: {},
            });
        }

        const isPhoneInputFocus = ref(false);
        const isCodeInputFocus = ref(false);
        const isPasswordInputFocus = ref(false);

        const isPhoneClearIconVisible = computed(() => {
            return isPhoneInputFocus.value && inputs.verifycodePhone.value.length;
        });
        const isCodeClearIconVisible = computed(() => {
            return isCodeInputFocus.value && inputs.verifycodeCode.value.length;
        });
        const isPasswordClearIconVisible = computed(() => {
            return isPasswordInputFocus.value && inputs.passwordPwd.value.length;
        });

        return {
            cache,
            Countdown,
            isSended,
            wxLogin,
            toggleAgreement,
            agreementState,
            inputs,
            togglePassword,
            showPassword,
            limitNumber,
            changedWayText,
            changeWay,
            WAYS,
            whichWay,
            errors,
            send,
            whichPageState,
            PAGESTATE,
            back,
            login,
            isLoginBtnBright,
            canLogin,
            showAgreementPopup,
            verifycodePhoneIsValid,
            accounts,
            chooseToLogin,
            isInWeixin,
            isSmallWebview,
            privacyPageUrl,
            servicePageUrl,
            forgetPwdEvent,
            showForgetPwdModal,
            agree() {
                agreementState.value = true;
            },

            // 清除按钮展示逻辑、清除逻辑
            isPhoneClearIconVisible,
            isCodeClearIconVisible,
            isPasswordClearIconVisible,
            handleInputBlur(e: InputEvent, which: string) {
                cache(e, which);
                switch (which) {
                    case 'phone': {
                        isPhoneInputFocus.value = false;
                        break;
                    }
                    case 'code': {
                        isCodeInputFocus.value = false;
                        break;
                    }
                    case 'password': {
                        isPasswordInputFocus.value = false;
                        break;
                    }
                }
            },
            handleInputFocus(which: string) {
                switch (which) {
                    case 'phone': {
                        isPhoneInputFocus.value = true;
                        break;
                    }
                    case 'code': {
                        isCodeInputFocus.value = true;
                        break;
                    }
                    case 'password': {
                        isPasswordInputFocus.value = true;
                        break;
                    }
                }
            },
            clear(val: CLEARTYPE) {
                inputs[val].value = '';
                console.log('clear', val);
                switch (val) {
                    case 'verifycodePhone':
                        loginInfoCache.phone = '';
                        break;
                    case 'verifycodeCode':
                        loginInfoCache.code = '';
                        break;
                    case 'passwordPwd':
                        loginInfoCache.password = '';
                        break;
                }
                update(loginInfoCache);
            },
            logoUrl,
            privacyPolicy,
            userAgreement,
        };
    },
});
</script>

<style lang="less" scoped="true">
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.common-login {
    display: flex;
    flex-direction: column;
    position: relative;
    min-height: 100vh;
    padding: 1.68rem 0 0.92rem;
    box-sizing: border-box;
    &-logo {
        width: 2.52rem;
        height: 0.95rem;
        //background-size: contain;
        //background-repeat: no-repeat;
        background: center left/contain no-repeat;
        //background-image: url(~@/assets/loginlogo.png);
        margin: 0 auto;
    }
    &-logo-default {
        width: 2.52rem;
        height: 0.95rem;
        margin: 0 auto;
        background: center left/contain no-repeat;
        background-image: url(~@/assets/loginlogo.png);
    }
    &-ways {
        margin: 0.8rem 0.7rem 0;
    }
    &-password {
        position: relative;
        &-phone {
            font-size: 0;
            position: relative;
            box-sizing: border-box;
            border-bottom: 0.02rem solid #EAEAEA;
            input {
                padding: 0.25rem 0 0.29rem;
                line-height: 0.66rem;
                font-size: 0.32rem;
                font-weight: 400;
                color: #222222;
                border: none;
                outline: none;
                width: 100%;
                &::placeholder {
                    line-height: 40px;
                    font-size: 0.32rem;
                    font-weight: 400;
                    color: #c6c6c6;
                    overflow: visible;
                }

            }
        }
        &-clear {
            width: 0.32rem;
            height: 0.32rem;
            background-image: url(~@/assets/login-clear.png);
            background-repeat: no-repeat;
            background-size: contain;
            position: absolute;
            top: 0.4rem;
            right: 0;
        }
        &-error {
            display: flex;
            justify-content: flex-end;
            margin-top: 0.2rem;
            .right-forget-pwd {
                height: .34rem;
                line-height: .34rem;
                font-size: .24rem;
                font-family: PingFangSC, PingFangSC-Regular;
                font-weight: 400;
                color: #9c9c9c;
            }
        }
        &-password {
            position: relative;
            display: flex;
            justify-content: space-between;
            font-size: 0;
            box-sizing: border-box;
            border-bottom: 0.02rem solid #EAEAEA;
            input {
                padding: 0.25rem 0 0.29rem;
                line-height: 0.66rem;
                font-size: 0.32rem;
                font-weight: 400;
                color: #222222;
                border: none;
                outline: none;
                width: 100%;
                &::placeholder {
                    line-height: 40px;
                    font-size: 0.32rem;
                    font-weight: 400;
                    color: #c6c6c6;
                    overflow: visible;
                }
            }
            .common-login-password-clear {
                right: -0.8rem;
            }
            .common-login-password-pwdclear {
                right: 0.4rem;
                top: 0.45rem;
            }
        }
        &-box {
            width: 100%;
            position: relative;
        }
        &-toggle-hide {
            width: 0.4rem;
            height: 0.4rem;
            background-image: url(~@/assets/toggle.png);
            background-size: 100% 200%;
            background-repeat: no-repeat;
            margin-top: 0.4rem;
        }
        &-toggle-show {
            background-position: 0 100%;
        }
    }
    &-verifycode {
        position: relative;
        &-phone {
            font-size: 0;
            position: relative;
            box-sizing: border-box;
            border-bottom: 0.02rem solid #EAEAEA;
            input {
                padding: 0.25rem 0 0.29rem;
                line-height: 0.66rem;
                font-size: 0.32rem;
                font-weight: 400;
                color: #222222;
                border: none;
                outline: none;
                width: 100%;
                &::placeholder {
                    line-height: 40px;
                    font-size: 0.32rem;
                    font-weight: 400;
                    color: #c6c6c6;
                    overflow: visible;
                }

            }
        }
        &-clear {
            width: 0.32rem;
            height: 0.32rem;
            background-image: url(~@/assets/login-clear.png);
            background-repeat: no-repeat;
            background-size: contain;
            position: absolute;
            top: 0.4rem;
            right: 0;
        }
        &-error {
            padding: 0 0.38rem 0;
            line-height: 0.4rem;
            font-size: 0.32rem;
            font-weight: 400;
            color: #ff3a30;
            position: absolute;
            bottom: -0.52rem;
        }
        &-code {
            position: relative;
            display: flex;
            justify-content: space-between;
            font-size: 0;
            box-sizing: border-box;
            border-bottom: 0.02rem solid #EAEAEA;
            input {
                padding: 0.25rem 0 0.29rem;
                line-height: 0.66rem;
                font-size: 0.32rem;
                font-weight: 400;
                color: #222222;
                border: none;
                outline: none;
                width: 100%;
                &::placeholder {
                    line-height: 40px;
                    font-size: 0.32rem;
                    font-weight: 400;
                    color: #c6c6c6;
                    overflow: visible;
                }
            }
            .common-login-verifycode-clear {
                right: 0.4rem;
            }
        }
        &-box {
            flex: 1;
            position: relative;
        }
        &-send {
            padding: 0.37rem 0 0.43rem;
            line-height: 0.4rem;
            font-size: 0.32rem;
            font-weight: 400;
            color: #c6c6c6;
        }
        &-can-send {
            color: #FE3666;
        }
        &-timeout {
            padding: 0.37rem 0 0.43rem;
            line-height: 0.4rem;
            font-size: 0.32rem;
            font-weight: 400;
            color: #c6c6c6;
        }
    }
    &-agreement {
        display: flex;
        font-size: 0;
        justify-content: center;
        position: relative;
        margin: 0.81rem auto 0;
        &-icon {
            margin-right: 0.12rem;
            width: 0.34rem;
            height: 0.34rem;
            background-image: url(~@/assets/agreement.png);
            background-repeat: no-repeat;
            background-size: 100% 200%;
        }
        &-checked {
            background-position: 0 100%;
        }
        &-text {
            line-height: 0.34rem;
            font-size: 0.24rem;
            font-weight: 400;
            color: #9c9c9c;
            b {
                font-weight: 400;
            }
            span a {
                margin: 0 0.08rem;
                color: #385080;
            }
        }
    }
    &-btns {
        &-login,
        &-logging,
        &-changed-way {
            height: 1.12rem;
            line-height: 1.22rem;
            opacity: 0.5;
            background: #FE3666;
            border-radius: 0.56rem;
            font-size: 0.32rem;
            font-weight: 500;
            text-align: center;
            color: #ffffff;
            margin-top: 0.4rem;
        }
        &-login-bright,
        &-logging,
        &-changed-way {
            opacity: 1;
        }
        &-logging {
            display: flex;
            justify-content: center;
            align-items: center;
            &-inner {
                animation: 1s linear 0s infinite rotate;
                width: 0.34rem;
                height: 0.34rem;
                margin: 0;
                border-radius: 50%;
                border: 0.04rem solid #ffffff;
                border-right-color: transparent;
                transform: rotate(-45deg);
                box-sizing: border-box;
            }
        }
        &-changed-way {
            color: #222222;
            border: 1px solid #EAEAEA;
            background: transparent;
        }
    }
    &-error {
        border-bottom-color: #ff3a30;
    }
    &-wx {
        flex-grow: 1;
        justify-items: flex-end;
        align-items: center;

        display: flex;
        flex-direction: column;
        justify-content: flex-end;

        margin-top: 0.4rem;
        &-other-way {
            width: 0.88rem;
            height: 0.88rem;
            background-size: contain;
            background-repeat: no-repeat;
            background-image: url(~@/assets/wx.png);
        }
        &-tips {
            line-height: 0.4rem;
            font-size: 0.32rem;
            font-weight: 400;
            text-align: left;
            color: #c6c6c6;
            margin-bottom: 0.4rem;
        }
    }
}

input{
    background: transparent;
}
</style>
