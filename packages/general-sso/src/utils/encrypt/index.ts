import { genEncryptCommonParams, EncryptHeaders } from './shared';
import { encryptor as encryptorRSA } from './encryptor-rsa';
import { encryptor as encryptorGM } from './encryptor-gm';
import { getConfig } from '@/core/configInit';
import { ENCRYPT_STRATEGY } from '@/config';

export function genEncryptReqParams(params: {
    account?: string;
    phone?: string;
    email?: string;
    password?: string;
    password2?: string;
}) {
    const encryptor = getConfig().encryptStrategy === ENCRYPT_STRATEGY.GM ? encryptorGM : encryptorRSA;

    const { options: encryptOptions, headers: encryptHeaders } = genEncryptCommonParams(encryptor.keyVersion);
    let reqParams: Record<string, string | boolean | EncryptHeaders> = {
        encryptHeaders,
    };
    if (params.password) {
        const encryptedPwd = encryptor.encrypt(params.password, {
            ...encryptOptions,
            isMix: true,
        });
        reqParams = {
            ...reqParams,
            password: encryptedPwd.encryptText,
            ignorePwd: encryptedPwd.ignoreEncrypt,
        };
    }
    if (params.password2) {
        const encryptedPwd = encryptor.encrypt(params.password2, {
            ...encryptOptions,
            isMix: true,
        });
        reqParams = {
            ...reqParams,
            password2: encryptedPwd.encryptText,
        };
    }
    if (params.account) {
        const encryptedAccount = encryptor.encrypt(params.account, encryptOptions);
        reqParams = {
            ...reqParams,
            account: encryptedAccount.encryptText,
            ignoreAccount: encryptedAccount.ignoreEncrypt,
        };
    }
    if (params.phone) {
        const encryptedPhone = encryptor.encrypt(params.phone, encryptOptions);
        reqParams = {
            ...reqParams,
            phone: encryptedPhone.encryptText,
            ignoreAccount: encryptedPhone.ignoreEncrypt,
        };
    }
    if (params.email) {
        const encryptedEmail = encryptor.encrypt(params.email, encryptOptions);
        reqParams = {
            ...reqParams,
            email: encryptedEmail.encryptText,
            ignoreAccount: encryptedEmail.ignoreEncrypt,
        };
    }

    return reqParams;
}

export function signReqParams(reqParams: any) {
    if (getConfig().encryptStrategy === ENCRYPT_STRATEGY.GM) {
        return encryptorGM.sign(reqParams);
    }

    return reqParams;
}
