<script lang="ts">
    import { getContext, setContext, onMount } from "svelte";

    import ComponentStage1 from '$lib/features/stage1/kuaishou/IndexWrapper.svelte';
    import BusinessLoginForm from "$lib/features/stage1/business/SubAccountIndex.svelte";
    import ComponentStage2 from '$lib/features/stage2/index.svelte';
    import ComponentStage3 from '$lib/features/stage3/index.svelte';
    import LoadingComponent from "$lib/features/components/LoadingComponent.svelte";
    import { writable } from "svelte/store";
    import type { ActionStartDimension } from "$lib/core/radar";
    import { getConfigPageFromServer } from "$lib/services";
    import { EnumErrorType } from "$lib/shared/const";
    import { sendEvent } from "$lib/core/radar";

    let myElement;

    export let env: 'staging | production';
    export let baseUrl: string;
    export let sid: string;
    export let themeColor;
    export let borderRadiusConfig;
    export let isMainAccountPage: boolean;

    export let onSuccess: (res: any) => void;
    export let onFail: (error: any) => void;
    export let onLoginStart: (res: ActionStartDimension) => void;
    export let loginPageConfig: object;
    const configState = writable(loginPageConfig);
    setContext('themeColor', themeColor);
    setContext('env', env);
    setContext('onSuccess', onSuccess);
    setContext('onLoginStart', onLoginStart);
    setContext('onFail', onFail);
    setContext('loginPageConfig', configState);
    setContext('sid', sid);
    setContext('isCAloneAuthPage', false);
    setContext('isMainAccountPage', isMainAccountPage);
    // 获取服务端配置
    let promise = getConfigPageFromServer(baseUrl, {
        sid,
    }).then(async (res: any) => {
        console.log('then', res);
        configState.set({
            ...loginPageConfig,
            ...res,
        });
        return res;
    }).catch(error => {
        // 前端配置兜底
        console.log('error', error);
        onFail({
            loginType: EnumErrorType.GetConfigError,
            error,
        });
        configState.set({
            ...loginPageConfig,
        });
    });
    function getCurrentHeight() {
        if (myElement) {
            const rect = myElement.getBoundingClientRect();
            return rect.height;
        }
        return -1;
    }
    setContext('getCurrentHeight', getCurrentHeight);

    onMount(() => {
        sendEvent({
            name: 'login-component-v2 init',
        });
    })
</script>

{#await promise}
    <LoadingComponent />
{:then res}
    <div class="login-component-v2-container"
         style="--colorC: {themeColor.colorC}; --colorB: {themeColor.colorB}; --containerRadius: {borderRadiusConfig.containerRadius}px; --buttonRadius: {borderRadiusConfig.buttonRadius}px; --inputRadius: {borderRadiusConfig.inputRadius}px; --toolTipRadius: {borderRadiusConfig.toolTipRadius}px; "
         bind:this={myElement}
         on:click={getCurrentHeight}
    >
        {#if !isMainAccountPage}
            <BusinessLoginForm />
        {:else}
            {#if $configState.currentMainState === 1}
                <ComponentStage1 />
            {:else if $configState.currentMainState === 2}
                <ComponentStage2 />
            {:else if $configState.currentMainState === 3}
                <ComponentStage3 />
            {:else}
                <p>currentMainState: { $configState.currentMainState }</p>
            {/if}
        {/if}
    </div>
{:catch error}
    <p>error: {error}</p>
{/await}

<style>
.login-component-v2-container {
    border-radius: var(--containerRadius);
    -webkit-font-smoothing: antialiased;
    background: white;
    color: #000000;
    position: relative;
}
</style>
