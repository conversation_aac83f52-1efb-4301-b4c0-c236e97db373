{"private": true, "scripts": {"prepare": "husky install", "build": "turbo build --filter='./packages/*'", "dev": "turbo dev --no-cache --continue", "lint": "turbo lint", "clean": "turbo clean && rm -rf node_modules", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "changeset": "changeset", "version-packages": "changeset version", "release": "turbo build --filter='./packages/*' && changeset publish", "build:account-zt-pc": "turbo build --filter='account-zt-pc'"}, "devDependencies": {"@changesets/cli": "^2.22.0", "@commitlint/cli": "^17.6.5", "@commitlint/config-conventional": "^17.6.5", "eslint": "^7.32.0", "husky": "^8.0.3", "prettier": "^2.5.1", "turbo": "^1.10.16"}}