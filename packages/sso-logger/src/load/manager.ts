/**
 * @file 异步模块加载管理器
 */
import { asyncLoad } from './async-load';
// 异步加载器： 异步加载模块
export default class AsyncLoadManager<Base, Remote extends Base = Base> {
    private readonly baseModule!: Base;
    private module!: Base | Remote;
    constructor(baseModule: Base) {
        this.baseModule = baseModule;
    }
    private asyncLoad(url: string): Promise<Remote> { // 异步加载
        return asyncLoad(url) as Promise<Remote>;
    }
    async getModule(remoteSrc: string) {
        if (this.module) {
            return this.module;
        }
        try {
            return this.module = await this.asyncLoad(remoteSrc);
        } catch(error) {
            return this.module = this.baseModule;
        }
    }
}
