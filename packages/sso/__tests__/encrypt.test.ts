import { encryptorCreator } from '../src/encrypt/index';

const publicKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjWfCb9IR5pMheXLUHCQko8VddGGDZ2jN0Edj/yQoXl91plE6r/muh1oKeuWtSpDwqDDAO5s3lHYfPFb45eWTky0a4ijOBlGbls5WJBQqoqD3gYTPcZyc1KFmn9wRTbNLMFhWN2kCHzo6YOO9kRcCQdAuXaj2sxFrirdglL8v7I0gp0n2ME+3V4Jwiv86cL24t6DfzxHqW/CO7Q/7P6bE5xVZHkuup7J1vXrjewN0r9nXovmahYlLIop4QuWC6zDVHDSTk/SXifHJBidOgEWHKgQSC5FS3xism5bth8XKWu4WX/z2pND4vA4STNE9LwULQPX2MJFjqUdYG7fBePZnIwIDAQAB';

test('generate encryptor content', () => {
    const encryptor = encryptorCreator(publicKey);
    const rawText = 'This is test content!';
    const encryptContent = encryptor.encrypt(rawText, {
        timestamp: 1640921575146,
        nonce: 'abcdefghij',
        isMix: true,
    });
    console.log('encryptContent is:', encryptContent);
    expect(encryptContent.encryptText).not.toBe('');
});
