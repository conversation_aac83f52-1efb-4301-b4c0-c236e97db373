<template>
    <div class="qr-wrapper">
        <identity-qrcode
            :env="env"
            :kpn="kpn"
            :sid="sid"
            :url="url"
            :type="type"
            :show-close-icon="showCloseIcon"
            @sendMsg="sendMsg"
        ></identity-qrcode>
    </div>
</template>

<script lang="ts">
import { defineComponent, onMounted } from '@vue/composition-api';
import { parseUrl } from '@/common/js/util';
import IdentityQrcode from './identity-qrcode.vue';
import { sendRadarFmp } from '@/common/js/log';
import { IS_PROD } from '@/common/js/const';
import { formatParams } from '@/utils/url';
import { ENUM_TYPE_MAP } from '@/config/identity-qrcode';

export default defineComponent({
    name: 'identity-qrcode-page',
    components: {
        IdentityQrcode,
    },
    setup() {
        const params = parseUrl(location.href);
        console.log(params);
        const {
            env = IS_PROD ? 'production' : 'staging',
            // kpn = 'KUAISHOU',
            sid = 'kuaishou.web.api',
            url,
            type = 'identity-verify-face',
            displayType = '',
        } = params.paramObj;

        const { query } = formatParams(url);
        console.log('query', query);

        console.log('result type', ENUM_TYPE_MAP[type]);

        const showCloseIcon = displayType === 'popup';

        function sendMsg(data: {result: number;type?: number | string;token?: string}) {
            const msg = { ...data, type: data?.type || ENUM_TYPE_MAP[type] };
            window.parent && window.parent.postMessage(JSON.stringify({ msgType: 'RESULT', msg }), '*');
        }

        onMounted(() => {
            sendRadarFmp();
        });

        return {
            params,
            env,
            kpn: query?.kpn || 'KUAISHOU',
            sid,
            url,
            type,
            showCloseIcon,
            sendMsg,
        };
    },
});
</script>
<style lang="stylus" scoped>
.qr-wrapper
    width 100vw
</style>
