import axios from 'axios';
import { getConfig, Config } from '@/env';
import { TIMEOUT, AjaxResult } from "@/service";

const config = getConfig();

/**
 * 实名相关接口
 */
const accountClient = axios.create({
    baseURL: `${config.API.ACCOUNT_HOST}/rest/infra/id/card/qrToken`,
    timeout: TIMEOUT,
    withCredentials: true,
    headers: {
        // 'trace-context': JSON.stringify({ laneId: 'STAGING.across_device_qr_face_verify' }),
    },
})

function setAccountHost(host: string) {
    console.info('set api account host', host);
    accountClient.defaults.baseURL = `${host}/rest/infra/id/card/qrToken`;
}

export function setHostFromConfig(config: Config) {
    setAccountHost(config.API.ACCOUNT_HOST);
}

type RealNameResult = AjaxResult<{
    url: string;
    error_msg?: string
}>

/**
 * 获取实名信息
 * @param bizName
 */
export async function getRealNameCheckURL(params: { bizNameForIdCard: string;authType: number}): Promise<RealNameResult> {
    console.log('document.cookie', document.cookie.match(/token=[^;]+/));
    return accountClient.post<RealNameResult>(
        '/url/gen',
        null,
        {
            params,
            headers: {
                'content-type': 'application/x-www-form-urlencoded',
            },
            withCredentials: true,
        }
    ).then(res => res.data)
}
