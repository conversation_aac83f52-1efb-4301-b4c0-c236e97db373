// vite.config.ts
import { defineConfig } from "file:///Users/<USER>/code/account-fe-collective/node_modules/.pnpm/vite@5.4.10_@types+node@18.16.18_stylus@0.63.0/node_modules/vite/dist/node/index.js";
import { svelte } from "file:///Users/<USER>/code/account-fe-collective/node_modules/.pnpm/@sveltejs+vite-plugin-svelte@3.1.2_svelte@4.2.19_vite@5.4.10/node_modules/@sveltejs/vite-plugin-svelte/src/index.js";
import dts from "file:///Users/<USER>/code/account-fe-collective/node_modules/.pnpm/vite-plugin-dts@3.9.1_@types+node@18.16.18_typescript@5.6.3_vite@5.4.10/node_modules/vite-plugin-dts/dist/index.mjs";
import { libInjectCss } from "file:///Users/<USER>/code/account-fe-collective/node_modules/.pnpm/vite-plugin-lib-inject-css@2.1.1_vite@5.4.10/node_modules/vite-plugin-lib-inject-css/dist/index.js";
import { resolve } from "path";

// package.json
var package_default = {
  name: "@mfe/b-h5-login-component",
  version: "0.0.1",
  type: "module",
  main: "dist/index.cjs",
  module: "dist/index.js",
  types: "dist/index.d.ts",
  scripts: {
    dev: "vite --host",
    build: "vite build",
    "build:dev": "vite build --watch",
    preview: "vite preview",
    check: "svelte-check --tsconfig ./tsconfig.json",
    publish: "pnpm run build && pnpm publish --tag beta"
  },
  files: [
    "dist",
    "README.md",
    "CHANGELOG.md",
    "package.json"
  ],
  devDependencies: {
    "@sveltejs/vite-plugin-svelte": "^3.0.2",
    "@tsconfig/svelte": "^5.0.2",
    "@types/qs": "^6.9.7",
    autoprefixer: "^10.4.16",
    postcss: "^8.4.39",
    stylus: "^0.63.0",
    svelte: "^4.2.12",
    "svelte-check": "^3.6.7",
    "tailwind-merge": "^1.13.1",
    "@floating-ui/dom": "^1.6.7",
    tailwindcss: "^3.4.6",
    tslib: "^2.6.2",
    typescript: "^5.2.2",
    vite: "^5.2.0",
    "vite-plugin-dts": "^3.9.1",
    "vite-plugin-lib-inject-css": "^2.1.1",
    "@ks/general-sso": "workspace:*",
    "@ks/sso": "workspace:*"
  },
  repository: "https://git.corp.kuaishou.com/mfe/tp/user-center/account-zt/account-fe-collective",
  dependencies: {
    "@ks-radar/radar-core": "^1.2.4",
    "@ks-radar/radar-event-collect": "^1.2.4",
    "@ks/weblogger": "^3.0.17",
    "@svelteuidev/core": "^0.14.0",
    qs: "^6.11.2"
  },
  peerDependencies: {
    "@ks/general-sso": "workspace:*",
    "@ks/sso": "workspace:*"
  }
};

// vite.config.ts
var __vite_injected_original_dirname = "/Users/<USER>/code/account-fe-collective/packages/b-h5-login-component";
var vite_config_default = defineConfig({
  plugins: [
    svelte(),
    libInjectCss(),
    dts({ include: ["lib"] })
  ],
  define: {
    __PACKAGE_VERSION__: JSON.stringify(package_default.version)
  },
  resolve: {
    alias: {
      "$lib": resolve(__vite_injected_original_dirname, "lib")
    }
  },
  build: {
    copyPublicDir: false,
    lib: {
      entry: resolve(__vite_injected_original_dirname, "lib/index.ts"),
      formats: ["es"],
      fileName: "[name]"
    },
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    rollupOptions: {
      external: ["tailwindcss", "@ks/general-sso", "@ks/sso"]
    },
    minify: true
    // sourcemap: true,
  },
  server: {
    port: 5e3
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcudHMiLCAicGFja2FnZS5qc29uIl0sCiAgInNvdXJjZXNDb250ZW50IjogWyJjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZGlybmFtZSA9IFwiL1VzZXJzL2h1aS9jb2RlL2FjY291bnQtZmUtY29sbGVjdGl2ZS9wYWNrYWdlcy9iLWg1LWxvZ2luLWNvbXBvbmVudFwiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiL1VzZXJzL2h1aS9jb2RlL2FjY291bnQtZmUtY29sbGVjdGl2ZS9wYWNrYWdlcy9iLWg1LWxvZ2luLWNvbXBvbmVudC92aXRlLmNvbmZpZy50c1wiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9pbXBvcnRfbWV0YV91cmwgPSBcImZpbGU6Ly8vVXNlcnMvaHVpL2NvZGUvYWNjb3VudC1mZS1jb2xsZWN0aXZlL3BhY2thZ2VzL2ItaDUtbG9naW4tY29tcG9uZW50L3ZpdGUuY29uZmlnLnRzXCI7aW1wb3J0IHsgZGVmaW5lQ29uZmlnIH0gZnJvbSAndml0ZSdcbmltcG9ydCB7IHN2ZWx0ZSB9IGZyb20gJ0BzdmVsdGVqcy92aXRlLXBsdWdpbi1zdmVsdGUnXG5pbXBvcnQgZHRzIGZyb20gJ3ZpdGUtcGx1Z2luLWR0cydcbmltcG9ydCB7IGxpYkluamVjdENzcyB9IGZyb20gJ3ZpdGUtcGx1Z2luLWxpYi1pbmplY3QtY3NzJ1xuaW1wb3J0IHsgcmVzb2x2ZSB9IGZyb20gJ3BhdGgnXG5pbXBvcnQgcGFja2FnZUpzb24gZnJvbSBcIi4vcGFja2FnZS5qc29uXCI7XG5cbi8vIGh0dHBzOi8vdml0ZWpzLmRldi9jb25maWcvXG5leHBvcnQgZGVmYXVsdCBkZWZpbmVDb25maWcoe1xuICAgIHBsdWdpbnM6IFtcbiAgICAgICAgc3ZlbHRlKCksXG4gICAgICAgIGxpYkluamVjdENzcygpLFxuICAgICAgICBkdHMoeyBpbmNsdWRlOiBbJ2xpYiddIH0pXG4gICAgXSxcbiAgICBkZWZpbmU6IHtcbiAgICAgICAgX19QQUNLQUdFX1ZFUlNJT05fXzogSlNPTi5zdHJpbmdpZnkocGFja2FnZUpzb24udmVyc2lvbiksXG4gICAgfSxcbiAgICByZXNvbHZlOiB7XG4gICAgICAgIGFsaWFzOiB7XG4gICAgICAgICAgICAnJGxpYic6IHJlc29sdmUoX19kaXJuYW1lLCAnbGliJylcbiAgICAgICAgfVxuICAgIH0sXG4gICAgYnVpbGQ6IHtcbiAgICAgICAgY29weVB1YmxpY0RpcjogZmFsc2UsXG4gICAgICAgIGxpYjoge1xuICAgICAgICAgICAgZW50cnk6IHJlc29sdmUoX19kaXJuYW1lLCAnbGliL2luZGV4LnRzJyksXG4gICAgICAgICAgICBmb3JtYXRzOiBbJ2VzJ10sXG4gICAgICAgICAgICBmaWxlTmFtZTogJ1tuYW1lXSdcbiAgICAgICAgfSxcbiAgICAgICAgdGVyc2VyT3B0aW9uczoge1xuICAgICAgICAgICAgY29tcHJlc3M6IHtcbiAgICAgICAgICAgICAgICBkcm9wX2NvbnNvbGU6IHRydWUsXG4gICAgICAgICAgICAgICAgZHJvcF9kZWJ1Z2dlcjogdHJ1ZSxcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgcm9sbHVwT3B0aW9uczoge1xuICAgICAgICAgICAgZXh0ZXJuYWw6IFtcInRhaWx3aW5kY3NzXCIsIFwiQGtzL2dlbmVyYWwtc3NvXCIsIFwiQGtzL3Nzb1wiXSxcbiAgICAgICAgfSxcbiAgICAgICAgbWluaWZ5OiB0cnVlLFxuICAgICAgICAvLyBzb3VyY2VtYXA6IHRydWUsXG4gICAgfSxcbiAgICBzZXJ2ZXI6IHtcbiAgICAgICAgcG9ydDogNTAwMCxcbiAgICB9XG59KVxuIiwgIntcbiAgICBcIm5hbWVcIjogXCJAbWZlL2ItaDUtbG9naW4tY29tcG9uZW50XCIsXG4gICAgXCJ2ZXJzaW9uXCI6IFwiMC4wLjFcIixcbiAgICBcInR5cGVcIjogXCJtb2R1bGVcIixcbiAgICBcIm1haW5cIjogXCJkaXN0L2luZGV4LmNqc1wiLFxuICAgIFwibW9kdWxlXCI6IFwiZGlzdC9pbmRleC5qc1wiLFxuICAgIFwidHlwZXNcIjogXCJkaXN0L2luZGV4LmQudHNcIixcbiAgICBcInNjcmlwdHNcIjoge1xuICAgICAgICBcImRldlwiOiBcInZpdGUgLS1ob3N0XCIsXG4gICAgICAgIFwiYnVpbGRcIjogXCJ2aXRlIGJ1aWxkXCIsXG4gICAgICAgIFwiYnVpbGQ6ZGV2XCI6IFwidml0ZSBidWlsZCAtLXdhdGNoXCIsXG4gICAgICAgIFwicHJldmlld1wiOiBcInZpdGUgcHJldmlld1wiLFxuICAgICAgICBcImNoZWNrXCI6IFwic3ZlbHRlLWNoZWNrIC0tdHNjb25maWcgLi90c2NvbmZpZy5qc29uXCIsXG4gICAgICAgIFwicHVibGlzaFwiOiBcInBucG0gcnVuIGJ1aWxkICYmIHBucG0gcHVibGlzaCAtLXRhZyBiZXRhXCJcbiAgICB9LFxuICAgIFwiZmlsZXNcIjogW1xuICAgICAgICBcImRpc3RcIixcbiAgICAgICAgXCJSRUFETUUubWRcIixcbiAgICAgICAgXCJDSEFOR0VMT0cubWRcIixcbiAgICAgICAgXCJwYWNrYWdlLmpzb25cIlxuICAgIF0sXG4gICAgXCJkZXZEZXBlbmRlbmNpZXNcIjoge1xuICAgICAgICBcIkBzdmVsdGVqcy92aXRlLXBsdWdpbi1zdmVsdGVcIjogXCJeMy4wLjJcIixcbiAgICAgICAgXCJAdHNjb25maWcvc3ZlbHRlXCI6IFwiXjUuMC4yXCIsXG4gICAgICAgIFwiQHR5cGVzL3FzXCI6IFwiXjYuOS43XCIsXG4gICAgICAgIFwiYXV0b3ByZWZpeGVyXCI6IFwiXjEwLjQuMTZcIixcbiAgICAgICAgXCJwb3N0Y3NzXCI6IFwiXjguNC4zOVwiLFxuICAgICAgICBcInN0eWx1c1wiOiBcIl4wLjYzLjBcIixcbiAgICAgICAgXCJzdmVsdGVcIjogXCJeNC4yLjEyXCIsXG4gICAgICAgIFwic3ZlbHRlLWNoZWNrXCI6IFwiXjMuNi43XCIsXG4gICAgICAgIFwidGFpbHdpbmQtbWVyZ2VcIjogXCJeMS4xMy4xXCIsXG4gICAgICAgIFwiQGZsb2F0aW5nLXVpL2RvbVwiOiBcIl4xLjYuN1wiLFxuICAgICAgICBcInRhaWx3aW5kY3NzXCI6IFwiXjMuNC42XCIsXG4gICAgICAgIFwidHNsaWJcIjogXCJeMi42LjJcIixcbiAgICAgICAgXCJ0eXBlc2NyaXB0XCI6IFwiXjUuMi4yXCIsXG4gICAgICAgIFwidml0ZVwiOiBcIl41LjIuMFwiLFxuICAgICAgICBcInZpdGUtcGx1Z2luLWR0c1wiOiBcIl4zLjkuMVwiLFxuICAgICAgICBcInZpdGUtcGx1Z2luLWxpYi1pbmplY3QtY3NzXCI6IFwiXjIuMS4xXCIsXG4gICAgICAgIFwiQGtzL2dlbmVyYWwtc3NvXCI6IFwid29ya3NwYWNlOipcIixcbiAgICAgICAgXCJAa3Mvc3NvXCI6IFwid29ya3NwYWNlOipcIlxuICAgIH0sXG4gICAgXCJyZXBvc2l0b3J5XCI6IFwiaHR0cHM6Ly9naXQuY29ycC5rdWFpc2hvdS5jb20vbWZlL3RwL3VzZXItY2VudGVyL2FjY291bnQtenQvYWNjb3VudC1mZS1jb2xsZWN0aXZlXCIsXG4gICAgXCJkZXBlbmRlbmNpZXNcIjoge1xuICAgICAgICBcIkBrcy1yYWRhci9yYWRhci1jb3JlXCI6IFwiXjEuMi40XCIsXG4gICAgICAgIFwiQGtzLXJhZGFyL3JhZGFyLWV2ZW50LWNvbGxlY3RcIjogXCJeMS4yLjRcIixcbiAgICAgICAgXCJAa3Mvd2VibG9nZ2VyXCI6IFwiXjMuMC4xN1wiLFxuICAgICAgICBcIkBzdmVsdGV1aWRldi9jb3JlXCI6IFwiXjAuMTQuMFwiLFxuICAgICAgICBcInFzXCI6IFwiXjYuMTEuMlwiXG4gICAgfSxcbiAgICBcInBlZXJEZXBlbmRlbmNpZXNcIjoge1xuICAgICAgICBcIkBrcy9nZW5lcmFsLXNzb1wiOiBcIndvcmtzcGFjZToqXCIsXG4gICAgICAgIFwiQGtzL3Nzb1wiOiBcIndvcmtzcGFjZToqXCJcbiAgICB9XG59XG4iXSwKICAibWFwcGluZ3MiOiAiO0FBQTJYLFNBQVMsb0JBQW9CO0FBQ3haLFNBQVMsY0FBYztBQUN2QixPQUFPLFNBQVM7QUFDaEIsU0FBUyxvQkFBb0I7QUFDN0IsU0FBUyxlQUFlOzs7QUNKeEI7QUFBQSxFQUNJLE1BQVE7QUFBQSxFQUNSLFNBQVc7QUFBQSxFQUNYLE1BQVE7QUFBQSxFQUNSLE1BQVE7QUFBQSxFQUNSLFFBQVU7QUFBQSxFQUNWLE9BQVM7QUFBQSxFQUNULFNBQVc7QUFBQSxJQUNQLEtBQU87QUFBQSxJQUNQLE9BQVM7QUFBQSxJQUNULGFBQWE7QUFBQSxJQUNiLFNBQVc7QUFBQSxJQUNYLE9BQVM7QUFBQSxJQUNULFNBQVc7QUFBQSxFQUNmO0FBQUEsRUFDQSxPQUFTO0FBQUEsSUFDTDtBQUFBLElBQ0E7QUFBQSxJQUNBO0FBQUEsSUFDQTtBQUFBLEVBQ0o7QUFBQSxFQUNBLGlCQUFtQjtBQUFBLElBQ2YsZ0NBQWdDO0FBQUEsSUFDaEMsb0JBQW9CO0FBQUEsSUFDcEIsYUFBYTtBQUFBLElBQ2IsY0FBZ0I7QUFBQSxJQUNoQixTQUFXO0FBQUEsSUFDWCxRQUFVO0FBQUEsSUFDVixRQUFVO0FBQUEsSUFDVixnQkFBZ0I7QUFBQSxJQUNoQixrQkFBa0I7QUFBQSxJQUNsQixvQkFBb0I7QUFBQSxJQUNwQixhQUFlO0FBQUEsSUFDZixPQUFTO0FBQUEsSUFDVCxZQUFjO0FBQUEsSUFDZCxNQUFRO0FBQUEsSUFDUixtQkFBbUI7QUFBQSxJQUNuQiw4QkFBOEI7QUFBQSxJQUM5QixtQkFBbUI7QUFBQSxJQUNuQixXQUFXO0FBQUEsRUFDZjtBQUFBLEVBQ0EsWUFBYztBQUFBLEVBQ2QsY0FBZ0I7QUFBQSxJQUNaLHdCQUF3QjtBQUFBLElBQ3hCLGlDQUFpQztBQUFBLElBQ2pDLGlCQUFpQjtBQUFBLElBQ2pCLHFCQUFxQjtBQUFBLElBQ3JCLElBQU07QUFBQSxFQUNWO0FBQUEsRUFDQSxrQkFBb0I7QUFBQSxJQUNoQixtQkFBbUI7QUFBQSxJQUNuQixXQUFXO0FBQUEsRUFDZjtBQUNKOzs7QURyREEsSUFBTSxtQ0FBbUM7QUFRekMsSUFBTyxzQkFBUSxhQUFhO0FBQUEsRUFDeEIsU0FBUztBQUFBLElBQ0wsT0FBTztBQUFBLElBQ1AsYUFBYTtBQUFBLElBQ2IsSUFBSSxFQUFFLFNBQVMsQ0FBQyxLQUFLLEVBQUUsQ0FBQztBQUFBLEVBQzVCO0FBQUEsRUFDQSxRQUFRO0FBQUEsSUFDSixxQkFBcUIsS0FBSyxVQUFVLGdCQUFZLE9BQU87QUFBQSxFQUMzRDtBQUFBLEVBQ0EsU0FBUztBQUFBLElBQ0wsT0FBTztBQUFBLE1BQ0gsUUFBUSxRQUFRLGtDQUFXLEtBQUs7QUFBQSxJQUNwQztBQUFBLEVBQ0o7QUFBQSxFQUNBLE9BQU87QUFBQSxJQUNILGVBQWU7QUFBQSxJQUNmLEtBQUs7QUFBQSxNQUNELE9BQU8sUUFBUSxrQ0FBVyxjQUFjO0FBQUEsTUFDeEMsU0FBUyxDQUFDLElBQUk7QUFBQSxNQUNkLFVBQVU7QUFBQSxJQUNkO0FBQUEsSUFDQSxlQUFlO0FBQUEsTUFDWCxVQUFVO0FBQUEsUUFDTixjQUFjO0FBQUEsUUFDZCxlQUFlO0FBQUEsTUFDbkI7QUFBQSxJQUNKO0FBQUEsSUFDQSxlQUFlO0FBQUEsTUFDWCxVQUFVLENBQUMsZUFBZSxtQkFBbUIsU0FBUztBQUFBLElBQzFEO0FBQUEsSUFDQSxRQUFRO0FBQUE7QUFBQSxFQUVaO0FBQUEsRUFDQSxRQUFRO0FBQUEsSUFDSixNQUFNO0FBQUEsRUFDVjtBQUNKLENBQUM7IiwKICAibmFtZXMiOiBbXQp9Cg==
