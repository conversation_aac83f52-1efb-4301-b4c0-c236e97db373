<template>
    <div>
        <header class="login-header">
            <div class="login-content">
                <div class="login-header-logo" :style="{ backgroundImage: `url(${logoImage})` }"></div>
            </div>
        </header>
        <section class="login-body" :style="{ backgroundImage: `url(${mainBackgroundImage})`}">
            <div class="login-content">
                <div class="login-bg"></div>
                <slot></slot>
            </div>
        </section>
        <footer class="login-footer">
            <div class="login-content">
                <KuaishouFooter></KuaishouFooter>
            </div>
        </footer>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed, watch } from '@vue/composition-api';
import useSWRV from 'swrv';
import { getLoginPageInfoFetcher } from '@/services/login';
import { parseUrl } from '@/common/js/util';
import KuaishouFooter from '@/components/kuaishou-footer.vue';


export default defineComponent({
    components: {
        KuaishouFooter,
    },
    setup(props, { emit }) {
        const query = parseUrl(location.href).paramObj;
        const querySid = query.sid || 'kuaishou.web.api';

        // 请求 sid 对应的 logo/主背景/是否显示第三方登录 等信息
        const { data } = useSWRV(querySid, getLoginPageInfoFetcher(querySid), {
            revalidateOnFocus: false,
        });

        const logoImage = computed(() => data.value?.logo?.[0] || '');
        const mainBackgroundImage = computed(() => data.value?.background?.[0] || '');

        watch(data, (val, oldVal) => {
            if (val && !oldVal) {
                emit('pageInfo', val);
            }
        });

        return {
            logoImage,
            mainBackgroundImage,
        };
    },
});
</script>

<style lang="stylus" scoped>
.login-content {
	width: 1180px;
	height: 100%;
	margin: 0 auto;
	position: relative;
}

@media (max-width:1180px) {
	.login-content {
		width: 100%;
		position: relative;
	}
}

.login-header {
	width: 100%;
	padding: 40px 0 24px;
	height: 36px
}

.login-header-logo {
	margin-left: 20px;
	background: center left/contain no-repeat;
	height: 36px;
	max-width: 300px
}

.login-body {
	height: 500px;
	min-height: 500px;
	background: top center/auto no-repeat
}

.login-main {
	position: absolute;
	right: 0;
	top: 50%;
	transform: translate(0,-50%);
	width: 380px;
	min-height: 200px;
	box-shadow: 0 8px 20px -4px rgba(74,83,93,0.12);
	background: #fff
}

@media (max-width:1180px) {
	.login-main {
		margin-right: 10px
	}
}

.login-footer {
	padding: 50px 0;
	height: 200px
}
</style>
