
/**
 * str 中间替换成 char
 * @param str          需要处理的字符串
 * @param showStartLen 开始显示字符数 默认 = 1
 * @param showEndLen   结束显示字符数 默认 = 1
 * @param char         替换的字符    默认 = *
 */
export function mask({
    str = '',
    showStartLen = 1,
    showEndLen = 1,
    char = '*',
}) {
    const maskLen = str.length - showStartLen - showEndLen;
    if (maskLen <= 0) {
        return str;
    }
    const start = str.substr(0, showStartLen);
    const end = str.substr(str.length - showEndLen, showEndLen);
    return `${start}${char.repeat(maskLen)}${end}`;
}