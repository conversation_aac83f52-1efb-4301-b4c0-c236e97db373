<script lang="ts">
    import { twMerge } from 'tailwind-merge';

    export let color: 'primary' | 'blue' | 'gray' | 'green' | 'red' | 'yellow' | 'pink' | 'purple' | 'white' | 'custom' | undefined = 'primary';
    export let bg: string = 'text-gray-300';
    export let customColor: string = '';
    export let size: string | number = '8';
    // these two props add fine control over the spinner colors
    export let currentFill: string = 'currentFill';
    export let currentColor: string = 'currentColor';

    if (currentFill !== 'currentFill') {
        color = undefined;
    }

    const fillColorClasses = {
        primary: 'fill-primary-600',
        blue: 'fill-blue-600',
        gray: 'fill-gray-600 dark:fill-gray-300',
        green: 'fill-green-500',
        red: 'fill-red-600',
        yellow: 'fill-yellow-400',
        pink: 'fill-pink-600',
        purple: 'fill-purple-600',
        white: 'fill-white',
        custom: customColor
    };

    let fillColorClass: string = color === undefined ? '' : fillColorClasses[color] ?? fillColorClasses.blue;
</script>

<svg {...$$restProps} role="status" class={twMerge('inline -mt-px animate-spin dark:text-gray-600', bg, fillColorClass, $$props.class)} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"
     width={size} height={size}
>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M7.18778 1.38285C10.8422 0.93414 14.1685 3.53291 14.6172 7.18736C15.0659 10.8418 12.4672 14.1681 8.8127 14.6168C5.15825 15.0655 1.83197 12.4667 1.38326 8.81229C1.31951 8.29309 1.31659 7.77386 1.37312 7.26232C1.41357 6.89636 1.74302 6.63247 2.10898 6.67292C2.47495 6.71336 2.73883 7.04282 2.69839 7.40878C2.65318 7.81779 2.65553 8.23335 2.70666 8.64979C3.06563 11.5734 5.72664 13.6524 8.65021 13.2934C11.5738 12.9344 13.6528 10.2734 13.2938 7.34986C12.9348 4.42629 10.2738 2.34728 7.35027 2.70625C6.98482 2.75112 6.65219 2.49124 6.60732 2.12579C6.56245 1.76035 6.82233 1.42772 7.18778 1.38285Z" fill={currentFill}/>
</svg>
