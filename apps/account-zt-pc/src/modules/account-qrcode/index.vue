<template>
    <div class="qr-wrapper">
        <account-qrcode
            :env="env"
            :biz-name="bizName"
            :kpn="kpn"
            :sid="sid"
            :show-close-icon="showCloseIcon"
            @sendMsg="sendMsg"
        ></account-qrcode>
    </div>
</template>

<script lang="ts">
import { defineComponent, onMounted } from '@vue/composition-api';
import { sendRadarFmp } from '@/common/js/log';
import AccountQrcode from './account-qrcode.vue';
import { IS_PROD } from '@/common/js/const';

declare global {
    interface Window {
        instance_03E9FEFF221F438DBBBB8BDC175A12F6: {
            submitData: (data: string) => void;
        }
    }
}

function removeTokenDomain() {
    const domainList = location.hostname.split('.');
    for (let i = 1, l = domainList.length; i < l; ++i) {
        const domain = domainList.slice(-i).join('.');
        document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 GMT; domain=' + domain + '; path=/';
    }
    document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 GMT; domain=kuaishou.com; path=/';
    document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 GMT; domain=gifshow.com; path=/';
}

removeTokenDomain();

export default defineComponent({
    components: {
        AccountQrcode,
    },
    setup(props, vm) {
        const {
            env = IS_PROD ? 'production' : 'staging',
            bizName = 'LIVE_STREAM_START',
            kpn = 'KUAISHOU',
            sid = 'kuaishou.api',
            url,
            type = '1',
            displayType = '',
        } = vm.root.$route.query;
        document.body.style.cssText = 'min-width: 120px';
        const showCloseIcon = displayType === 'popup';
        console.log('params', env, kpn, sid, bizName);

        function sendMsg(data: {result: number;type?: number | string;token?: string}) {
            const msg = { ...data, type: data?.type || +type };
            window.instance_03E9FEFF221F438DBBBB8BDC175A12F6
                    && window.instance_03E9FEFF221F438DBBBB8BDC175A12F6.submitData
                    && window.instance_03E9FEFF221F438DBBBB8BDC175A12F6.submitData(JSON.stringify(msg));
        }

        onMounted(() => {
            sendRadarFmp();
        });

        return {
            env,
            kpn,
            sid,
            url,
            bizName,
            showCloseIcon,
            sendMsg,
        };
    },
});
</script>

<style lang="stylus" scoped>
.qr-wrapper
    width 100vw
</style>
