export enum EnumInputLoginType {
    phone = 'phone',
    email = 'email',
    phoneAndEmail = 'phoneAndEmail',
}


export const isValidPhone = (phone: string): boolean => {
    const re = /^\+\d{11,13}$|^\d{8,11}$/
    return re.test(phone)
}

export const isValidEmail = (email: string): boolean => {
    const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
    return re.test(String(email).toLowerCase())
}

export const isValidSmsCode = (smsCode: string): boolean => {
    const re = /^(\d{6})$/
    return re.test(smsCode)
}

export const isValidAccount = (account: string, type: EnumInputLoginType) => {
    if (type === EnumInputLoginType.phone) {
        return isValidPhone(account);
    } else if (type === EnumInputLoginType.email) {
        return isValidEmail(account);
    } else {
        return isValidPhone(account) || isValidEmail(account);
    }
}

export function filterInput(event: Event) {
    const input = event.target;
    const value = input?.value;
    // 定义允许的字符范围
    const allowedChars = /[a-zA-Z0-9!"@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`]+$/;

    // 过滤掉非法字符
    input.value = value.replace(/[^a-zA-Z0-9!"@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`]/g, '');

    // 防止默认行为
    if (!allowedChars.test(event.key)) {
        event.preventDefault();
        return true;
    }
    return false;
}
