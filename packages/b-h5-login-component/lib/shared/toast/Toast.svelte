<script lang="ts">
    import Modal from "$lib/shared/toast/Modal.svelte";
    import { useToast } from "$lib/shared/toast/toastStore";

    const { state } = useToast();
</script>

<Modal open={$state.isOpen}>
    <div class="toast-body">
        <slot>{$state.message}</slot>
    </div>
</Modal>

<style>
    .toast-body {
        padding: 12px 20px;
        text-align: center;
        background: rgba(0,0,0, 0.7);
        border-radius: 8px;
        color: #fff;
        display: flex;
        flex-direction: column;
        align-items: center;

        color: #FFFFFF;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
        vertical-align: top;
    }
</style>
