{"name": "@mfe/login-component-v2", "version": "0.0.5-beta.14", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"dev": "vite --host", "build": "vite build", "build:dev": "vite build --watch", "preview": "vite preview", "check": "svelte-check --tsconfig ./tsconfig.json", "publish": "pnpm run build && pnpm publish --tag beta"}, "files": ["dist", "README.md", "CHANGELOG.md", "package.json"], "devDependencies": {"@sveltejs/vite-plugin-svelte": "^3.0.2", "@tsconfig/svelte": "^5.0.2", "@types/qs": "^6.9.7", "autoprefixer": "^10.4.16", "postcss": "^8.4.39", "stylus": "^0.63.0", "svelte": "^4.2.12", "svelte-check": "^3.6.7", "tailwind-merge": "^1.13.1", "@floating-ui/dom": "^1.6.7", "tailwindcss": "^3.4.6", "tslib": "^2.6.2", "typescript": "^5.2.2", "vite": "^5.2.0", "vite-plugin-dts": "^3.9.1", "vite-plugin-lib-inject-css": "^2.1.1", "@ks/general-sso": "workspace:*", "@ks/sso": "workspace:*"}, "repository": "https://git.corp.kuaishou.com/mfe/tp/user-center/account-zt/account-fe-collective", "dependencies": {"@ks-radar/radar-core": "^1.2.4", "@ks-radar/radar-event-collect": "^1.2.4", "@ks/weblogger": "^3.0.17", "qs": "^6.11.2"}, "peerDependencies": {"@ks/general-sso": "workspace:*", "@ks/sso": "workspace:*"}}