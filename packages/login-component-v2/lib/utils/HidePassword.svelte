<script lang="ts">
    import { twMerge } from 'tailwind-merge';
    import ToolbarButton from '$lib/shared/toolbar/ToolbarButton.svelte';

    export let name: string = 'See';
</script>

<ToolbarButton on:click {name} {...$$restProps} class={twMerge('ms-auto', $$props.class)} let:svgSize>
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16">
        <path fill="#8A9199" d="m12.373 11.585 1.324 1.406-.728.686-1.374-1.46A6.155 6.155 0 0 1 8 13.334c-5 0-6.667-5.333-6.667-5.333s.721-2.307 2.637-3.886L2.303 2.344l.728-.686 6.527 6.936.731.776 1.4 1.487.684.728Zm-1.472-.105-1.28-1.361a2.667 2.667 0 0 1-3.636-3.864l-1.329-1.41A6.752 6.752 0 0 0 3.58 5.95 8.691 8.691 0 0 0 2.399 8a8.69 8.69 0 0 0 1.18 2.051c.925 1.184 2.33 2.283 4.421 2.283 1.17 0 2.125-.344 2.901-.854ZM8.93 9.385 6.675 6.989A1.667 1.667 0 0 0 8.93 9.384ZM8 2.668a6.26 6.26 0 0 0-2.318.429l.749.797A5.362 5.362 0 0 1 8 3.668c2.092 0 3.496 1.1 4.42 2.282A8.69 8.69 0 0 1 13.601 8a8.69 8.69 0 0 1-1.268 2.16l.69.736C14.202 9.49 14.668 8 14.668 8S13 2.668 8 2.668Z"/>
        <path fill="#8A9199" d="M8 5.334a2.7 2.7 0 0 0-.201.008l2.842 3.027A2.667 2.667 0 0 0 8 5.334Z"/>
    </svg>
</ToolbarButton>
