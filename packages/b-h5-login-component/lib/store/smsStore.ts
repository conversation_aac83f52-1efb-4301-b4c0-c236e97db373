import { EMAIL_SOURCE, KEYCODE, COUNTRY_CODE, EnumErrorType } from '$lib/shared/const'
import { writable } from 'svelte/store'
import { isValidPhone, isValidSmsCode } from "$lib/utils/validate";
import { requestMobileCode } from "@ks/sso";

export function createSmsCodeStore() {
    let state = {
        dropdownVisible: false,
        highlightIndex: 0,

        phone: '',
        phoneInputClass: '',
        phoneErrorMsg: '',

        smsCodeErrorMsg: '',
        smsCodeInputClass: '',
        phonePlaceholder: '',
        phoneInput: undefined as any,
        countryCode: '+86',
        countryCodeList: COUNTRY_CODE.map(item => ({
            label: item.chineseName,
            value: item.countryCode,
            key: item.englishName,
        })),

        smsCode: '',
        countDown: 0,

        bottomAlertOpen: false,
        chooseUserOpen: false,
        agreementChecked: false,

        loginErrorMsg: '',
    }

    const { set, update, subscribe } = writable({
        ...state,
    })

    subscribe(value => {
        state = value
    })

    const setValues = (value: Partial<Parameters<typeof set>[0]>) => {
        set({
            ...state,
            ...value,
        });
    }

    const validatePhone = () => {
        let passed = true;
        if (!state.phone) {
            setValues({
                loginErrorMsg: '请输入手机号',
            });
            passed = false;
        } else if (!isValidPhone(state.phone) && !state.phone.startsWith('+')) {
            setValues({
                loginErrorMsg: '手机号码格式不正确',
            });
            passed = false;
        }
        // if (passed) {
        //     setValues({
        //         phoneErrorMsg: '',
        //     });
        // }
        return passed;
    };

    const validateSmsCode = () => {
        let passed = true;
        if (!state.smsCode) {
            setValues({
                loginErrorMsg: '请输入验证码',
            });
            passed = false;
        } else if (!isValidSmsCode(state.smsCode)) {
            setValues({
                loginErrorMsg: '验证码格式不正确',
            });
            passed = false;
        }
        // if (passed) {
        //     setValues({
        //         smsCodeErrorMsg: '',
        //     });
        // }
        return passed;
    }

    const reducers = {
        setVisble(value: boolean) {
            setValues({
                dropdownVisible: value,
            })
        },
        validateSend() {
            return validatePhone();
        },
        validateLogin() {
            const bool1 = validatePhone();
            const bool2 = validateSmsCode();
            return bool1 && bool2;
        },
        isButtonForbidden() {
                return !(
                    (isValidPhone(state.phone) || state.phone.startsWith('+'))
                    && isValidSmsCode(state.smsCode)
                );
        },
        async sendCodeAction(type: number) {
            if (!reducers.validateSend()) {
                return;
            }
            if (state.countDown > 0) {
                return;
            }
            try {
                await requestMobileCode({
                    phone: state.phone!,
                    countryCode: state.countryCode,
                    isLogin: true,
                    type,
                });
                setValues({
                    countDown: 60,
                });
            } catch (error) {
                throw error;
            }
        },

        filterPhone() {
            setValues({
                phone: state.phone?.replace(/[^\d]/g, ''),
            });
        },
        filterSmsCode() {
            setValues({
                smsCode: state.smsCode.replace(/[^\d]/g, ''),
            })
        },
        onPhoneBlur() {
            validatePhone();
        },
        onSmsCodeBlur() {
            let passed = true;
            if (!state.smsCode) {
                // 空状态不校验
            } else if (!isValidSmsCode(state.smsCode)) {
                setValues({
                    smsCodeErrorMsg: '验证码格式不正确',
                });
                passed = false;
            }
            if (passed) {
                setValues({
                    smsCodeErrorMsg: '',
                });
            }
            return passed;
        },
        onBottomReject() {
            setValues({
                bottomAlertOpen: false,
            });
        },
        onBottomAccept() {
            setValues({
                bottomAlertOpen: false,
            });
        },
    }
    return {
        update,
        subscribe,
        set,
        setValues,
        reducers,
    }
}
