// 缓存区域
const cbMask: HTMLDivElement = document.createElement('div');
cbMask.style.zIndex = '1234567809';
cbMask.style.position = 'fixed';

cbMask.style.opacity = '0';

// 测试:
// cbMask.style.backgroundColor = 'rgba(0, 255, 255, .5)';
// cbMask.style.top = '0';
// cbMask.style.left = '0';
// cbMask.style.width = '10em';
// cbMask.style.height = '10em';

// Range 对象
const cbRange = document.createRange();

// Selection 对象
const cbSelection = document.getSelection();

/**
 * 将文本写入系统剪贴板。
 * 受浏览器安全策略影响：只能在短期事件函数内调用: Firefox、Edge 等，无限制：IE、Chrome、UC、360、Opera 等。
 */
export async function setClipboardText(str: string): Promise<boolean> {
    if (navigator.clipboard) {
        try {
            await navigator.clipboard.writeText(str);
            return true;
            // eslint-disable-next-line no-empty
        } catch (e) {}
    }

    cbMask.innerText = str;

    document.body.appendChild(cbMask);

    cbRange.selectNodeContents(cbMask);

    if (!cbSelection) {
        return false;
    }

    cbSelection.removeAllRanges();
    cbSelection.addRange(cbRange);

    const ret = document?.execCommand('copy');
    cbMask.remove();

    return Boolean(ret);
}
