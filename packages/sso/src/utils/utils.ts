function stringifyPrimitive(v: string | boolean | number | undefined) {
    switch (typeof v) {
        case 'string':
            return v;
        case 'boolean':
            return v ? 'true' : 'false';
        case 'number':
            return isFinite(v) ? v : '';
        default:
            return '';
        }
}

export function stringify(obj: any, sep = '&', eq = '=') {
    if (obj === null) {
        obj = undefined;
    }
    if (typeof obj === 'object') {
        return Object.keys(obj).map((k) => {
            const ks = encodeURIComponent(stringifyPrimitive(k)) + eq;
            if (Array.isArray(obj[k])) {
                return obj[k].map((v: any) => {
                    return ks + encodeURIComponent(stringifyPrimitive(v));
                }).join(sep);
            }
            return ks + encodeURIComponent(stringifyPrimitive(obj[k]));
        }).join(sep);
    }
    return encodeURIComponent(stringifyPrimitive(obj)) + eq + encodeURIComponent(stringifyPrimitive(obj));
}

/**
 * 生成 callback 地址，即将 params 参数对象添加到 callback 的 query 上
 */
export function generateCallback(callback: string, params: Record<string, unknown>) {
    const [ url, hash ] = callback.split('#');
    const [ path, query ] = url.split('?');
    const loginQuery = stringify(params);
    const newQuery = query
        ? query + `&${loginQuery}`
        : loginQuery;
    const result = `${path}?${newQuery}`;
    if (hash) {
        return `${result}#${hash}`;
    }
    return result;
}

export function getSafeScriptUrl(url: string) {
    const safeHosts = [
        'ali.static.yximgs.com',
        'static.yximgs.com',
    ];
    for (let i = 0; i < safeHosts.length; i++) {
        const host = safeHosts[i];
        if (
            url.indexOf(`http://${host}/`) === 0
            || url.indexOf(`https://${host}/`) === 0
            || url.indexOf(`//${host}/`) === 0
        ) {
            return url;
        }
    }
    return '';
}

export function getDomainFormUrl(url: string) : string {
    const regx = /^(?:(?:[a-z]+)?:\/\/)?([^/]+)/
    const match = url.match(regx);
    return match ? match[1] : ''
}

export function formatParams(url: string) {
    const urlObj = new URL(url, window.location.origin); // 创建 URL 对象
    const path = urlObj.pathname; // 获取路径部分，不包含域名
    // 将查询参数转换成对象格式
    const queryParams = new URLSearchParams(urlObj.search);
    const query: Record<string, string> = {};
    queryParams.forEach((value, key) => {
        query[key] = value;
    });
    return {
        path,
        query,
    }
}
