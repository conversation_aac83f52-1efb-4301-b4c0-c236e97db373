import Vue from 'vue';
import { <PERSON><PERSON>, But<PERSON>, Dialog } from '@ks/sharp-ui';
import { setClipboardText } from '@/utils/clipboard';
import { setTopBarBackgroundColor } from '@/modules/one-id-query/common/utils';
import { jump2App } from '@/utils/jump';
import { KUAISHOU_APP_DOWNLOAD_ULINK, ONE_ID_QUERY_COMPLAINTS } from '@/common/config/links'

export default Vue.extend({
    name: 'first',
    components: {
        'sp-button': <PERSON><PERSON>,
    },
    created() {
        setTopBarBackgroundColor();
    },
    data() {
        return {
            // 是否在端内
            inApp: navigator.userAgent.match(/(KsWebView|Kwai)\/\d/i) && navigator.userAgent.match(/(yoda)\/\d/i),
        };
    },
    methods: {
        onFeedBackBtnClick() {
            location.href = ONE_ID_QUERY_COMPLAINTS;
        },
        async onOpenAppToUnbindBtnClick() {
            // 需要在端内打开的 验证页面
            const verifyUrl = `${location.origin}${
                (this as any).$router.resolve({
                    name: 'one-id-query-verify',
                }).href
            }`;

            // 跳转操作触发，退出。
            if (await jump2App(verifyUrl)) {
                return;
            }

            if (this.inApp) {
                // 已经在快手端内, 直接切换路由
                (this as any).$router.push({
                    name: 'one-id-query-verify',
                });
                return;
            }

            const dialog = Dialog.confirm({
                title: '请使用浏览器打开',
                content: `
<div style="user-select: none;">
  <div>请先安装快手 APP，再复制解绑链接，粘贴到浏览器访问：</div>
  <div style="user-select: text; line-height: 1.2; margin: .618em 0; font-weight: bold;">${location.href}</div>
</div
`,
                maskCloseable: true,
                btns: [
                    {
                        text: '复制链接',
                        async handler() {
                            // dialog.close();
                            if (await setClipboardText(location.href)) {
                                Toast.success('复制成功');
                            } else {
                                Toast.error('复制失败');
                            }
                        },
                    },
                    {
                        text: '下载快手APP',
                        async handler() {
                            // dialog.close();
                            location.href = KUAISHOU_APP_DOWNLOAD_ULINK;
                        },
                    },
                ],
            });
        },
    },
});
