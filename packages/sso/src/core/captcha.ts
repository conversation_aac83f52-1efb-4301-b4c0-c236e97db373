import { request } from '@/core/request';
import { getSafeScriptUrl } from '@/utils/utils';
import { privateGetConfig } from './configInit';
import { getBaseUrl } from '@ks-passport/url-kit';

interface CaptchaParam {
    // TODO type
    type: string;
    key: string;
    uri: string;
    device?: 'pc' | 'mobile';
}

interface TicketParam extends CaptchaParam {
    ticket: string;
}

interface VerifyResult {
    result: number;
    token: string;
}

interface CapInitResponse {
    ret: number;
    ticket: string;
}

function getCaptcha({type, key, uri, device}: CaptchaParam) {
    return request((privateGetConfig()?.baseUrl || getBaseUrl(privateGetConfig().env)) + '/rest/c/infra/captcha/get', {
        device,
        type,
        key,
        uri,
    }).then(({data}: any) => data);
}
function verifyTicket({type, key, uri, ticket}: TicketParam) {
    return request<VerifyResult>((privateGetConfig()?.baseUrl || getBaseUrl(privateGetConfig().env)) + '/rest/c/infra/captcha/verify', {
        type,
        key,
        uri,
        input: ticket,
    }).then(({token}) => token);
}

const config = {
    type: 'popup',
    themeColor: '6bd379',
    pos: 'fixed',
};

interface KwaiCaptchaConfig {
    captchaSession: string;
    configUrl: string;
    type: number;
    callback: (res: {
        result: number;
        ticket: string;
    }) => void;
}

interface KwaiCaptchaWithoutGatewayConfig {
    pageUrl: string,
    callback: (res: {
        result: number;
        ticket: string;
    }) => void;
}

// 在需要多次capInit的时候, 先capDestroy
// 这里的show方法已经默认会调用, 所以destroy一般不用手动调用了
// https://cloud.tencent.com/document/product/295/6616
declare global {
    interface Window {
        capDestroy?: () => void;
        capInit?: (...args: any) => void;
        kwaiCaptcha?: {
            capInit: (container: HTMLElement, config: KwaiCaptchaConfig | KwaiCaptchaWithoutGatewayConfig) => void;
            capDestroy: () => void;
        }
    }
}

interface ExtendedHTMLScriptElement extends HTMLScriptElement {
    readyState?: 'loaded' | 'complete';
    onreadystatechange?: (() => void) | null;
}

function destroy() {
    if (typeof window.capDestroy === 'function') {
        window.capDestroy();
    } else if (window.kwaiCaptcha) {
        window.kwaiCaptcha.capDestroy();
    }
    currentCaptchaScript?.parentNode?.removeChild(currentCaptchaScript);
    captchaContainer?.parentNode?.removeChild(captchaContainer);
    captchaMaskElement?.parentNode?.removeChild(captchaMaskElement);
}

function init({type, key, uri, device}: CaptchaParam) {
    return getCaptcha({
        type,
        key,
        uri,
        device,
    }).then(jsUrl => applyCaptcha(jsUrl));
}

let captchaContainer: HTMLDivElement | null = null;
let captchaMaskElement: HTMLDivElement | null = null;
let currentCaptchaScript: ExtendedHTMLScriptElement | null = null;

const CAPTCHA_STYLE = `
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    z-index: 99999;
`;

const CAPTCHA_MASK_STYLE = CAPTCHA_STYLE + `
    background-color: rgba(0, 0, 0, 0);
    transition: background-color .4s ease-in-out;
`;

function injectScript(jsUrl: string, style: string = CAPTCHA_MASK_STYLE) {
    return new Promise((resolve, reject) => {
        const head = document.querySelector('head') as Element;
        const script = document.createElement('script') as ExtendedHTMLScriptElement;
        script.type = 'text/javascript';
        script.src = getSafeScriptUrl(jsUrl);
        head.appendChild(script);
        // 动态生成div加遮罩
        captchaContainer = document.createElement('div');
        captchaContainer.id = 'captcha-container';
        if (style) {
            captchaContainer.style.cssText = style;
        }
        captchaMaskElement = document.createElement('div');
        captchaMaskElement.id = 'captcha-mask';
        captchaMaskElement.style.cssText = CAPTCHA_MASK_STYLE;
        document.body.appendChild(captchaMaskElement);
        document.body.appendChild(captchaContainer);
        script.onload = script.onreadystatechange = () => {
            if (!script.readyState || script.readyState === 'loaded' || script.readyState === 'complete') {
                if (captchaMaskElement) {
                    captchaMaskElement.style.backgroundColor = 'rgba(0, 0, 0, .5)';
                }
                script.onload = script.onreadystatechange = null;
                resolve(undefined);
            }
        };
        script.onerror = reject;
        currentCaptchaScript = script;
    });
}

function callTecentCaptcha(jsUrl: string) {
    return injectScript(jsUrl).then(() => {
        return new Promise<string>((resolve, reject) => {
            const capOption = {
                type: config.type,
                pos: config.pos,
                themeColor: config.themeColor,
                callback(res: CapInitResponse) {
                    // capInit的回调函数，获取ticket
                    // 用户验证成功
                    if (res.ret === 0) {
                        resolve(res.ticket);
                    } else {
                        // 用户关闭验证码页面，没有验证
                        reject(res);
                        destroy();
                    }
                },
            };
            if (window.capInit) {
                window.capInit(captchaContainer, capOption);
            } else {
                throw new Error("can not find function 'capInit'");
            }
        });
    });
}

interface KwaiCaptchaInfo {
    type: number;
    captchaSession: string;
    jsSdkUrl: string;
    url: string;
}

function callKwaiCaptcha(info: KwaiCaptchaInfo) {
    return injectScript(info.jsSdkUrl, CAPTCHA_STYLE).then(() => {
        return new Promise<string>((resolve) => {
            const capOption = {
                type: info.type,
                configUrl: info.url,
                captchaSession: info.captchaSession,
                callback(res: { result: number; ticket: string; }) {
                    // capInit的回调函数，获取ticket
                    // 用户验证成功
                    if (res.result === 1) {
                        resolve(res.ticket);
                    }
                },
            };
            if (window.kwaiCaptcha && captchaContainer) {
                window.kwaiCaptcha.capInit(captchaContainer, capOption);
            } else {
                throw new Error("can not find function 'capInit'");
            }
        });
    });
}

// 调用验证码逻辑
function applyCaptcha(jsUrl: string): Promise<string> {
    try {
        // 自研验证码逻辑
        return callKwaiCaptcha(JSON.parse(jsUrl) as KwaiCaptchaInfo);
    } catch {
        return callTecentCaptcha(jsUrl);
    }
}

export function getCaptchaToken({type, key, uri, device = 'pc'}: CaptchaParam) {
    return init({type, key, uri, device})
        .then(ticket => {
            destroy();
            return ticket
                ? verifyTicket({
                    type,
                    key,
                    uri,
                    ticket,
                })
                : '';
        });
}

export function initCaptcha (jsSdkUrl: string, url: string): Promise<string> {
    return new Promise((resolve, reject) => {
        injectScript(jsSdkUrl).then(() => {
            if (!captchaContainer) {
                throw new Error('cannot find container dom');
            }
            window.kwaiCaptcha?.capInit(captchaContainer, {
                pageUrl: url,
                callback(res) {
                    switch (res.result) {
                        case 1: // 验证成功
                            resolve(res.ticket);
                            destroy();
                            break;
                        case 0: // 验证失败（滑块位置不对），属于正常错误，保持验证码不销毁
                            break;
                        default: // 其他非正常错误
                            reject(res);
                            destroy();
                    }
                }
            });
        })
    });
}

