<template>
    <div
        :class="'login-component-page ' + version"
        ref="login-component"
    >
        <div class="wrapper">
            <div ref="refContainer" class="login-component-page-container">
            </div>
            <i
                v-if="showClose"
                id="login-component-page-close-icon"
                ref="close"
                class="close-icon"
                @click="handleClose"
            ></i>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref } from '@vue/composition-api';
import { renderComponent, RenderParam } from '@ks/login-component';
import { LoginChannelType, parseUrl, updateLoggerLoginChannel, getParentUrlObject } from '@/common/js/util';
import { hostnameWhiteList } from '@/common/js/const';
import { webLog, sendRadarFmp } from '@/common/js/log';

// 更新login用法为pc弹窗
updateLoggerLoginChannel(LoginChannelType.PC_MODAL);

// 确定父页面的域名是否在白名单内
const urlObject = getParentUrlObject();
const parentOrigin = urlObject.origin;
const parentHostname = urlObject.hostname;

const targetOrigin = (function getParentOrigin() {
    const isLegal = hostnameWhiteList.some(hostname => parentHostname.endsWith(hostname));

    return isLegal ? parentOrigin : 'www.kuaishou.com';
}());

const MSG_PREFIX = 'passport-login-iframe-msg-';
const query = parseUrl(location.href).paramObj;
const version = ['register-qrcode', 'login-password-qrcode', 'login-verify-sms-qrcode'].indexOf(query.indexPage) > -1 ? 'v2' : 'v1';

export default defineComponent({
    setup() {
        const refContainer = ref<HTMLElement>();
        onMounted(() => {
            const renderComponentParams: RenderParam = {
                env: query.env || 'production',
                sid: query.sid,
                kpn: query.kpn,
                qrType: query.qrType,
                target: refContainer.value!,
                indexPage: query.indexPage,
                baseUrl: query.baseUrl ? decodeURIComponent(query.baseUrl) : '',
                onSuccess(res: any) {
                    parent.postMessage({
                        type: MSG_PREFIX + 'success',
                        data: res,
                    }, targetOrigin);
                },
                onFail(e: any) {
                    parent.postMessage({
                        type: MSG_PREFIX + 'fail',
                        data: e,
                    }, targetOrigin);
                },
                enableSig4: true,
                logger: webLog,
                showThirdPartLogin: query.showThirdPartLogin === 'true',
                thirdPartLoginFollowUrl: query.thirdPartLoginFollowUrl,
                showRegister: query.showRegister !== 'false',
                showPlatformSwitch: query.showPlatformSwitch !== 'false',
                // 自定义反馈相关
                appealCustomTitle: query.appealCustomTitle, // 反馈btn文案
                appealCustomHref: query.appealCustomHref && decodeURIComponent(query.appealCustomHref), // 反馈btn的跳转链接
                themeColor: query.themeColor,
                serviceOwnParams: query.serviceOwnParams || '',
                handleRegisterAction: query.handleRegisterByEvent === 'true' ? () => {
                    parent.postMessage({
                        type: MSG_PREFIX + 'register',
                    }, targetOrigin);
                // eslint-disable-next-line no-undefined
                } : undefined,
            };
            renderComponent(renderComponentParams);
            sendRadarFmp();
        });

        return {
            version,
            showClose: query.showCloseIcon !== 'false',
            refContainer,
            handleClose() {
                parent && parent.postMessage({
                    type: MSG_PREFIX + 'close',
                }, targetOrigin);
            },
        };
    },
});
</script>

<style lang="stylus" scoped>
.login-component-page
    width 380px
    position relative
    margin auto
    height 100vh
    display flex
    justify-content center
    align-items center
    .v2
       width 675px
.wrapper
    position relative
.login-component-page-container
    width 380px
    box-shadow 0 8px 20px -4px rgba(0, 0, 0, .1)
    background #fff
    margin auto
.close-icon
    position absolute
    width 14px
    height 14px
    top 0
    right -24px
    z-index 22
    cursor pointer
    background url("~@/assets/icon-close.svg") no-repeat center

.v2
    .login-component-page-container
        width 675px
        border-radius 8px
    .close-icon
        top 16px
        right 16px
</style>
