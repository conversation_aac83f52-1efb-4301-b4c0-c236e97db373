import request, { AjaxResult } from './request';
import { baseUrl } from '@/common/config/base';

interface BaseParams {
    sid: string;
    countryCode: string;
    phone: string;
}

interface SendSmsCodeParams extends BaseParams {
    type: string;
    ztIdentityVerificationCheckToken?: string;
    ztIdentityVerificationType?: number;
}

/**
 *  发短信
 *  @see https://kapi.corp.kuaishou.com/project/5843/interface/api/462529
 */
export function sendSmsCode(param: SendSmsCodeParams): Promise<{ result: number; error_msg?: string }> {
    return request.post(`${baseUrl}/pass/kuaishou/sms/requestMobileCode`, param);
}

interface QueryAccountsParams extends BaseParams{
    smsCode: string
}

export interface UserInfo {
    userName: string;
    userHead: string;
}
export interface QueryAccountResult {
    accountNum: number;
    userInfos: UserInfo[];
}

/**
 *  校验短信二维码并获取用户信息
 *  @see https://kapi.corp.kuaishou.com/project/5843/interface/api/890048
 */
export function queryAccount(param: QueryAccountsParams) {
    return request.post<AjaxResult<QueryAccountResult>>(`${baseUrl}/pass/kuaishou/account/query`, param);
}