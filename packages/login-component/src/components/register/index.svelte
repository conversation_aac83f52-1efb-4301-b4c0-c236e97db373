<script>
    import PlInput from '../input/index.svelte';
    import PlButton from '../button/index.svelte';
    import globalAsyncErrorHandler from '../../common/asyncErrorHandler';
    import { getContext, createEventDispatcher, onMount, onDestroy} from 'svelte';
    import { requestMobileCode, register, getCaptchaToken } from '@ks/sso';
    import {logger, sendCommonPV, sendTaskEvent} from '../../common/logger';

    sendCommonPV('QUICK_REGISTER');

    const dispatch = createEventDispatcher();
    const isProduction = getContext('isProduction');
    const themeColor = getContext('themeColor');

    export let qrloginVersion;
    let registerInfo = {
        countryCode: isProduction ? '+86' : '+1264',
        mobile: '',
        verification: '',
        password: '',
    };
    let errorMsg = ''
    function setErrorMsg(val) {
        errorMsg = val;
    }
    let step = 'verification';
    let countdown = 0;
    let isMobileInputHovered = false;
    let showPassword = false;

    $: phoneValidate = isProduction ? /^1\d{10}$/.test(registerInfo.mobile) : /^1?\d{10}$/.test(registerInfo.mobile);
    $: verificationValidate =
        /^\d{4,}$/.test(registerInfo.verification) &&
        registerInfo.password.length >= 8 &&
        registerInfo.password.length <= 20;
    $: canToNextStep = phoneValidate && verificationValidate;

    $: if (countdown > 0) {
        setTimeout(() => countdown--, 1000);
    }

    function clearRegisterInfo() {
        registerInfo = {
            mobile: '',
            verification: '',
            password: '',
        };
        step = 'verification';
        countdown = 0;
        isMobileInputHovered = false;
        showPassword = false;
    }
    function togglePassword() {
        showPassword = !showPassword;
    }

    async function getVerification() {
        logger.sendImmediately(
            'CLICK',
            {
                action: 'GET_IDENTITY_CODE',
            }
        )
        if (!registerInfo.mobile || !phoneValidate) {
            errorMsg = '请输入有效的手机号'
            return;
        }

        if (countdown > 0) {
            return;
        }

        try {
            setErrorMsg('');
            await requestMobileCode({
                countryCode: registerInfo.mobile.length === 10 ? '+1264' : '+86',
                phone: registerInfo.mobile,
            });
            countdown = 60;
        } catch (e) {
            setErrorMsg(e.error_msg);
        }
    }
    async function submit() {
        const beginTime = Date.now()
        sendTaskEvent({
            action: 'QUICK_REGISTER_BUTTON',
            type: "USER_OPERATION",
            status: "START",
        })
        if (!registerInfo.mobile || !phoneValidate) {
            errorMsg = '请输入有效的手机号';
            return;
        }
        if (!registerInfo.verification) {
            errorMsg = '请输入验证码';
            return;
        }
        if (!verificationValidate) {
            errorMsg = '请输入有效的验证码';
            return;
        }
        if (!canToNextStep) {
            return;
        }


        try {
            setErrorMsg('');
            const result = await register({
                countryCode: registerInfo.mobile.length === 10 ? '+1264' : '+86',
                phone: registerInfo.mobile,
                password: registerInfo.password,
                smsCode: registerInfo.verification,
            });
            sendTaskEvent({
                action: 'QUICK_REGISTER_BUTTON',
                type: "USER_OPERATION",
                status: 'SUCCESS',
                params: {
                    result_code: 1,
                    cost_time: Date.now() - beginTime,
                }
            })
            dispatch('register-success', result);
        } catch (e) {
            sendTaskEvent({
                action: 'QUICK_REGISTER_BUTTON',
                type: "USER_OPERATION",
                status: 'FAIL',
                params: {
                    result_code: e.result,
                    cost_time: Date.now() - beginTime,
                }
            })
            if (!e.captchaKey) {
                setErrorMsg(e.error_msg);
                throw e;
            }
            const {captchaKey, captchaType, captchaUri} = e;
            try {
                const captchaToken = await getCaptchaToken({
                    key: captchaKey,
                    type: captchaType,
                    uri: captchaUri,
                });
                const result = await register({
                    countryCode: registerInfo.mobile.length === 10 ? '+1264' : '+86',
                    phone: registerInfo.mobile,
                    password: registerInfo.password,
                    smsCode: registerInfo.verification,
                    captchaToken,
                });
                dispatch('register-success', result);
            } catch (error) {
                // 因关闭图形验证码 而导致获取captchaToken失败异常情况吞掉-KSLIVEWEB-1392
                // 其他情况仍抛出
                // TODO 需要对error重新包装
                if (error.ret !== 2) {
                    setErrorMsg(error.error_msg);
                }
                setErrorMsg(error.error_msg);
            }
        }
    }

    /**
     * 发送协议点击日志
     * @param {'user_service' | 'privacy_product' }type
     */
    function sencClickLicense(type) {
        return () => {
            logger.sendImmediately(
                'CLICK',
                {
                    action: 'PROTOCOL_BUTTON',
                    params: {
                        type,
                    }
                }
            )
        }
    }
    function onClickLogin() {
        logger.sendImmediately(
            'CLICK',
            {
                action: 'QUICK_LOGIN'
            }
        )
        dispatch('change-type', 'login')
    }
    onMount(() => {
        logger.sendImmediately(
            'SHOW',
            {
                action: 'PROTOCOL_BUTTON',
                params: {
                    type: "user_service",
                }
            }
        )
        logger.sendImmediately(
            'SHOW',
            {
                action: 'PROTOCOL_BUTTON',
                params: {
                    type: "privacy_product",
                }
            }
        )
    })

</script>

<div class="register">
    <p class="change-type">
        已有账号？
        <span on:click={onClickLogin} class="{themeColor}">立即登录</span>
    </p>
    <h4 class="register-title">立即注册</h4>
    <div class="register-content">
        <PlInput bind:value={registerInfo.mobile} maxlength="11" placeholder="输入中国大陆手机号" className="large" />
        <div class="divider" />
        <PlInput
            className="large"
            bind:value={registerInfo.password}
            maxlength="20"
            placeholder="请设置 8~20 位密码"
            type={showPassword ? 'text' : 'password'}>
            <span slot="append" class="toggle-password" class:password-show={showPassword} on:click={togglePassword} />
        </PlInput>
        <div class="divider" />
        <PlInput className="large" bind:value={registerInfo.verification} maxlength="6" placeholder="请输入验证码">
            <span slot="append" class="get-verification {themeColor}" class:disabled={countdown} on:click={getVerification}>
                {countdown || '获取验证码'}
            </span>
        </PlInput>
        <div class="error-msg {themeColor}">{errorMsg}</div>
    </div>

    <PlButton
        disabled={!canToNextStep}
        type="primary"
        size="large"
        radius={qrloginVersion !== 'v2'}
        className={qrloginVersion === 'v2' ? 'pl-btn-large-padding' : 'next-step-button'}
        on:click={submit}>
        立即注册
    </PlButton>
    <p class="register-license">
        注册即代表同意
        <a
            href="https://www.kuaishou.com/about/policy?tab=protocol"
            target="_blank"
            rel="noopener"
            on:click={sencClickLicense('user_service')}
        >用户协议</a>
        和
        <a
            href="https://www.kuaishou.com/about/policy?tab=privacy"
            target="_blank"
            rel="noopener"
            on:click={sencClickLicense('privacy_product')}
        >隐私政策</a>
    </p>
</div>
<style type="text/stylus">
@import '../../common/variables.styl'
.register
    position relative
    &-title
        font-size 18px
        line-height 28px
        margin 0
    &-license
        margin-top 20px
        text-align center
        color font-color-license
        font-size 12px
        > a
            color link-color-license
            text-decoration none;
    &-content
        padding 30px 0 20px
.next-step-button
    display block
    box-sizing border-box
    padding 13px 0
    width 100%
    font-size 18px
// TODO后面换一下
span.toggle-password
    display inline-block
    margin-right 9px
    width 20px
    height 13px
    background url(https://static.yximgs.com/s1/i/eye-close.e184b3b.svg) no-repeat
    cursor pointer
    &.password-show
        margin-top 2px
        background url(https://static.yximgs.com/s1/i/eye-open.a9e7ca2.svg) no-repeat

.verification-input
.password-input
    margin-top 20px
.change-type
    position absolute
    top 0
    right 0
    z-index 1
    margin 0
    line-height 28px
    font-size 14px
    span
        cursor pointer
        color brand-primary
        &.blue
            color brand-primary-blue

// TODO这货后面其实可以抽象一下
.get-verification
    color brand-primary
    cursor pointer
    margin-right 10px
    font-size 15px
    display inline-block
    &.disabled
        opacity .5
        cursor not-allowed
    &.blue
        color brand-primary-blue
.divider
    margin-top 10px
.error-msg
    font-size 14px
    color font-color-error
    margin-top 25px
    height 20px
    &.blue
        color font-color-error-blue
</style>
