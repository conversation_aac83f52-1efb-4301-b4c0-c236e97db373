import { RouteConfig } from 'vue-router';
import QueryAccountByPhone from './v1-query-account/query-account-by-phone.vue';
import OneIdQueryV2First from './v2-first/first.vue';
import OneIdQueryV2Verify from './v2-verify/verify.vue';
import OneIdQueryV2Result from './v2-result/result.vue';
import OneIdQueryV3Index from './v3-index/index.vue';
import OneIdQueryV3Rebind from './v3-rebind/rebind.vue';
import OneIdQueryV3RebindAccounts from './v3-rebind/rebind-multi-accounts.vue';
import OneIdQueryV3RebindNewPhone from './v3-rebind/rebind-new-phone.vue';
import OneIdQueryV3Unregister from './v3-unregister/unregister.vue';
import OneIdQueryV3UnregisterAccounts from './v3-unregister/unregister-multi-accounts.vue';

const routes: RouteConfig[] = [
    // 工信部：一键通查页面
    {
        path: '/query-account-by-phone',
        name: 'queryAccountByPhone',
        component: QueryAccountByPhone,
        meta: {
            title: '快手',
        },
    },
    // 工信部: 一证通查 2.0 页面（端外->端内）
    {
        path: '/one-id-query/first',
        name: 'one-id-query-first',
        component: OneIdQueryV2First,
        meta: {
            title: '手机号码解绑',
        },
    },
     {
        path: '/one-id-query/verify',
        name: 'one-id-query-verify',
        component: OneIdQueryV2Verify,
        meta: {
            title: '手机号码解绑',
        },
    },
    {
        path: '/one-id-query/result',
        name: 'one-id-query-result',
        component: OneIdQueryV2Result,
        meta: {
            title: '手机号码解绑',
        },
    },
    // 工信部: 一证通查 3.0 页面（端外）
    {
        path: '/one-id-query/v3',
        name: 'one-id-query-v3',
        component: OneIdQueryV3Index,
        meta: {
            title: '账号管理',
        },
    },
    {
        path: '/one-id-query/v3/rebind',
        name: 'one-id-query-v3-rebind',
        component: OneIdQueryV3Rebind,
        meta: {
            title: '手机号码换绑',
        },
    },
    {
        path: '/one-id-query/v3/rebind/multi-accounts',
        name: 'one-id-query-v3-rebind-multi-accounts',
        component: OneIdQueryV3RebindAccounts,
        meta: {
            title: '手机号码换绑',
        },
    },
    {
        path: '/one-id-query/v3/rebind/new-phone',
        name: 'one-id-query-v3-rebind-new-phone',
        component: OneIdQueryV3RebindNewPhone,
        meta: {
            title: '手机号码换绑',
        },
    },
    {
        path: '/one-id-query/v3/unregister',
        name: 'one-id-query-v3-unregister',
        component: OneIdQueryV3Unregister,
        meta: {
            title: '账号注销',
        },
    },
    {
        path: '/one-id-query/v3/unregister/multi-accounts',
        name: 'one-id-query-v3-unregister-multi-accounts',
        component: OneIdQueryV3UnregisterAccounts,
        meta: {
            title: '账号注销',
        },
    },
];

export default routes;
