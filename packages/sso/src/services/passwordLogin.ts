
import { privateGetConfig } from '../core/configInit';
import { genEncryptReqParams } from '../utils/encrypt';
import { getLoginToken } from '../core/loginToken';
import { AuthTokenResult, BaseRes, QuickLoginResult } from '../types/services';
import { DEFAULT_COUNTRY_CODE } from '../config/constants';
import { request } from '@/core/request';
import { wrapLoginRequest } from '../core/bidLoginAdapter';

/**
 * 密码登录
 */
export interface PhonePasswordLogin {
    password: string;
    captchaToken?: string | number;
    phone: string;
    countryCode: string;
}

export interface AccountPasswordLogin {
    password: string;
    captchaToken?: string | number;
    account: string;
}

/**
 * 密码登录
 */
export function passwordLogin({account, password, captchaToken}: AccountPasswordLogin): Promise<AuthTokenResult | QuickLoginResult>;
export function passwordLogin({phone, countryCode, password, captchaToken}: PhonePasswordLogin): Promise<AuthTokenResult | QuickLoginResult>;
export function passwordLogin({phone = '', account = '', countryCode = DEFAULT_COUNTRY_CODE, password, captchaToken}: any): Promise<AuthTokenResult | QuickLoginResult> {
    const config = privateGetConfig();
    const encryptReqParams = genEncryptReqParams({
        account,
        phone,
        password,
    });
    const params = {
        sid: config.sid,
        captchaToken,
        ...encryptReqParams,
    };
    if (account) { // 手机号/邮箱 通用
        const r = getLoginToken('/pass/kuaishou/email/ks/loginAccount/v2', params)
        return wrapLoginRequest(r);
    } else { // 国家码+手机号
        const r = getLoginToken('/pass/kuaishou/login/phone/v2', {
            ...params,
            countryCode,
        });
        return wrapLoginRequest(r);
    }
}

/**
 * 密码强度校验
 */
export interface checkPassStrengthOptions {
    password: string;
}

export function checkPassStrength({password}: checkPassStrengthOptions) {
    const config = privateGetConfig();
    const encryptReqParams = genEncryptReqParams({
        password,
    });
    return request<BaseRes>(config.baseUrl + '/pass/kuaishou/pwd/checkPwd/v2', {
        sid: config.sid,
        ...encryptReqParams,
    });
}