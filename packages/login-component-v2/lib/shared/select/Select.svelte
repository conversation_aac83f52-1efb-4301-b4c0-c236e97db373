<script lang="ts">
    import { twMerge } from 'tailwind-merge';
    import IconDownArrow from "$lib/utils/IconDownArrow.svelte";

    export let value = ''
    export let options: any[] = []
    export let arrowVisible = true
    export let dropdownVisible=false;
    export let highlightIndex = 0;

    export let handleKeydown: (e: Event) => void;
    export let changeHighlight: (index: number) => void;
    export let onSelect: ( value: string | number ) => void;
    export let onSelectWrapper: (value: string, index: number) => void;
    export let setVisble: (value: boolean) => void;
    const onClick = () => {
        setVisble(!dropdownVisible);
    }

    let reference: HTMLElement

    const handleDocumentClick = ( event: any ) => {
        const targetEl = event.target

        if (!reference || reference.contains(targetEl)) {
            return
        }

        setVisble(false);
    }
    let spanClass = '';
    console.log('$$props.class', $$props.class);
    $: if ($$props.class?.includes('input-error')) {
        spanClass = 'component-selector-content input-error';
    } else {
        spanClass = 'component-selector-content';
    }
</script>

<svelte:body on:click={handleDocumentClick}/>

<div class={twMerge('component-select', $$props.class)} on:click={onClick}>
    <div bind:this={reference} class="component-selector flex"
         class:input-error={$$props.class?.includes('input-error')}
    >
        <input on:keydown={handleKeydown}
               class="component-selector-input"
        />
        <span class={spanClass}>{value}</span>
        {#if arrowVisible}
            <IconDownArrow />
        {/if}
    </div>
</div>

<style>
    .component-select {
        position: relative;
    }
    .component-selector {
        /*border-left: 1px solid #EAEAEA;*/
        /*border-top: 1px solid #EAEAEA;*/
        /*border-bottom: 1px solid #EAEAEA;*/
    }
    .component-selector:hover {
        border-color: #4A84F7;
    }
    .component-selector:focus {
        border-color: #1D59F2;
        box-shadow: 0 0 0 2px #0075ff33;
    }

    .component-select .component-selector-input {
        position: absolute;
        opacity: 0;
        width: 100%;
        height: 100%;
        margin: 0;
        z-index: 100;
        color: transparent;
        cursor: pointer;
    }

    .component-select .component-selector-content {
        display: inline-block;
        padding-left: 12px;
        padding-top: 12px;
        padding-bottom: 12px;
        line-height: 22px;
        font-size: 14px;
    }

    .component-select .component-selector img {
        width: 16px;
        height: 16px;
        vertical-align: middle;
    }

    .component-select-items {
        position: absolute;
        top: 40px;
        z-index: 10;
    }
    .input-error {
        /*background: #FFF2EC !important;*/
    }
</style>
