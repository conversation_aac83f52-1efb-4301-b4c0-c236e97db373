import { request } from '@/core/request';
import { getConfig } from '@/core/configInit';
import { DEFAULT_COUNTRY_CODE } from '@/config';
import { identityVerificationInterceptor } from '@/core/verificationInterceptor';
import { BaseRes } from '../types';

/**
 * 检查密码强度
 */
export interface checkPassStrengthData {
    password: string;
}
export function checkPassStrength({ password }: checkPassStrengthData) {
    const config = getConfig();
    return request<BaseRes>(`/pass/${config.appName}/web/password/checkPassword`, {
        sid: config.sid,
        password,
    });
}

/**
 * 检查手机号是否可用
 */
export interface checkPhoneAvailabilityData {
    phone: string;
    countryCode?: string;
}
export function checkPhoneAvailability({ phone, countryCode = DEFAULT_COUNTRY_CODE }: checkPhoneAvailabilityData) {
    const config = getConfig();
    return identityVerificationInterceptor(`/pass/${config.appName}/web/account/checkMobileExist`, {
        sid: config.sid,
        phone,
        countryCode,
    });
}

/**
 * 检查邮箱是否可用
 */
export interface checkEmailAvailabilityData {
    email: string;
}
export function checkEmailAvailability({ email }: checkEmailAvailabilityData) {
    const config = getConfig();
    return identityVerificationInterceptor(`/pass/${config.appName}/web/account/checkEmailExist`, {
        sid: config.sid,
        email,
    });
}
