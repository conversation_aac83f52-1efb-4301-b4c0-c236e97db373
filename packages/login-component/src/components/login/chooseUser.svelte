<script>
    import { chooseUser } from '@ks/sso';
    import PlButton from '../button/index.svelte';
    import {createEventDispatcher} from 'svelte';

    const dispatch = createEventDispatcher();
    export let multiUserToken;
    export let userInfos;
    export let phone;
    export let loginType;

    let currentUser;
    let errorMsg = '';
    function setErrorMsg(msg) {
        errorMsg = msg;
    }
    async function submit() {
        try {
            const result = await chooseUser({
                userId: currentUser,
                multiUserToken: multiUserToken,
                phone: phone,
            });
            dispatch('login-success', {
                loginType,
                result,
            });
        } catch (e) {
            dispatch('login-fail', e);
            setErrorMsg(e.error_msg ?? '');
        }
    }
</script>

<div class="choose-user">
    <h4 class="choose-user-title">选择要登录的账号</h4>
    <ul class="choose-user-list">
        {#each userInfos as user}
            <li
                class="choose-user-list-item"
                class:selected={user.userId === currentUser}
                key={user.userId}
                on:click={() => (currentUser = user.userId)}>
                <img src={user.headUrl} alt="avatar" />
                <p class="choose-user-list-item-detail">{user.name}</p>
                {#if user.userId === currentUser}
                    <div class="choose-user-list-item-checked"></div>
                {/if}
            </li>
        {/each}
    </ul>
    <div class="error-msg">{errorMsg}</div>
    <PlButton disabled={!currentUser} type="primary" size="large" on:click={submit} radius>确定</PlButton>
</div>

<style type="text/stylus">
@import '../../common/variables.styl'

.choose-user
    padding-top 5px
    &-title
        font-size 18px
    &-list
        margin-top 24px
        height 190px
        overflow-y scroll
        padding-inline-start 0
        &-item
            box-sizing border-box
            display flex
            align-items center
            margin-bottom 8px
            height 62px
            padding 8px
            cursor pointer
            &.selected
                background-color item-selected-active
            &:last-child
                margin-bottom 0
            &-detail
                box-sizing border-box
                text-ellipsis(206px)
                width 206px
                padding-left 12px
                font-size 14px
            &-checked
                margin-left 12px
                width 16px
                height 16px
                background-image: url(https://static.yximgs.com/udata/pkg/fe/user-selected.png)
                background-position center
                background-size contain
                background-repeat no-repeat
            img
                width 46px
                height 46px
                border-radius 50%
.error-msg
    font-size 14px
    padding-left 5px
    height 20px
    margin-top -20px
    margin-bottom 20px
    color font-color-error
</style>
