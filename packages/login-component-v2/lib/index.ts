import './styles/main.css';
import { init as ssoInit, cancelQrLogin as cancelQrLoginC } from '@ks/sso';
import type { InitParam } from '@ks/general-sso';
import { init as generalInit, cancelQrLogin as cancelQrLoginB } from "@ks/general-sso";
import type { ActionStartDimension } from "$lib/core/radar";
import { initRadar, sendActionEnd, sendActionStart } from "$lib/core/radar";
import LoginComponent from './features/LoginContainer.svelte';
import { EnumComponentType, EnumErrorType, EnumLoginType } from "$lib/shared/const";
import { getServiceOwnParams } from "$lib/utils/env";
import { setBusinessTab, setBusinessSubTab, setKuaishouTab } from "$lib/store/tabStore";


export { EnumLoginType } from "$lib/shared/const";

export type CommonPageConfigType = {
    loginMainMethod: Array<{
        type: 'password' | 'code' | 'scan';
        title?: string;
        tabTip?: string;
    }>;
    mainTitle?: string;
    mainTitleTooltip?: string;
    qrCodeToolTip?: string;
    pcToolTip?: string;
    passwordLoginTypes: Array<'phone'|'email'>;
    viceOperateConfig?: {
        leftSection: Array<{
            type?: string;
            onClick?: Function;
            rawHTML?: string;
        }>;
        rightSection: Array<{
            type?: string;
            onClick?: Function;
            rawHTML?: string;
        }>
    };
    otherOperateConfig?: {
        title: string;
        onClick?: Function;
    };
    agreements: Array<{
        title: string;
        url: string;
        onClick?: Function;
    }>;
    inputClass?: string;
    tabsClass?: string;
    scanConfig?: {
        appName?: string;
        rawDescHTML?: string;
        [key: string]: any;
    };
};

export type LoginPageConfigType = {
    currentMainState: number;
    errorMsgClass?: string;
    iconStyle?: 0 | 1;
    cAccountConfig?: CommonPageConfigType;
    bAccountConfig?: CommonPageConfigType & {
        qrType?: string;
        oAuthTypes?: Record<string, any>;
        baseAgreementOnClick?: Function;
    };
    bSubAccountConfig?: CommonPageConfigType & {
        qrType?: string;
        baseAgreementOnClick?: Function;
    };
}

export type InitComponentConfigType = Omit<InitParam, "appName"> & {
    target: HTMLElement;
    env: 'staging' | 'production';
    isMainAccountPage: boolean;
    onLoginStart?: (name: EnumLoginType) => void;
    onSuccess?: (res: {
        loginType: EnumLoginType;
        result: object;
    }) => void;
    onFail?: (res: {
        loginType: EnumLoginType | EnumErrorType;
        error: object;
    }) => void;
    // 主题色
    themeColor?: {
        colorB: string;
        colorC: string;
    };
    enableSig4?: boolean;
    borderRadiusConfig?: {
        containerRadius: number;
        inputRadius: number;
        buttonRadius: number;
        toolTipRadius: number;
    }
    loginPageConfig: LoginPageConfigType;
}

function initPublicMethods(component: LoginComponent, config: InitComponentConfigType) {
    component.reRender = (customConfig?: InitComponentConfigType) => {
        cancelQrLoginC();
        cancelQrLoginB();
        component.$destroy();
        config.target.innerHTML = '';
        if (customConfig) {
            renderComponent(customConfig);
        } else {
            renderComponent(config);
        }
    }
    component.setTabIndex = (type: EnumComponentType, activeTabIndex: number) => {
        if (type === EnumComponentType.BMain) {
            setBusinessTab(activeTabIndex)
        } else if (type === EnumComponentType.BSub) {
            setBusinessSubTab(activeTabIndex)
        } else if (type === EnumComponentType.C) {
            setKuaishouTab(activeTabIndex)
        }
    }
}

export function renderComponent(config: InitComponentConfigType) {
    let qrType = '';
    if (config.isMainAccountPage) {
        qrType = config.loginPageConfig?.bAccountConfig?.qrType || 'bid-main';
    } else {
        qrType = config.loginPageConfig?.bSubAccountConfig?.qrType || 'bid-sub';
    }
    generalInit({
        appName: 'bid',
        qrType,
        kpn: 'BID',
        // serviceOwnParams 不用传
        ...config,
    });
    ssoInit({
        ...config,
        kuaishouAuth: true,
        // qrType: 'oauth-web',
        // kpn: 'BID',
        serviceOwnParams: getServiceOwnParams(config.env),
    });
    initRadar(config.sid);
    try {
        console.log('renderComponent', config.isMainAccountPage);
        const component = new LoginComponent({
            target: config.target,
            props: {
                env: config.env || 'production',
                onSuccess: (o: { loginType: EnumLoginType; result: object }) => {
                    sendActionEnd({
                        name: o?.loginType,
                    });
                    if (config.onSuccess) {
                        config.onSuccess(o);
                    }
                },
                onFail: (o: { loginType: EnumLoginType | EnumErrorType; error: object }) => {
                    if (config.onFail) {
                        config.onFail(o);
                    }
                },
                onLoginStart: (o: ActionStartDimension) => {
                    sendActionStart(o);
                    if (config.onLoginStart) {
                        config.onLoginStart(o.name as EnumLoginType);
                    }
                },
                isMainAccountPage: config.isMainAccountPage,
                handleRegisterAction: config.handleRegisterAction,
                sid: config.sid,
                baseUrl: config.baseUrl,
                themeColor: config.themeColor || {
                    colorB: '#1D59F2',
                    colorC: '#FF4906',
                },
                borderRadiusConfig: config.borderRadiusConfig || {
                    // 外部圆角
                    containerRadius: 2,
                    // 文本框圆角
                    inputRadius: 1,
                    // 按钮圆角
                    buttonRadius: 1,
                    // hover圆角
                    toolTipRadius: 1,
                },
                loginPageConfig: config.loginPageConfig,
            },
        });
        initPublicMethods(component, config);
        // window.component = component;
        return component;
    } catch (e) {
        console.log('renderComponent error', e);
        throw e;
    }
}
