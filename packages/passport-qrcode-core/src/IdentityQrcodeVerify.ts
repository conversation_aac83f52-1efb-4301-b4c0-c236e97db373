import QrcodeVerify, { QrcodeVerifyResult } from "@/QrcodeVerify";
import { envType, getConfig } from './env';

enum IDENTITY_QR_TYPES {
    'identity-verify-face' = 1,
    'cross-device-verify' = 1,
    'identity-verify-realname' = 9,
}

const availableTypes = Object.values(IDENTITY_QR_TYPES).filter((x: string | number) => typeof x === 'number');

/**
 * 验证相关二维码验证流程
 */
export class IdentityQrcodeVerify extends QrcodeVerify {
    bizName!: string;

    constructor(props: {
        qrType?: string;
        type: string;
        kpn: string;
        sid: string;
        url: string;
        env: envType;
    }) {
        const {
            url,
            type,
            qrType = 'identity-type',
            ...other
        } = props;
        const ACCOUNT_QRCODE_HOST = getConfig(props.env).API.ACCOUNT_QRCODE_HOST;
        // TODO: 不要直接改动 path，账号后端线上有检验
        const qrLandingURL = `${ACCOUNT_QRCODE_HOST}/unified/qrcode/index.html?idCardVerifyStartUrl=${encodeURIComponent(url)}&__f__=identityQrcode`
        const qrTypeCheck = IDENTITY_QR_TYPES[type as unknown as IDENTITY_QR_TYPES];
        if (!qrTypeCheck) {
            throw TypeError(`type = ${type} not support. use ${availableTypes.join(',')}`)
        }
        console.log(qrTypeCheck, url, qrLandingURL);
        super({
            ...other,
            qrType,
            serviceOwnParams: qrLandingURL,
        });
    }

    start(url?: string): Promise<{
        result: number;
        errorMsg: string;
        qrLoginSignature: string;
        serviceOwnToken: string;
        serviceOwnType: number;
        qrAuthToken: string;
    }> {
        if (url) {
            this.serviceOwnParams = url;
        }
        return super.start()
    }
}
