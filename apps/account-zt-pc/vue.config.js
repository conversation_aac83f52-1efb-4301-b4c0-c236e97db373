const RuntimePublicPathPlugin = require('webpack-runtime-public-path-plugin');
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

const publicPath = process.env.NODE_ENV === 'production'
    ? '//var----h5----var.kskwai.com/kos/nlav12555/'
    : '/';

/**
 *
 * @param time 毫秒级时间戳
 * @return {string}
 */
function timeStamp2String(time) {
    const ret = new Date(time).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
        weekday: 'short',
    }).replace(/\//g, '-');
    return ret.slice(0, 10) + ' ' + ret.slice(10);
}

module.exports = {
    publicPath,
    devServer: {
        disableHostCheck: true,
        proxy: {
            // 实名认证二维码
            /**
             * 实名: 二维码API
             * @module account-pc-qrcode
             */
            '/rest/infra/id/card/qrToken/': {
                target: 'https://zt-idcardweb.staging.kuaishou.com',
                // target: 'https://app.m.kuaishou.com',
                changeOrigin: true,
                cookieDomainRewrite: {
                    '*': '',
                },
                followRedirects: true,
                logLevel: 'debug',
            },

            /**
             * 账号: 二维码API
             * @module account-pc-qrcode
             */
            '/rest/c/infra/ks/new/qr/': {
                target: 'https://ksid-staging.corp.kuaishou.com',
                // target: 'https://id.kuaishou.com',
                changeOrigin: true,
                cookieDomainRewrite: {
                    '*': '',
                },
                followRedirects: true,
                logLevel: 'debug',
            },

            '/pass/kuaishou': {
                target: 'https://ksid-staging.corp.kuaishou.com',
                changeOrigin: true,
            },
        },
    },
    chainWebpack(config) {

        config.plugin('html').tap(args => {
            args[0].title = '快手，记录世界 记录你';

            args[0].gitCommitLog = (() => {
                const ret = String(process.env.gitCommitLog).split(' ');
                ret.push(timeStamp2String(Number(ret.pop()) * 1000));
                return ret.join(' ');
            })();

            // 在 index.html 里存下格式类似 "2022-11-04周五 16:20:55" 编译日期时间。
            args[0].buildingDateTime = timeStamp2String(Date.now());

            return args;
        });

        config.when(process.env.NODE_ENV === 'production',
            config => config.plugin('RuntimePublicPathPlugin').use(RuntimePublicPathPlugin, [{
                runtimePublicPath: 'cdn_public_path + \'/kos/nlav12555/\'',
            }]),
        );

        config.optimization.splitChunks({
            /**
             * @see https://webpack.docschina.org/plugins/split-chunks-plugin/#splitchunksmaxinitialrequests
             */
            cacheGroups: {
                vendors: {
                    name: 'chunk-vendors',
                    test(module) {
                        return module.resource
                            && module.resource.includes('/node_modules/');
                    },
                    priority: -10,
                    chunks: 'initial',
                },
                weblogger: {
                    name: 'weblogger',
                    minChunks: 1,
                    test(module) {
                        return module.resource
                            && module.resource.includes('@ks/weblogger');
                    },
                    priority: -9,
                    chunks: 'initial',
                },
            },
        });

        // 可视化依赖分析
        // config.plugin('bundle-analyzer').use(BundleAnalyzerPlugin, [
        //     {
        //         generateStatsFile: true,
        //     },
        // ]);

        return config;
    },
};
