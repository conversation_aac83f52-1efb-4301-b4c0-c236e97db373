import {onDestroy, onMount} from "svelte";

/**
 * 非线上打日志用
 * @param args
 */
function log(...args) {
    if (process.env.NODE_ENV !== 'production') {
        console.info('login-component:logger', ...args);
    }
}

/**
 * 精简后的webLogger
 * @typedef {Object} WebLogBasic
 * @property WebLogBasic.sendImmediately
 * @property WebLogBasic.updateBase
 * @property WebLogBasic.generateLog
 * @property {{proto: 'v2' | 'v3'}} WebLogBasic.logConfig
 */

/**
 * 模拟用的logger
 * 非线上环境会打 info 日志
 * @type WebLogBasic
 */
export const mockLogger = {
    sendImmediately(type, options) {
        log('immediate', type, options);
    },
    updateBase(option) {
        log('updataBase', option);
    },
}

/**
 * 对外导出的logger
 * @type WebLogBasic
 */
export let logger = mockLogger;

/**
 * 设置logger 的方法
 * @param {WebLogBasic} logger
 */
export function setLogger(newLogger) {
    logger = newLogger;
}

/**
 * 给logger增加风控plugin 的方法
 * @param config
 */
export function addRiskPlugin(config) {
    if (logger === mockLogger) {
        return;
    }
    try {
        const RiskPlugin = require('@ks/weblogger/lib/plugins/riskMgt');
        logger.addPluginInstance(new RiskPlugin({
            env: config.env || 'production',
            bussType: config.sid,
            taskType: '8', // 登录
            subTaskType: '8-1',	// 登录
        }));
    } catch (e) {
        console.error('风控插件加载失败');
        console.error(e);
    }
}

/**
 * 按生命周期 发送 PV Enter 和 PV Leave
 * @param pageName
 * @param params
 */
export function sendPVEnterLeave(
    pageName,
    params = {},
) {
    onMount(() => {
        logger.sendImmediately(
            'PV',
            {
                page: pageName,
                ...params,
                type: 'enter',
            }
        )
    })
    onDestroy(() => {
        logger.sendImmediately(
            'PV',
            {
                page: pageName,
                ...params,
                type: 'leave',
            }
        )
    })
}

/**
 * 发送默认PV
 * 补充 document.referrer || '' 作为 params.source_url
 * @param pageName
 * @param params
 */
export function sendCommonPV(
    pageName,
    params = {},
) {
    sendPVEnterLeave(
        pageName,
        {
            ...params,
            source_url: document.referrer || ''
        }
    )
}

/**
 * 发送 TaskEvent 事件
 * 兼容v2 v3 格式
 * @note 格式判断依赖于 WebLogger.logConfig.proto 变量
 * v2 格式时，使用 option.action 作为 sendImmediately 第一个参数
 * v3 格式时，使用 'CLICK' 作为 sendImmediately 第一个参数
 * @param option
 */
export function sendTaskEvent(option) {
    // console.log(logger);
    const isV3 = (logger.logConfig && logger.logConfig.proto) === 'v3';
    const action = option.action;
    if (!action) {
        throw TypeError('option.action is required');
    }
    logger.sendImmediately(
        isV3 ? 'CLICK': action,
        option,
    )
}
