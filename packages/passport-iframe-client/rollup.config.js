// rollup.config.js
import typescript from 'rollup-plugin-typescript2';
import resolve from 'rollup-plugin-node-resolve';
import commonjs from 'rollup-plugin-commonjs';
import { terser } from 'rollup-plugin-terser';

export default [
    {
        input: 'src/index.ts',
        output: [{
            file: 'dist/index.esm.js',
            format: 'es',
            name: '@ks/passport-iframe-client',
            sourcemap: true,
        }, {
            file: 'dist/index.js',
            format: 'cjs',
            name: '@ks/passport-iframe-client',
            sourcemap: true,
        }, {
            file: 'dist/index.umd.js',
            format: 'umd',
            name: '@ks/passport-iframe-client',
            sourcemap: true,
        }],
        plugins: [
            resolve({browser: true}),
            commonjs(),
            typescript({
                clean: true,
                tsconfig: `tsconfig.json`,
                tsconfigOverride: {
                    compilerOptions: {
                        target: 'es5'
                    }
                },
                rollupCommonJSResolveHack: false
            }),
            terser(),
        ],
    },
];
