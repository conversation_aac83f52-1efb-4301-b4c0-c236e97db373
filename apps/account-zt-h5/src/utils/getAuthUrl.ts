import toParam from './toParam';
import { WEIXIN_OAUTH_URL } from '@/common/config/links';

interface ScopeHash {
    [k: number]: string;
}

export default function getAuthUrl({
    redirectUri,
    scope = 2,
    state,
    appid,
}: {
    redirectUri: string;
    scope?: number;
    state?: string;
    appid: string;
}) {
    const scopeHash: ScopeHash = {
        1: 'snsapi_base',
        2: 'snsapi_userinfo',
    };
    const params = {
        appid: appid,
        redirect_uri: redirectUri,
        response_type: 'code',
        scope: scopeHash[scope],
        state: state || '',
    };

    return `${WEIXIN_OAUTH_URL}?${toParam(params)}#wechat_redirect`;
}
