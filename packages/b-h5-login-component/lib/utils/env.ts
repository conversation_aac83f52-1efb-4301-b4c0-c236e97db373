export const getServiceOwnParams = (env: 'staging' | 'production', appName?: string) => {
    const o = {
        staging: {
            appId: 'ks694117295173644432',
        },
        production: {
            appId: 'ks667377170011306237',
        }
    };
    const config = o[env];
    const s = `{ "appId":"${config.appId}","scope":"user_info","redirect_uri":"https://passport-bid.staging.kuaishou.com", "appName":"${appName}"}`
    return s;
}
