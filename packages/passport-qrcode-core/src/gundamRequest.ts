import type {
    AxiosInstance,
    AxiosPromise,
    InternalAxiosRequestConfig,
} from 'axios';
import axios from 'axios';

export { default as axios } from 'axios';


export let request: AxiosInstance;
interface MultiAdapter {
    (config: InternalAxiosRequestConfig, next?: MultiAdapter): AxiosPromise;
}


let defaultAdapter: MultiAdapter = axios.getAdapter('xhr');
const adapterChain: Array<MultiAdapter> = [];

export function adapter(config: InternalAxiosRequestConfig) {
    const chain = [defaultAdapter].concat(adapterChain.slice(0));
    let index = chain.length - 1;
    async function next(conf: InternalAxiosRequestConfig) {
        if (index >= 0) {
            const fn = chain[index--];
            return fn(conf, next);
        }
        // 兜底这个结果, 通常不会执行到这里, 因为当index = 0时, 就是默认的adapter了。
        return {
            data: '',
            status: -1,
            statusText: '',
            headers: {},
            config: conf,
        };
    }
    return next(config);
}

export function addAdapter(fn: MultiAdapter) {
    // 防止同一个 adapter 重复添加
    if (!adapterChain.includes(fn)) {
        adapterChain.push(fn);
    }
}
