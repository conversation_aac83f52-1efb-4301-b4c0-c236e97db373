<template>
    <div>
        <!-- 调试 bridge 存在性 -->
        <div v-if="IS_DEV">
            <p>kwadSDK: {{hasSDK}}</p>
            <p>kwadSDK.handleLoginAction: {{hasSDKBridge}}</p>
        </div>
    </div>
</template>

<script>
import axios from 'axios';
import { baseUrl } from "@/common/config/base";

export default {
    data() {
        return {
            IS_DEV: process.env.NODE_ENV !== 'production',
            /** @deprecated */
            hasSDK: !!window.kwadSDK,
            /** @deprecated */
            hasSDKBridge: !!window.kwadSDK && !!window.kwadSDK.handleLoginAction,
        };
    },
    async mounted() {
        const query = this.$route.query;
        if (!window.kwadSDK) {
            console.info('kwadSDK not found');
        }
        const res = await axios.post(`${baseUrl}/rest/infra/sts`, {
            authToken: query.authToken,
            sid: query.sid,
        });
        if (this.IS_DEV) {
            console.log('loginSuccess', res.data);
            console.log('bridge', !!window.kwadSDK);
            this.hasSDK = window.kwadSDK;
            this.hasSDKBridge = this.hasSDK && window.kwadSDK.handleLoginAction;
        }
        window.kwadSDK.handleLoginAction(JSON.stringify({
            action: 'loginSuccess',
            params: res.data,
        }));
        window.kwadSDK.handleLoginAction(JSON.stringify({
            action: 'close',
        }));
    },
};
</script>
