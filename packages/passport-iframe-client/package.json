{"name": "@ks/passport-iframe-client", "version": "0.1.7", "description": "账户 iframe 客户端", "author": "kuaishou-fe", "main": "./dist/index.js", "module": "./dist/index.esm.js", "sideEffect": false, "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"dev": "rollup -c -w", "build": "rimraf dist && rollup -c", "publishBeta": "npm run build && npm publish --tag beta", "lintXXX": "lint-staged", "prepublishOnly": "node ../../scripts/prepublish.js", "sdk-version": "sdk-version upgrade"}, "dependencies": {"qs": "^6.9.6"}, "devDependencies": {"@types/node-fetch": "^2.1.2", "@types/qs": "^6.5.1", "@typescript-eslint/parser": "^4.15.1", "@typescript-eslint/eslint-plugin": "^4.15.2", "eslint": "^7.20.0", "http-server": "^0.12.3", "rimraf": "^2.6.2", "rollup": "^1.20.3", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.0", "rollup-plugin-visualizer": "^2.5.4", "standard-version": "^7.1.0", "typescript": "^3.9.7"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "lint-staged": {"src/*.ts": ["eslint src --ext .ts"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 9"]}