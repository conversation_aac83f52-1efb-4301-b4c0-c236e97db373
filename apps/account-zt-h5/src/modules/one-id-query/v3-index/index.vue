<template>
    <div class="one-id-query-container">
        <div class="logo" style="margin-top: 4.5625em;">
            <img src="@/assets/kwai.png" class="icon" />
        </div>
        <div class="tips">
            <p>您可以通过以下两种方式解除手机号码账号绑定：</p>
            <p>1、手机号码换绑：您可绑定新的手机号码，下次使用新的手机号登录。</p>
            <p>2、账号注销：注销您手机号绑定的账号，账号注销后将无法登录、无法找回。</p>
        </div>
        <div style="text-align: center;">
            <div @click="onRebindBtnClick" class="red-btn">
                以手机号码换绑的方式解绑
            </div>
        </div>
        <div style="text-align: center; margin-top: 2.5em;">
            <div @click="onUnregisterBtnClick" class="red-btn">
                以账号注销的方式解绑
            </div>
        </div>
       
    </div>
</template>

<script lang="ts">
export default {
    methods: {
        onRebindBtnClick() {
            (this as any).$router.push('/one-id-query/v3/rebind');
        },
        onUnregisterBtnClick() {
            (this as any).$router.push('/one-id-query/v3/unregister');
        }
    }
};
</script>

<style scoped lang="less">
@import "../common/styles.less";

.one-id-query-container {
  background-color: #fff;
}

.logo {
  text-align: center;

  .icon {
    width: 9.12513em;
    height: 4.625em;
  }
}

.tips {
    padding: 4em 3.2em;
    font-size: 0.8em;
    color: #999;
    p {
        margin: 0;
    }
}
</style>

