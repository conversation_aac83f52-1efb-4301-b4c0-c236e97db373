import { writable } from 'svelte/store';

interface ToastOptions {
    message: string;
    type?: 'warning' | 'loading' | 'success' | '';
    duration?: number;
}

// 状态
const state = writable({
    isOpen: false,
    message: '提示信息',
    type: '',
    duration: 0,
});

// 显示toast的逻辑
function show(options: ToastOptions) {
    return new Promise((resolve) => {
        state.update(value =>({
            isOpen: true,
            type: options?.type || '',
            message: options.message,
            duration: options.duration || 0,
        }));

        console.log(`Showing toast`, options);
        resolve(undefined); // 表示成功完成
    }).then(() => {
        if (options.duration && options.duration > 0) {
            return delay(options.duration).then(hide);
        } else {
            return delay(2000).then(hide);
        }
    });
}

// 隐藏 Toast
function hide() {
    return new Promise(resolve => {
        state.update(value => ({
            isOpen: false,
            type: '',
            message: '',
            duration: 0,
        }));

        console.log('Hiding toast');
        resolve(undefined);
    });
}

// 延迟时间
function delay(duration: number) {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve(undefined);
        }, duration);
    });
}

export const useToast = () => {
    return  {
        show,
        hide,
        state,
    };
}
