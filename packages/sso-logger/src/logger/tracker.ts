/**
 * @file 日志的上报
 */
import { sendBeacon } from '@/common/request/beacon';
import { pkgName, pkgVersion, did } from '@/common/utils/common';
import { sniffUrlWithCached } from '@/common/utils/sniffUrl';
import { TrackerConfig, ReportParams } from '@/logger/types';
import { ITraceBodyParams, ITraceBody } from './types';
import { getAccount, genUUID } from './utils';

const trackPath = '/rest/zt/basic/tracing/report';
// 默认的上报域名 - 生产环境
const trackDefaultDomain = 'https://verification.kuaishouzt.cn';

export class Tracker {
    private readonly config!: TrackerConfig;
    private readonly trackUrl!: Promise<string>;
    constructor(config: TrackerConfig) {
        this.config = config;
        this.trackUrl = this.getTrackUrl(this.config.domains);
    }

    private async getTrackUrl(domains: TrackerConfig['domains']) {
        // 发送埋点的接口地址
        const finalDomains = await domains  || [];
        const urls = finalDomains.map(domain => `${domain}${trackPath}`);
        return sniffUrlWithCached(urls);
    }

    // 生成上报数据体params字段
    private genTrackBodyParamsByApi(params: ReportParams): ITraceBodyParams {
        const duration = params.endTime - params.startTime;
        const result: ITraceBodyParams = {
            ownerSDKName: pkgName,
            ownerSDKVer: pkgVersion,
            baseSDKName: this.config.baseSDK.name,
            baseSDKVer: this.config.baseSDK.version,
            callerSDKName: this.config.callerSDK.name,
            callerSDKVer: this.config.callerSDK.version,
            apiUrl: params.apiUrl,
            account: getAccount(params.request),
            siteUrl: window.location.href,
            sid: this.config.sid || '',
            duration,
            did,
            userAgent: window.navigator.userAgent,
            reportType: params.reportType,
            channelType: this.config.channelType,
            userId: params.response.userId,
        } as ITraceBodyParams;
        if (params.result && params.result !== 'SUCC') {
            result.errorDetail = params.response;
        }
        return result;
    }

    // 生成上报数据体
    private genTrackBodyByApi(params: ReportParams): ITraceBody {
        const result: ITraceBody = {
            verifySessionId: genUUID(),
            // upStreamSpanName, spanName, spanContexts这三个参数目前用不上，随便填的
            upStreamSpanName: 'passport-empty',
            spanName: 'passport-empty',
            spanContexts: 'FOLLOWS_FROM',
            startTime: params.startTime,
            endTime: params.endTime,
            reportTime: 0,
            result: params.result,
            returnResult: {
                resultCode: params.response.result,
            },
            category: 'PASSPORT',
            sid: this.config.sid || '',
            did,
            userId: params.response.userId,
            params: this.genTrackBodyParamsByApi(params),
        };
        return result;
    }

    /**
     * 调用接口上报埋点
     */
    public async sendTrack(trackBody: object) {
        // 发送埋点的接口地址
        // trackUrl为promise
        const url = await this.trackUrl;
        const defaultUrl = trackDefaultDomain + trackPath;
        return sendBeacon(url || defaultUrl, trackBody);
    }
    /**
     * 接口埋点的上报
     */
    public sendApiTrack(params: ReportParams) {
        const trackBody = this.genTrackBodyByApi(params);
        return this.sendTrack(trackBody);
    }
}



