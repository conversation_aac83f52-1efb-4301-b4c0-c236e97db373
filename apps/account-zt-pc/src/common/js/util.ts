import { webLog } from '@/common/js/log';
import { IS_PROD } from '@/common/js/const';

export function parseUrl(url: string) {
    const index = url.indexOf('?');
    if (index < 0) {
        return {
            baseUrl: url,
            search: '',
            paramObj: {},
        };
    }
    const baseUrl = url.substr(0, index);
    const search = url.substr(index + 1);
    const paramObj = search.split('&')
        .map(x => x.split('='))
        .reduce((o, [key, value]) => {
            o[decodeURIComponent(key)] = decodeURIComponent(value);
            return o;
        }, {} as Record<string, any>);
    return {
        baseUrl,
        search,
        paramObj,
    };
}

export enum LoginChannelType {
    PC_PAGE = 'PC_PAGE',
    PC_MODAL = 'PC_MODAL',
}

export function updateLoggerLoginChannel(channelType: LoginChannelType) {
    const h5_extra_attr = webLog.commonPackage?.h5_extra_attr;
    webLog.updateCommonPackage({
        h5_extra_attr: {
            ...h5_extra_attr,
            channelType,
        },
    });
}

export function getParentUrlObject() {
    let url;
    if (parent !== window) {
        try {
            url = parent.location.href;
        } catch (e) {
            // Electron 桌面应用会返回空字符串
            url = document.referrer;
        }
    }
    return new URL(url || location.href);
}

export function redirectTo(url: string) {
    webLog.sendImmediately('PV', {
        page: 'ACCOUNT_LOGIN_DEFAULT',
        type: 'leave',
        params: {
            source_url: document.referrer || '',
            target_url: url,
        },
    });
    window.location.href = url;
}

export function appendParam(
    url: string,
    params: { [key: string]: any },
) {
    const sp = url.indexOf('?') === -1 ? '?' : '&';
    const arr = [] as string[];
    const hasOwnProperty = {}.hasOwnProperty;
    for (const key in params) {
        if (hasOwnProperty.call(params, key)) {
            const value = params[key] || '';
            arr.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));
        }
    }
    if (arr.length) {
        url += sp + arr.join('&');
    }
    return url;
}

export function paramsToString(params: object) {
    return Object.entries(params)
        .map(([key, value]) => {
            return `${encodeURIComponent(key)}=${encodeURIComponent(value as string)}`;
        })
        .join('&');
}

export function isUrlValid(url: string) {
    try {
        new URL(url);
        return true;
    } catch (e) {
        return false;
    }
}

/**
 * 判断 callback 是否是有效的，必须路径和主域名都在白名单里
 * @param url callback
 * @param whiteList 主域名白名单，类似 [kuaishou.com]
 * @returns 是否有效
 */
export function isCallbackValid(url: string, whiteList: string[]): boolean {
    try {
        const whitePathsMap = {
            production: [
                '/rest/infra/sts',
                '/oauth2/web/api/sts',
                '/portal/rest/infra/sts',
                '/pass/kuaishou/login/passToken',
                '/rest/k/sts',
                '/thirdPart/qq/',
                '/thirdPart/wechat/',
                '/pc/account/third-part-login-page/qq',
                '/pc/account/third-part-login-page/wechat',
            ],
            staging: [
                '/rest/infra/sts',
                '/oauth2/web/api/sts',
                '/portal/rest/infra/sts',
                '/pass/kuaishou/login/passToken',
                '/rest/k/sts',
                '/thirdPart/qq/',
                '/thirdPart/wechat/',
                '/pc/account/third-part-login-page/qq',
                '/pc/account/third-part-login-page/wechat',
            ],
        };

        const { origin, pathname } = new URL(url); // `url` should be a legal URL

        const matchedPath = pathname === '/'
            ? '/'
            : whitePathsMap[IS_PROD ? 'production' : 'staging'].find((whitePath: string) => whitePath === '*' || pathname === whitePath);

        const matchedOrigin = whiteList.filter(whiteUrl => origin.endsWith(whiteUrl));
        return matchedOrigin.length !== 0 && !!matchedPath;
    } catch {
        return false;
    }
}
