import axios from 'axios';
import stringify from '../utils/stringify';
import { MUTI_ACCOUNTS_CODE, TEENAGER_RECHARGE_CODE, NOT_REGIST_KWAI } from '@/common/config/constants'

export type AjaxResult<T = Record<string, never>> = {
    result: number;
    error_msg?: string;
} & T

const request = axios.create({
    timeout: 30000,
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
    },
    withCredentials: true,
});

request.defaults.transformRequest = [function (data, headers) {
    if (headers['Content-Type'] === 'multipart/form-data') { // 使用FormData时，不用qs改写数据
        return data;
    }
    if (headers['Content-Type'] === 'application/json') { // 使用json时，需要字符串化json对象
        return JSON.stringify(data);
    }
    return stringify(data);
}];

request.interceptors.request.use(
    function (config) {
        config.url = process.env.PREFIX + config.url;
        return config;
    },
    function (error) {
        return Promise.reject(error);
    },
);

const success = new Set<any>([1, MUTI_ACCOUNTS_CODE, 'SUCCESS', NOT_REGIST_KWAI, TEENAGER_RECHARGE_CODE]);

request.interceptors.response.use(
    function (response) {
        if (response.status === 200) {
            const data = response.data;
            if (success.has(data.result)) {
                return data;
            }
            return Promise.reject(data);
        }
    },
    function (error) {
        throw error;
    },
);

export default request;
