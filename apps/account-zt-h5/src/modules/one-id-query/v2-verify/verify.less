@import "../common/styles.less";

/deep/ .input-component {

  position: relative;

  input:not(:focus) + .clear-icon, input:not(:focus) + .captcha-btn .clear-icon {
    opacity: 0;
  }

  .clear-icon {
    position: absolute;
    top: 0;
    bottom: 0;
    display: grid;
    place-items: center;

    img {
      width: 1em;
      height: 1em;
    }
  }

  &.phoneNumber .clear-icon {
    right: 0.995em;
  }

  &.captcha .clear-icon {
    left: -1.2738em;
    transform: translateX(-100%);

    img {
      font-size: 1.23em;
    }
  }

  // 区号和验证码按钮
  .area-code, .captcha-btn {
    color: #222222;
    display: grid;
    place-items: center;
    position: absolute;
    bottom: 0;
    top: 0;
  }

  &.phoneNumber {
    // 区号
    .area-code {
      width: 3.8em;
      left: 0;
      font-size: 0.9375em;

      font-family: PingFang SC;
      font-style: normal;
      font-weight: 500;
    }

    input {
      padding-left: 3.9em;
    }
  }

  &.captcha {
    // 验证码按钮
    .captcha-btn {
      color: #385080;
      right: 0;
      width: 7em;
      font-size: 0.8125em;

      &:hover {
        opacity: .8;
      }

      &:active {
        opacity: .5;
      }
    }

    input {
      padding-right: 7em;
    }
  }

  input {

    color: #222;

    &::placeholder {
      color: #9C9C9C;
    }

    -webkit-appearance: none;
    box-shadow: none;
    margin: 0;
    resize: none;
    border: none;
    outline: none;

    border-radius: 0.5em;
    background: #F8F8F8;
    width: 100%;

    height: 3.46666667em;
    line-height: 3.46666667em;
    font-size: 0.9375em;
    padding: 0 1em;

  }

}

.gray-text {
  color: #9C9C9C;
  font-feature-settings: 'clig' off, 'liga' off;
  font-family: PingFang SC;
  font-size: 0.8125em;
  font-style: normal;
  font-weight: 400;
  line-height: 1;
}

.black-text {
  color: #222;
  font-family: PingFang SC;
  font-size: 0.9375em;
  font-style: normal;
  font-weight: 400;
  line-height: 1.375em; /* 146.667% */
}

/deep/ .upload-component {

  margin-top: 0.438596em;
  font-size: 1.14em;

  width: 4.8246143em;
  height: 4.8246143em;
  border-radius: 0.5em;
  background-color: #F8F8F8;

  display: inline-flex;
  justify-content: center;
  align-items: center;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;

  position: relative;

  .clear-icon {
    width: 1.315789em;
    height: 1.315789em;
    background-color: rgba(0, 0, 0, 0.30);
    position: absolute;
    top: 0;
    right: 0;
    border-radius: 0 .5em;

    display: grid;
    place-items: center;

    svg {
      width: 1em;
      height: 1em;
    }

    :hover {
      opacity: .9;
    }

    &:active {
      opacity: .618;
    }
  }


  .upload-tip {

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 0.25em;

    svg.icon {
      width: 1.3em;
      height: 1.3em;
    }

    .upload-text {
      font-size: 0.6578947em;
      color: #C6C6C6;
    }

  }

}
