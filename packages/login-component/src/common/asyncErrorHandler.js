// 用于处理全局异步异常的，以decorator方式使用于项目中
export default function globalAsyncErrorHandler(target, propertyKey, descriptor) {
    return {
        get() {
            // 此处不可使用arrow function，因为vue的this是动态变化的…
            return async function (...args) {
                try {
                    await descriptor.value.apply(this, args);
                } catch (e) {
                    eventBus.$emit('global-error', e);
                    throw e;
                }
            };
        }
    };
}
