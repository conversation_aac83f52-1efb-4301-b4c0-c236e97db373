<script lang="ts">
import Select from "$lib/shared/select/Select.svelte";
import Input from "$lib/shared/forms/input.svelte";
import CloseButton from "$lib/utils/CloseButton.svelte";
import { createEventDispatcher } from 'svelte';

const dispatch = createEventDispatcher();


export let smsStore;

let isFocused = false;
let divElement;

function handleFocus() {
    dispatch('focus');
    isFocused = true;
    divElement?.focus();
}

function handleBlur() {
    isFocused = false;
    smsStore?.reducers?.onPhoneBlur?.();
}

let errorClass = '';
let divStyle = 'border-color: #1D59F2; box-shadow: 0 0 0 2px #0075ff33;'
$: {
    if (isFocused) {
        divStyle = 'border-color: #1D59F2; box-shadow: 0 0 0 2px #0075ff33;'
    } else {
        divStyle = '';
    }
    if ($smsStore.phoneErrorMsg) {
        errorClass = ' input-error ';
    } else {
        errorClass = '';
    }
}
console.log('$$props.class', $$props.class);

</script>

<div class={'phone-input-wrapper border border-[#EAEAEA] ' + errorClass + $$props.class}
     bind:this={divElement}
>
    <Select
            options={$smsStore.countryCodeList}
            value={$smsStore.countryCode}
            handleKeydown={smsStore.reducers.handleKeydown}
            changeHighlight={smsStore.reducers.changeHighlight}
            onSelect={smsStore.reducers.handleSelect}
            onSelectWrapper={smsStore.reducers.onSelectWrapper}
            setVisble={smsStore.reducers.setVisble}
            class={$smsStore.phoneInputClass}
    />
    <Input placeholder="请输入手机号"
           bind:value={$smsStore.phone}
           class={$smsStore.phoneInputClass}
           style="border: none; box-shadow: none"
           on:focus={handleFocus}
           on:blur={handleBlur}
           on:input={smsStore.reducers.filterPhone}
           maxlength={11}
    >
        <slot slot="right">
            {#if $smsStore.phone}
                <CloseButton on:click={() => smsStore.setValues({phone: ''})} />
            {/if}
        </slot>
    </Input>
</div>

<style>
    .phone-input-wrapper {
        /*border: 1px solid #EAEAEA;*/
        display: flex;
        width: 100%;
        border-radius: var(--inputRadius);
    }
    /*.phone-input-wrapper:hover {*/
    /*    !*border-color: #4A84F7;*!*/
    /*}*/

    .input-error {
        border-color: #E02C1F;
    }
</style>
