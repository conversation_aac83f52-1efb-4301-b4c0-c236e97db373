import { EMAIL_SOURCE, KEYCODE, EnumInputLoginType } from '$lib/shared/const'
import { writable } from 'svelte/store'
import { filterInput, isValidAccount } from "$lib/utils/validate";



export function createPasswordStore(passwordLoginTypes: string[]) {
    let loginType: EnumInputLoginType
    if (passwordLoginTypes.length === 2) {
        loginType = EnumInputLoginType.phoneAndEmail
    } else if (passwordLoginTypes.includes(EnumInputLoginType.phone)) {
        loginType = EnumInputLoginType.phone
    } else {
        loginType = EnumInputLoginType.email
    }
    console.log('loginType', loginType);
    let state = {

        account: '',
        accountErrorMsg: '',

        password: '',
        passwordErrorMsg: '',

        emailInput: undefined as any,
        emailDropdownList: EMAIL_SOURCE,
        emailDropdownVisible: false,
        emailDropdown: null as any,
        emailHighlightIndex: 0,
        loginType,
        placeholder: loginType === EnumInputLoginType.phoneAndEmail ? '请输入手机号/邮箱' : loginType === EnumInputLoginType.email ? '请输入邮箱' : '请输入手机号',
    }

    const { set, update, subscribe } = writable({
        ...state,
    });

    subscribe(value => {
        state = value
    })

    const setValues = (value: Partial<Parameters<typeof set>[0]>) => {
        set({
            ...state,
            ...value,
        })
    }

    const validateAccount = () => {
        let passed = true;
        if (!state.account) {
            setValues({
                accountErrorMsg: state.placeholder,
            });
            passed = false;
        } else if (!isValidAccount(state.account, loginType)) {
            let msg = '手机/邮箱格式不正确';
            if (loginType === EnumInputLoginType.phone) {
                msg = '手机号码格式不正确';
            } else if (loginType === EnumInputLoginType.email) {
                msg = '邮箱格式不正确';
            }
            setValues({
                accountErrorMsg: msg,
            });
            passed = false;
        }
        if (passed) {
            setValues({
                accountErrorMsg: '',
            });
        }
        return passed;
    }

    const validatePassword = () => {
        let passed = true;
        if (!state.password) {
            setValues({
                passwordErrorMsg: '请输入密码',
            });
            passed = false;
        } else if (state.password?.length < 8 || state.password?.length > 20) {
            setValues({
                passwordErrorMsg: '请输入8-20个字符的密码',
            });
            passed = false;
        }
        if (passed) {
            setValues({
                passwordErrorMsg: '',
            });
        }
        return passed;
    }

    const reducers = {
        updateInputValue(e: Event) {
            if (filterInput(e)) {
                return;
            }
            let value = e.target.value || ''
            setValues({
                account: value,
                accountErrorMsg: '',
            })
            const emailPrefix = value.slice(0, value.indexOf('@'))

            const emailDropdownList = EMAIL_SOURCE.map(item => {
                return {
                    label: `${emailPrefix}@${item.label}`,
                    value: `${emailPrefix}@${item.value}`,
                    key: `${emailPrefix}@${item.value}`,
                }
            }).filter(item => item.value.startsWith(value))

            const emailDropdownVisible =
                !!emailDropdownList.length &&
                value.indexOf('@') !== -1 &&
                value.indexOf('@') === value.lastIndexOf('@')

            setValues({
                emailDropdownList,
                emailDropdownVisible,
                emailHighlightIndex: 0,
            })
        },

        clearInputValue() {
            setValues({
                account: '',
                accountErrorMsg: '',
            })
        },

        handleEmailSelect(value: string) {
            setValues({
                account: value,
                emailDropdownVisible: false,
            });
            validateAccount();
        },
        updateHighlight(index: number) {
            let emailHighlightIndex: number

            if (index < 0) {
                emailHighlightIndex = state.emailDropdownList.length - 1
            } else if (index > state.emailDropdownList.length - 1) {
                emailHighlightIndex = 0
            } else {
                emailHighlightIndex = index
            }

            setValues({
                emailHighlightIndex,
            })
        },

        handleKeydown(e: Event) {
            if (e.keyCode === KEYCODE.TAB) {
                if (state.emailDropdownVisible) {
                    setValues({
                        emailDropdownVisible: false,
                    })
                    e.preventDefault()
                }

                state.emailInput?.blur()
                return
            }

            switch (e.keyCode) {
                case KEYCODE.DOWN:
                    reducers.updateHighlight(state.emailHighlightIndex + 1)
                    break

                case KEYCODE.UP:
                    reducers.updateHighlight(state.emailHighlightIndex - 1)
                    break

                case KEYCODE.ENTER:
                    if (state.emailDropdownVisible) {
                        setValues({
                            account: state.emailDropdownList[state.emailHighlightIndex].value,
                            emailDropdownVisible: false,
                        })
                    }
                    break

                case KEYCODE.ESC:
                    setValues({
                        emailDropdownVisible: false,
                    })
                    break
            }
        },
        validateSend() {
            return validateAccount();
        },

        validateLogin() {
            const bool1 = validateAccount();
            const bool2 = validatePassword();
            return bool1 && bool2;
        },
        onAccountBlur() {
            validateAccount();
        },
        onPasswordBlur() {
            validatePassword();
        },
    }

    return {
        update,
        subscribe,
        set,
        setValues,
        reducers,
    }
}
