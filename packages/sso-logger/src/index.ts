// 增加一下Promise判断
if (!Promise || typeof Promise.prototype.then !== 'function') {
    throw new Error('@ks/sso-logger lib need Promise polyfill');
}
import { LoggerConfig } from './logger/types';
import Logger from './logger/instance/index';
import AsyncLoggerProxy from './proxy/logger';
import { notifyAsyncModuleLoadEvent, env, pkgName, pkgVersion, Env } from './common/utils/common';
import { getLoggerResource, } from './common/utils/services';

function getInstance(config: LoggerConfig) {
    return new Logger(config);
}

const asyncModule = {
    getInstance,
};

export class SSOLogger extends AsyncLoggerProxy {
    constructor(loggerConfig: Pick<LoggerConfig, 'callerSDK' | 'sid' | 'channelType' >, options: { env?: Env }) {
        env.set(options.env || 'production');
        const sid = loggerConfig.sid;
        const resource = getLoggerResource(sid);
        const remoteSrc = resource.cdn;
        const domains = resource.domains;
        const finalLoggerConfig = {
            ...loggerConfig,
            domains,
            baseSDK: { // 基线版信息
                name: pkgName,
                version: pkgVersion,
            },
        };
        const baseModule = asyncModule;
        super(baseModule, remoteSrc , finalLoggerConfig);
    }
}

notifyAsyncModuleLoadEvent(asyncModule);
