/**
 * @file (远端模块构建)实例的代理对象
 */
import AsyncLoadManager from '@/load/manager';

type InvokeFn = (...args: unknown[]) => unknown;

export default abstract class AsyncInstanceProxy<Instance extends object, Base, Remote extends Base = Base> {
    private manager!: AsyncLoadManager<Base>;
    private instance!: Instance;
    private readonly remoteSrc!: string|Promise<string>;
    protected constructor(baseModule: Base, remoteSrc: string|Promise<string>) {
        this.manager = new AsyncLoadManager<Base>(baseModule);
        this.remoteSrc = remoteSrc;
        this.getInstance();
    }
    public async getInstance(): Promise<Instance> {
        if (this.instance) {
            return this.instance;
        }
        const remoteSrc: string = await this.remoteSrc;
        const module = await this.manager.getModule(remoteSrc);
        return this.instance = this.createInstance(module);
    }
    protected abstract createInstance(module: Base|Remote): Instance;
    public async invoke(fnName: string, ...args: Parameters<InvokeFn>) {
        const instance = await this.getInstance();
        const res = (instance as Record<string, InvokeFn>)[fnName].apply(instance, args);
        return res;
    }
}
