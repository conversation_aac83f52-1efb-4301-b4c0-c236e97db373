/**
 * @file 发送埋点相关的请求使用
 */
import { request } from './index';

export function sendBeacon(url: string, data: Record<string, any>) {
    if (!navigator.sendBeacon) {
        return request(url, data);
    }
    try {
        const headers = {
            type: 'application/json',
        };
        const blob = new Blob([JSON.stringify(data)], headers);
        return navigator.sendBeacon(url, blob);
    } catch {
        return request(url, data);
    }
}

