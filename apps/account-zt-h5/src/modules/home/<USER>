<template>
    <div class="fallback">
        <div class="box">
            <div
                class="img img-light"
                :style="{ backgroundImage: `url(${content.src})` }"
            ></div>
            <div
                class="img img-dark"
                :style="{ backgroundImage: `url(${content.darkSrc})` }"
            ></div>
            <div class="tip">
                {{ content.tip }}
            </div>
            <div class="btn" @click="refresh">
                <span>重试</span>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { ref, reactive, Ref, UnwrapRef } from '@vue/composition-api';

export enum TYPE {
    notFound = 1,
    networkError,
}

interface InfoDetail {
    src?: string;
    tip?: string;
}
interface ContentInfo {
    info: InfoDetail;
}

const typeInfo = {
    [TYPE.notFound]: {
        src: require('../../assets/404.png'),
        darkSrc: require('../../assets/404Dark.png'),
        tip: '页面好像走丢了，再试一下',
    },
    [TYPE.networkError]: {
        src: require('../../assets/networkError.png'),
        darkSrc: require('../../assets/networkErrorDark.png'),
        tip: '请检查网络链接是否正常',
    },
};

function refresh() {
    window.location.reload();
}

export default {
    setup(props: any, { root }: { root: any }) {
        const type: Ref<TYPE> = ref(TYPE.networkError);
        const content: UnwrapRef<ContentInfo> = reactive({
            info: {},
        });
        const { $route } = root;
        type.value = $route.query.type || TYPE.notFound;
        content.info = typeInfo[type.value];
        return {
            type,
            content: content.info,
            refresh,
        };
    },
};
</script>

<style lang="less" scoped="true">
.dark {
    .fallback {
        background-color: #19191e;
    }
    .img-dark {
        display: block;
    }
    .img-light {
        display: none;
    }
    .tip {
        color: #9c9c9c;
    }
}
.fallback {
    display: flex;
    justify-content: center;
    min-height: 100vh;
}
.box {
    width: 3.77rem;
    margin-top: 2.79rem;
    flex-direction: column;
    display: flex;
    align-items: center;
}
.img {
    width: 3.77rem;
    height: 2.97rem;
    background-repeat: no-repeat;
    background-image: url(../../assets/404.png);
    background-size: contain;
}
.img-dark {
    display: none;
}
.tip {
    font-size: 0.28rem;
    font-weight: 500;
    text-align: center;
    color: #9c9c9c;
    margin-bottom: 0.4rem;
}
.btn {
    width: 2.24rem;
    height: 0.72rem;
    background: #ff5000;
    border-radius: 0.36rem;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
        font-size: 0.28rem;
        font-weight: 500;
        text-align: center;
        color: #ffffff;
    }
}
</style>
