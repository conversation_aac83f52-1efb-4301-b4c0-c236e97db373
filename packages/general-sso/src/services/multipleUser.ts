import { getConfig } from '@/core/configInit';
import { DEFAULT_COUNTRY_CODE } from '@/config';
import { getLoginToken, AuthTokenResult } from '../core/loginToken';
import { genEncryptReqParams } from '@/utils';

/**
 * 多账号下选择用户
 */
export interface MultiUserInfoForPhone {
    userId: number;
    multiUserToken: string;
    phone: string;
    countryCode?: string;
}
export function chooseUser({
    userId,
    multiUserToken,
    phone,
    countryCode,
}: MultiUserInfoForPhone): Promise<AuthTokenResult>;
export function chooseUser({ userId, multiUserToken, phone, countryCode = DEFAULT_COUNTRY_CODE }: any) {
    const config = getConfig();
    return getLoginToken(`/pass/${config.appName}/web/login/multiTokenLogin`, {
        sid: config.sid,
        countryCode,
        phone,
        userId,
        multiUserToken,
    });
}

export function chooseUserV2({
                               userId,
                               multiUserToken,
                               phone,
                               countryCode,
                           }: MultiUserInfoForPhone): Promise<AuthTokenResult>;
export function chooseUserV2({ userId, multiUserToken, phone, countryCode = DEFAULT_COUNTRY_CODE }: any) {
    const encryptReqParams = genEncryptReqParams({
        phone,
    });
    const config = getConfig();
    return getLoginToken(`/pass/${config.appName}/web/login/multiTokenLogin/v2`, {
        sid: config.sid,
        countryCode,
        userId,
        multiUserToken,
        ...encryptReqParams,
    });
}
