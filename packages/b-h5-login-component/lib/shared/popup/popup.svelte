<script>
    import { createEventDispatcher } from 'svelte';

    const dispatch = createEventDispatcher();

    const SP_PREFIX = 'sp';
    const COMPONENT_NAME = `${SP_PREFIX}-popup`;

    const durationMap = (ratio) => {
        if (ratio > 0 && ratio < 0.11) {
            return 200;
        } else if (ratio >= 0.11 && ratio < 0.22) {
            return 250;
        } else if (ratio >= 0.22 && ratio < 0.4) {
            return 300;
        } else if (ratio >= 0.4 && ratio < 0.75) {
            return 350;
        } else if (ratio >= 0.75) {
            return 400;
        }
        return 300;
    };

    let isPopupShow = false;
    let isPopupBoxShow = false;
    let isAnimating = false;
    let domTransferred = false;

    let show = false;
    let showMask = true;
    let maskCloseable = true;
    let position = 'center';
    let transition = '';
    let disableScroll = false;
    let maskStyle = {};
    let transferDom = false;
    let popupClass = '';
    let maskEnterDuration = 0;
    let boxEnterDuration = 0;
    let maskLeaveDuration = 0;
    let boxLeaveDuration = 0;
    let addMargin = false;
    let needSafeArea = false;

    $: transition = (() => {
        switch (position) {
            case 'top':
                return 'slide-down';
            case 'right':
                return 'slide-left';
            case 'bottom':
                return 'slide-up';
            case 'left':
                return 'slide-right';
            case 'fade':
                return 'fade';
            default:
                return 'fade-grow';
        }
    })();

    $: popupClasses = [COMPONENT_NAME, `is-${position}`, { 'is-mask-show': showMask }];

    $: extraClass = (() => {
        let className = '';
        if (position === 'bottom' && addMargin) {
            className = 'add-margin';
            if (needSafeArea) {
                className += ' safe-area';
            }
        }
        return className;
    })();

    function showPopupBox() {
        if (transferDom && !domTransferred) {
            if (typeof window === 'undefined') {
                return;
            }
            domTransferred = true;
            if (popupClass) {
                document.querySelector(`.${COMPONENT_NAME}`).classList.add(popupClass);
            }
            document.body.appendChild(document.querySelector(`.${COMPONENT_NAME}`));
        }
        isPopupShow = true;
        isAnimating = true;
        isPopupBoxShow = true;
    }

    function hidePopupBox() {
        isAnimating = true;
        isPopupBoxShow = false;
        dispatch('toggle', false);
    }

    function handleTouchMove(e) {
        if (disableScroll) {
            e.preventDefault();
        }
    }

    function handlePopupMaskClick() {
        if (maskCloseable) {
            hidePopupBox();
            dispatch('mask-click');
        }
    }

    function handlePopupTransitionStart() {
        if (!isPopupBoxShow) {
            dispatch('before-hide');
        } else {
            dispatch('before-show');
        }
    }

    function handlePopupTransitionEnd() {
        if (!isAnimating) {
            return;
        }
        if (!isPopupBoxShow) {
            isPopupShow = false;
            dispatch('hide');
        } else {
            dispatch('show');
        }
        isAnimating = false;
    }

    function onEnter() {
        if (transition === 'slide-up' || transition === 'slide-down') {
            const duration = durationMap(document.querySelector(`.${COMPONENT_NAME}__box`).offsetHeight / document.querySelector(`.${COMPONENT_NAME}`).offsetHeight);
            document.querySelector(`.${COMPONENT_NAME}__box`).style.transitionDuration = duration + 'ms';
            document.querySelector(`.${COMPONENT_NAME}__mask`).style.transitionDuration = duration + 'ms';
        } else {
            if (boxEnterDuration) {
                document.querySelector(`.${COMPONENT_NAME}__box`).style.transitionDuration = boxEnterDuration + 'ms';
            } else {
                document.querySelector(`.${COMPONENT_NAME}__box`).style.transitionDuration = '';
            }

            if (maskEnterDuration) {
                document.querySelector(`.${COMPONENT_NAME}__mask`).style.transitionDuration = maskEnterDuration + 'ms';
            } else {
                document.querySelector(`.${COMPONENT_NAME}__mask`).style.transitionDuration = '';
            }
        }
    }

    function onLeave() {
        if (boxLeaveDuration) {
            document.querySelector(`.${COMPONENT_NAME}__box`).style.transitionDuration = boxLeaveDuration + 'ms';
        } else {
            document.querySelector(`.${COMPONENT_NAME}__box`).style.transitionDuration = '';
        }

        if (maskLeaveDuration) {
            document.querySelector(`.${COMPONENT_NAME}__mask`).style.transitionDuration = maskLeaveDuration + 'ms';
        } else {
            document.querySelector(`.${COMPONENT_NAME}__mask`).style.transitionDuration = '';
        }
    }

    $: show && showPopupBox();
</script>

<div>
    <div
            class={popupClasses}
            bind:this={popup}
    >
        {#if isPopupShow}
            <div
                    class="maskfade"
                    bind:this={popupmask}
                    {#if showMask && isPopupBoxShow}
                    on:click={handlePopupMaskClick}
                    on:touchmove={handleTouchMove}
                    class={`${COMPONENT_NAME}__mask`}
                    style={maskStyle}
                    {/if}
            >
                <div
                        {#if isPopupBoxShow}
                        on:before-enter={handlePopupTransitionStart}
                        on:before-leave={handlePopupTransitionStart}
                        on:after-enter={handlePopupTransitionEnd}
                        on:after-leave={handlePopupTransitionEnd}
                        on:enter={onEnter}
                        on:leave={onLeave}
                        class={`${COMPONENT_NAME}__box ${transition} ${extraClass}`}
                        bind:this={popupbox}
                        on:touchmove={handleTouchMove}
                        {/if}
                >
                    <slot />
                </div>
            </div>
        {/if}
    </div>
</div>
