import { defineConfig } from 'vite'
import { svelte } from '@sveltejs/vite-plugin-svelte'
import dts from 'vite-plugin-dts'
import { libInjectCss } from 'vite-plugin-lib-inject-css'
import { resolve } from 'path'
import packageJson from "./package.json";

// https://vitejs.dev/config/
export default defineConfig({
    plugins: [
        svelte(),
        libInjectCss(),
        dts({ include: ['lib'] })
    ],
    define: {
        __PACKAGE_VERSION__: JSON.stringify(packageJson.version),
    },
    resolve: {
        alias: {
            '$lib': resolve(__dirname, 'lib')
        }
    },
    build: {
        copyPublicDir: false,
        lib: {
            entry: resolve(__dirname, 'lib/index.ts'),
            formats: ['es'],
            fileName: '[name]'
        },
        terserOptions: {
            compress: {
                drop_console: true,
                drop_debugger: true,
            }
        },
        rollupOptions: {
            external: ["tailwindcss", "@ks/general-sso", "@ks/sso"],
        },
        minify: true,
        // sourcemap: true,
    },
    server: {
        port: 5000,
    }
})
