import qs, { parse } from "qs";

export function useURLSearch() {
    const search = location.search ? location.search.substr(1) : '';

    if (!search) {
        return {};
    }
    const result = parse(search);
    return result;
}

export function appendParams(url: string, params: object) {
    const searchIndex = url.indexOf('?');
    const preUrl = searchIndex > -1 ? url.slice(0, searchIndex) : url;

    const oldParams = searchIndex === -1
        ? {}
        : qs.parse(url.slice(searchIndex + 1));

    const newParams = {
        ...oldParams,
        ...params,
    };
    return `${preUrl}?${qs.stringify(newParams)}`;
}
