import request from './request';
import { baseUrl } from '@/common/config/base';
// 看文档: https://kapi.corp.kuaishou.com/project/5843/interface/api/461475

interface loginViaWxParam {
    appId: string;
    sid: string;
    code: string;
    createId: boolean;
}

export interface loginViaWxRes {
    result: number;
    'kuaishou.shop.b.at': string;
    'kuaishou.shop.b_st': string;
    ssecurity: string;
    passToken: string;
    userId: number;
    stsUrl: string;
    followUrl: string;
    error_msg?: string;
}

export interface PageInfoRes {
    result: number,
    background: string[],
    thirdPartyLogin: boolean,
    callbackUrls: string[],
    logo: string[],
    type: number,
}

export interface LoginConfig {
    result: number;
    userAgreement: string;
    privacyPolicy: string;
    title: string;
    logoUrl: string;
}



export function loginViaWx(param: loginViaWxParam) {
    return request.post<loginViaWxRes>(`${baseUrl}/pass/kuaishou/login/sns/h5/code`, param);
}

export function loginPageInfo(param: { sid: string }): Promise<PageInfoRes> {
    return request.post(`${baseUrl}/pass/kuaishou/pc/pageInfo`, param);
}

export function getLoginPageInfoFetcher(sid: string) {
    return () => request.get(`${baseUrl}/pass/kuaishou/getH5LoginConfig`, {
        params: {
            sid,
        },
    }).then(data => {
        return data as unknown as Promise<LoginConfig>;
    }).catch(() => {
        return {
            result: 1,
            title: '快手账号登录',
            logoUrl: '',
            privacyPolicy: '',
            userAgreement: '',
        };
    });
}