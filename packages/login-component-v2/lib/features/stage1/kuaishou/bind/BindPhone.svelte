<script lang="ts">
import Input from "$lib/shared/forms/input.svelte";
import DropDown from "$lib/shared/dropdown/DropDown.svelte";
import { createSmsCodeStore } from "$lib/store/smsStore";
import { createEventDispatcher, getContext, onMount } from "svelte";
import Button from "$lib/shared/button/index.svelte";
import { bindPhoneInfo } from "$lib/features/stage1/kuaishou/store";

import { bindOtherPhoneAndLogin, notBindAndLogin, requestCodeByAccountV2 } from "@ks/general-sso";
import { getCountryCodeList } from "@ks/sso";
import { EnumLoginType, EnumErrorType } from "$lib/shared/const";
import PhoneInput from "$lib/features/components/PhoneInput.svelte";
import IconLeftArrow from "$lib/utils/IconLeftArrow.svelte";

const configStore = getContext('loginPageConfig');
let inputClass = $configStore.cAccountConfig?.inputClass || '';

const dispatch = createEventDispatcher();
const store = createSmsCodeStore();

const onSuccess = getContext('onSuccess');
const onFail = getContext('onFail');
const onLoginStart = getContext('onLoginStart');

const currentStage = $configStore.currentMainState;

let codeClass = '';
$: {
    if ($store.phone?.length <= 11 && $store.phone?.length >= 8) {
        codeClass = 'activate';
    } else {
        codeClass = '';
    }
}

let loginErrorMsg = '';

onMount(() => {
    getCountryCodeList().then((res) => {
        console.log('r', res.countryCodeList.map(item => ({
            label: item.chineseName,
            value: item.countryCode,
            key: item.englishName,
        })));
        store.setValues({
            countryCodeList: res.countryCodeList.map((item, index) => ({
                label: item.chineseName,
                value: item.countryCode,
                key: item.englishName + index,
            })),
        });
    });
});

let sendCodeClicked = false;
async function sendCodeAction() {
    if (!store.reducers.validateSend() || $store.countDown > 0) {
        return;
    }
    try {
        await requestCodeByAccountV2({
            type: 1483,
            account: $store.phone as string,
            countryCode: $store.countryCode,
        });
        sendCodeClicked = true;
        store.setValues({
            countDown: 60,
        });
        const interal = setInterval(() => {
            if ($store.countDown <= 0) {
                clearInterval(interal);
                return;
            }
            store.setValues({
                countDown: $store.countDown - 1,
            });
        }, 1000);
    } catch (error) {
        console.log('error', error);
        loginErrorMsg = error.error_msg || '网络错误，请稍后重试';
        onFail({
            loginType: EnumErrorType.SendCodeError,
            error,
        });
    }
}


let buttondisabled = false;
async function bindAction() {
    if (!store.reducers.validateLogin()) {
        return;
    }
    try {
        onLoginStart({
            name: EnumLoginType.BindB,
        });
        buttondisabled = true;
        const res = await bindOtherPhoneAndLogin({
            phone: $store.phone,
            smsCode: $store.smsCode,
            countryCode: $store.countryCode,
            authToken: $bindPhoneInfo.bindAuthToken,
        });
        buttondisabled = false;
        onSuccess({
            loginType: EnumLoginType.BindB,
            result: res,
        })
    } catch (e) {
        buttondisabled = false;
        loginErrorMsg = e?.error_msg || '网络错误，请稍后重试';
        console.log('phonebind error', e);
        onFail({
            loginType: EnumLoginType.BindB,
            error: e,
        });
    }
}

function backToLogin() {
    dispatch('backToLogin');
}

let buttonText = '快手商家账号登录'

async function notBind() {
    try {
        onLoginStart({
            name: EnumLoginType.NotBindB,
        });
        buttondisabled = true;
        buttonText = '暂不绑定手机号，登录中';
        const res = await notBindAndLogin($bindPhoneInfo.bindAuthToken);
        buttondisabled = false;
        onSuccess({
            loginType: EnumLoginType.NotBindB,
            result: res,
        });
    } catch (e) {
        buttondisabled = false;
        buttonText = '快手商家账号登录';
        onFail({
            loginType: EnumLoginType.NotBindB,
            error: e,
        });
    }
}


</script>

<div class="mb-[24px] mt-[48px] flex items-center">
    <PhoneInput
            smsStore={store}
            on:focus={() => {
                console.log('onfocus');
                store.setValues({
                    phoneErrorMsg: ''
                })
            }}
            class={inputClass}
    />
</div>
<div class="component-select-items relative z-10">
    <DropDown
            class="absolute w-full top-[-20px] currentC"
            visible={$store.dropdownVisible}
            options={$store.countryCodeList}
            current={$store.highlightIndex}
            renderItem={(label, value) => {
                    return `<span style="display: flex; justify-content: space-between; font-size: 14px">
                              <span>${value}</span>
                              <span>${label}</span>
                            </span>`
                }}
            onSelect={store.reducers.onSelectWrapper}
    />
</div>
{#if $store.phoneErrorMsg}
    <div class="text-[12px] leading-[20px] text-left text-[#F93920] mt-[-24px] mb-[4px]">{$store.phoneErrorMsg}</div>
{/if}
<div class="mb-[44px]">
    <Input placeholder="请输入验证码"
           bind:value={$store.smsCode}
           on:blur={store.reducers.onSmsCodeBlur}
           maxlength={6}
           class={inputClass}
           errorMsg={$store.smsCodeErrorMsg}
           globalErrorMsg={loginErrorMsg}
    >
        <slot slot="right">
            <div class="hover:cursor-pointer send-code font-[400] text-[14px] leading-[22px] currentC"
                 on:click={sendCodeAction}
            >
                {#if $store.countDown}
                    <span class="text-[#C6C6C6ff]">{$store.countDown}s</span>
                {:else}
                    <span class={codeClass}>
                            {#if sendCodeClicked}
                                重新发送
                            {:else}
                                发送验证码
                            {/if}
                        </span>
                {/if}
            </div>
        </slot>
    </Input>
</div>

<Button class="currentB mb-[8px]"
        on:click={bindAction}
        disabled={buttondisabled}
>
    {buttonText}
</Button>

<div class="vice-operate"
>
    <div class="flex items-center gap-[4px] cursor-pointer"
         on:click={backToLogin}
    >
        <IconLeftArrow />
        {currentStage === 3 ? '返回登录' : '返回授权登录'}
    </div>
    <div class="flex items-center gap-[8px]">
        <span class="cursor-pointer"
              on:click={notBind}
        >
                暂不绑定
            </span>
    </div>
</div>

<style>
    .currentB {
        color: var(--colorB);
    }
    .activate {
        color: var(--colorB);
    }
    .send-code {
        color: #C6C6C6ff;
    }
    .vice-operate {
        color: #9C9C9C;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: center;
        vertical-align: top;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
</style>
