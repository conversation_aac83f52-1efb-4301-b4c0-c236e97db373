<!DOCTYPE html>
<html
    data-building-date-time="<%= htmlWebpackPlugin.options.buildingDateTime %>"
    data-git-commit-log="<%= htmlWebpackPlugin.options.gitCommitLog %>"
    lang="zh-CN"
>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <link rel="shortcut icon" href="//var----h5----var.kskwai.com/s1/i/favicon-eb2e5f4482.ico" type="image/x-icon">
    <title>
        <%= htmlWebpackPlugin.options.title %>
    </title>
    <script type="text/javascript">
        var cdn_public_path = '//var----h5----var.kskwai.com';
        (function a() {
            // 视觉稿标准宽度为 414 的页面路径
            var pathListFor414 = [
                '/account-h5/login',
                '/account-h5/one-id-query/first',
                '/account-h5/one-id-query/verify',
                '/account-h5/one-id-query/result',
            ]
            var path = location.pathname;
            var standardUIWidth = pathListFor414.indexOf(path) > -1 ? 828 : 750;

            var docEl = document.documentElement;
            var width = Math.min(docEl.clientWidth, standardUIWidth);
            var originSize = width / (standardUIWidth / 100);
            docEl.style.fontSize = originSize + 'px';
            // 修正系统字体调整对rem布局的影响
            var realSize = parseFloat(window.getComputedStyle(docEl).fontSize);
            if (originSize !== realSize) {
                var e = originSize * (originSize / realSize);
                docEl.style.fontSize = e + 'px';
            }
            window.addEventListener('resize', a);
        })();
    </script>
</head>

<body>
    <noscript>
        <strong>抱歉，运行本网页需要您开启javascript</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
</body>

</html>
