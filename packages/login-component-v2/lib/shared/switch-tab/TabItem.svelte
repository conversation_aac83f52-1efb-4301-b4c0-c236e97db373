<script>
import { getContext } from 'svelte';
import { writable } from 'svelte/store';
import { twMerge } from 'tailwind-merge';

export let open = false;
export let showTabTip = true;
export let title = '';
export let tabTip = '';
export let activeClasses;
export let inactiveClasses;
export let defaultClass = 'font-[400] flex flex-col items-center';
const ctx = getContext('ctx') ?? {};
const themeColor = getContext('themeColor');
const colorC = themeColor.colorC;

// single selection
const selected = ctx.selected ?? writable();
function init(node) {
    console.log('tab init', node);
    selected.set(node);
    const destroy = selected.subscribe((x) => {
        if (x !== node) {
            console.log('tab init x', x);
            open = false;
        }
    });
    return { destroy };
}
let buttonClass;
let divClass;
let openClass = '';
$: {
    if (open) {
        openClass = 'active';
    } else {
        openClass = '';
    }
    buttonClass = twMerge(
        defaultClass,
        open ? activeClasses ?? ctx.activeClasses : inactiveClasses ?? ctx.inactiveClasses,
        openClass,
    );
    divClass = twMerge(
        'text-[20px] leading-[28px]',
        openClass,
        activeClasses?.includes('currentC') ? 'currentC' : '',
        activeClasses?.includes('currentB') ? 'currentB' : '',
    );
}
</script>

<div style="--colorC: {colorC}"
         class={divClass}
>
    <span on:click={() => (open = true)}
            class={buttonClass}
            {...$$restProps}
    >
        <span class="leading-[28px] mb-[2px]"><slot name="title">{title}</slot></span>
        <span class={`bar ${openClass}`}></span>
    </span>

    {#if open}
        <div use:init>
            {#if showTabTip}
                <div class="tab-tip">{tabTip}</div>
            {/if}
            <slot />
        </div>
    {/if}
</div>

<style>
    span.bar {
        width: 32px;
        height: 4px;
        border-radius: 4px;

    }
    span.currentB span.bar {
        background: var(--colorB);
    }
    span.currentC span.bar {
        background: var(--colorC);
    }
    .tab-tip {
        color: #9C9C9C;
        font-size: 12px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        height: 20px;
        text-align: left;
        vertical-align: top;
    }

</style>
