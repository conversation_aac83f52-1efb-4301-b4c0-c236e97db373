<script lang="ts">
    import { createEventDispatcher, getContext } from "svelte";
    import Modal from "$lib/shared/toast/Modal.svelte";
    import { CODE_NEED_BIND, EnumLoginType, EnumPageState } from "$lib/shared/const";
    import { chooseUser } from "@ks/sso";
    import { useToast } from "$lib/shared/toast/toastStore";
    import { loginMultiUserInfo } from "$lib/store/multiUserInfo";

    let multiUserToken: string;
    let userInfos;
    let phone: string;
    let loginType: string;
    let countryCode: string;
    const dispatch = createEventDispatcher();


    export let isOpen: boolean = false;
    export let onClose: () => void;

    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    const onLoginStart = getContext('onLoginStart');

    const toast = useToast();


    loginMultiUserInfo.subscribe((value) => {
        multiUserToken = value.multiUserToken;
        userInfos = value.userInfos;
        phone = value.phone;
        loginType = value.loginType;
        countryCode = value.countryCode;
    })

    let currentUser;
    let errorMsg = '';
    async function submit(userId: string) {
        currentUser = userId;
        try {
            onLoginStart({
                name: EnumLoginType.ChooseC,
            });
            const result = await chooseUser({
                userId: currentUser,
                multiUserToken: multiUserToken,
                account: phone,
                countryCode,
            });
            onSuccess({
                loginType: EnumLoginType.ChooseC,
                result,
            });
        } catch (e) {
            // if (e?.result === CODE_NEED_BIND) {
            //     // 需要跳转到绑定手机号页面
            //     bindPhoneInfo.set({
            //         phone: e.bindInfo.phone,
            //         bindAuthToken: e.bindInfo.bindAuthToken,
            //         agreements: e.bindInfo.agreements,
            //         quickLoginResponse: e,
            //     });
            //     dispatch('updatePageState', EnumPageState.bindPhone);
            //     return;
            // }
            if (e?.result === CODE_NEED_BIND) {
                onFail({
                    loginType: loginType,
                    error: e,
                });
                return;
            }
            toast.show({
                message: e?.error_msg || '未知错误，请稍后再试',
            })
            onFail({
                loginType: EnumLoginType.ChooseC,
                error: e,
            });
        }
        onClose();
    }

</script>

<Modal open={isOpen}
       enableBackdrop={true}
       placement="bottom-center"
>
    <div class="bottom-alert">
        <div
                class="close-x"
                on:click={onClose}
        >
            ×
        </div>
        <div class="bottom-alert-title">请选择登录账号</div>
        <div class="bottom-alert-content">
            {#each $loginMultiUserInfo.userInfos as user}
                <div
                        class="choose-user-list-item"
                        class:selected={user.userId === currentUser}
                        on:click={() => submit(user.userId)}>

                    <img src={user.headUrl} />
                    <div class="flex flex-col gap-[6px]">
                        <div class="choose-user-list-item-name">{user.name}</div>
                        <div class="choose-user-list-item-id">快手ID：{user.userId}</div>
                    </div>
                </div>
            {/each}
        </div>
    </div>
</Modal>

<style>
    .bottom-alert {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        background: white;
        border-top-left-radius: 16px;
        border-top-right-radius: 16px;
        box-sizing: border-box;
        padding-top: 38px;
        padding-left: 32px;
        padding-right: 32px;
        padding-bottom: 34px;
    }
    .bottom-alert-title {
        color: #000000;
        font-size: 20px;
        font-weight: 500;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 28px;
        text-align: center;
        vertical-align: top;
    }
    .bottom-alert-content {
        color: #9C9C9C;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        text-align: left;
        vertical-align: top;
        margin-top: 34px;
        margin-bottom: 24px;
        word-break: break-all;
        white-space: normal;
        display: flex;
        flex-direction: column;
        gap: 24px;
    }
    .choose-user-list-item {
        display: flex;
        border-radius: 16px;
        padding: 16px;
        padding-top: 15px;
        padding-bottom: 15px;
        border-style: solid;
        border-width: 1px;
        border-color: #EAEAEA;
        cursor: pointer;
        gap: 16px;
        align-items: center;
    }
    .choose-user-list-item img {
        width: 65px;
        height: 65px;
        border-radius: 50%;
    }
    .choose-user-list-item-name {
        font-size: 17px;
        font-weight: 500;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 24px;
        text-align: left;
        vertical-align: top;
        color: #222222ff;
    }
    .choose-user-list-item-id {
        color: #9C9C9C;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: left;
        vertical-align: top;
    }
    .close-x {
        position: absolute;
        top: 10px;
        right: 18px;
        font-weight: bolder;
        font-size: 20px;
    }
</style>
