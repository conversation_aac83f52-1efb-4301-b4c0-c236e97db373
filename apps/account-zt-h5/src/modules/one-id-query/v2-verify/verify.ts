import { Toast, Dialog } from '@ks/sharp-ui';
import { defineComponent, reactive } from '@vue/composition-api';
import { getZtIdentityVerification, startIdentityVerify } from '@/utils/identity-verification';
import { sendSmsCode as sendSmsCodeApi } from '@/api/query-account-by-phone';
import useCountDown from '@/common/hooks/useCountDown';
import { getAccountBindInfo } from '@/api/one-id-query';
import {
    getZtIdentityVerificationInYodaAndBrowser,
    startIdentityVerifyInYodaAndBrowser,
} from '@/utils/identity-verification-in-yoda-and-browser';
import { setTopBarBackgroundColor } from '@/modules/one-id-query/common/utils';
import { throttle } from '@/utils/limit';
import { transformImageFile } from '@/utils/image';
import { COUNTRY_CODE, ONE_ID_QUERY_SID } from '@/common/config/constants';

export default defineComponent({
    components: {},
    created() {
        setTopBarBackgroundColor();
        this.onFaceBtnClick = throttle(this.onFaceBtnClick) as any;
    },
    data() {
        return {
            // 图片
            image: {
                url: '',
                file: null as null | File,
            },
        };
    },
    methods: {
        // 选择图片文件
        selectFile() {
            (this.$refs.selectFileInput as any).click();
        },
        async onFileInput(ev: Event) {
            let file = (ev.target as HTMLInputElement)?.files?.[0];
            if (file instanceof File) {
                file = await transformImageFile(file, 1990);
                if (file.size > 5 * 1024 * 1024) {
                    Toast.error('图片大小不能超过 5MB');
                    return;
                }
                this.image.file = file;
                this.image.url = URL.createObjectURL(file);
            }
        },

        async onFaceBtnClick() {
            if (!this.image.file) {
                Toast.error('请上传手机号入网信息图片');
                return;
            }

            if (!this.checkPhone() || !this.checkSmsCode()) {
                return;
            }

            const getBindInfo = async () => {
                try {
                    const res = await getAccountBindInfo({
                        sid: ONE_ID_QUERY_SID,
                        accessTimeFile: this.image.file!,
                        countryCode: this.phoneInfo.countryCode,
                        phone: this.phoneInfo.phone,
                        smsCode: this.phoneInfo.smsCode,
                        kpn: document.cookie.match(/kpn=(\w+)/)?.[1] || '',
                        ...getZtIdentityVerificationInYodaAndBrowser(),
                    });

                    if (res.result !== 1) {
                        throw res;
                    }

                    // 缓存结果
                    sessionStorage.accountBindInfo = JSON.stringify(res);

                    // 切换到结果页
                    (this as any).$router.push({
                        name: 'one-id-query-result',
                    });
                } catch (res) {
                    if (res?.url) {
                        const r = await startIdentityVerifyInYodaAndBrowser(res.url);
                        if (r) getBindInfo();
                    } else {
                        Dialog.alert({
                            content: res?.error_msg || '网络异常',
                            confirmText: '我知道了',
                        });
                    }
                }
            };

            getBindInfo();
        },
    },

    setup() {
        const phoneInfo = reactive({
            countryCode: COUNTRY_CODE,
            // 手机号码
            phone: '',
            // 短信验证码
            smsCode: '',
        });

        const { start, stop, value: countDown, running: counting, hasRunned } = useCountDown(60);

        function checkSmsCode() {
            if (/^\d{6}$/.test(phoneInfo.smsCode)) {
                return true;
            }

            Toast.error(`请输入${phoneInfo.smsCode ? '6位' : ''}验证码`);

            return false;
        }

        function checkPhone() {
            if (/^1[3-9]\d{9}$/.test(phoneInfo.phone)) {
                return true;
            }

            Toast.error(`请输入${phoneInfo.phone ? '正确的' : ''}手机号`);

            return false;
        }

        async function sendSmsCode() {
            if (!checkPhone()) {
                return;
            }

            const params = {
                sid: ONE_ID_QUERY_SID,
                countryCode: phoneInfo.countryCode,
                phone: phoneInfo.phone,
                type: '53',
                ...getZtIdentityVerification(),
            };

            sendSmsCodeApi(params).then(
                (res) => {
                    start();
                },
                async (err) => {
                    if (err?.url) {
                        const res = await startIdentityVerify(err.url);
                        if (res) sendSmsCode();
                    } else {
                        Toast.error(err?.error_msg || '发送失败，请检查网络');
                    }
                },
            );
        }

        return {
            sendSmsCode: throttle(sendSmsCode),
            phoneInfo,
            countDown,
            counting,
            checkPhone,
            checkSmsCode,
        };
    },
});
