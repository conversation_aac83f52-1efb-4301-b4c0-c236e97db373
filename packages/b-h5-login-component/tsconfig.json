{
    "extends": "@tsconfig/svelte/tsconfig.json",
    "compilerOptions": {
        "target": "ESNext",
        "useDefineForClassFields": true,
        "module": "ESNext",
        "resolveJsonModule": true,
        /**
         * Typecheck JS in `.svelte` and `.js` files by default.
         * Disable checkJs if you'd like to use dynamic types in JS.
         * Note that setting allowJs false does not prevent the use
         * of JS in `.svelte` files.
         */
        "allowJs": true,
        "checkJs": true,
        "isolatedModules": true,
        "baseUrl": ".",
        "paths": {
            "$lib/*": [
                "lib/*"
            ]
        }
    },
    "include": [
        "src/**/*.ts",
        "src/**/*.js",
        "src/**/*.svelte",
        "lib/**/*.ts",
        "lib/**/*.js",
        "lib/**/*.svelte"
    ],
    "references": [
        {
            "path": "./tsconfig.node.json"
        }
    ]
}
