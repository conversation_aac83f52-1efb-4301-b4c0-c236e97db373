<script lang="ts">
import IconKuaiLogo from "$lib/utils/IconKuaiLogo.svelte";
import { appendParams } from "$lib/utils/url";
import { getContext } from "svelte";

const configStore = getContext('loginPageConfig');
const env = getContext('env') || 'production';

const appIdMap = {
    staging: {
        appId: 'ks694117295173644432',
        baseUrl: 'https://open-platform.staging.kuaishou.com/fe/authorization/pc',
    },
    production: {
        appId: 'ks667377170011306237',
        baseUrl: 'https://open.kuaishou.com/fe/authorization/pc',
    }
};

function jumpToCLogin() {
    // 拼接 sid 和 redirect_url，跳转到C端页面
    const url = appendParams(appIdMap[env].baseUrl, {
        app_id: appIdMap[env].appId,
        scope: 'user_info',
        response_type: 'code',
        redirect_uri: appendParams(location.href, {
            loginType: 'KSAuthorize'
        }),
        auth_mode: '1,2,3',
    });
    location.href = url;
}

</script>

{#if $configStore?.currentMainState === 3}
    <div class="flex mt-[24px] items-center">
        <div class="other-login mr-[12px]">其他登录方式</div>
        <div class="w-[24px] h-[24px]">
            <IconKuaiLogo
                    on:click={jumpToCLogin}
            />
        </div>
    </div>
{/if}


<style>
    .other-login {
        color: #9C9C9C;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: center;
        vertical-align: top;
    }
</style>
