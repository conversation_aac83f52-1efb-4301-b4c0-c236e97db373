import { getConfig } from '@/core/configInit';
import { identityVerificationInterceptor } from '@/core/verificationInterceptor';

/**
 * 手机号换绑 – 原手机验证, 短信type-2498
 */
export function verifyRebindOriginPhone(payload: VerifyRebindOriginPhoneData) {
    const config = getConfig();
    return identityVerificationInterceptor(`/pass/${config.appName}/phone/rebind/origin`, {
        sid: config.sid,
        ...payload,
    });
}
export interface VerifyRebindOriginPhoneData {
    smsCode: string;
}

/**
 * 手机号换绑 – 新手机验证 短信type-2498
 */
export function rebindNewPhone(payload: RebindNewPhoneData) {
    const config = getConfig();
    return identityVerificationInterceptor(`/pass/${config.appName}/phone/rebind/new`, {
        sid: config.sid,
        ...payload,
    });
}
export interface RebindNewPhoneData {
    countryCode: string;
    phone: string;
    smsCode: string; // 新手机号验证码
    originCode: string; // 原手机号验证码
}

/**
 * 换绑新邮箱
 */
export function rebindNewEmail(payload: {
    type: number; // 短信/邮箱平台提供的ID
    email: string; // 需要绑定的邮箱
    code: string; // 新邮箱的验证码
    originCode: string; // 当前登录账号的邮箱的验证码
}) {
    const config = getConfig();
    return identityVerificationInterceptor(`/pass/${config.appName}/email/rebind/new`, {
        sid: config.sid,
        ...payload,
    });
}
