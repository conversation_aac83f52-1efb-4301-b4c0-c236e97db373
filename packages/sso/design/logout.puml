登出相关的逻辑
①tokenId: 每一次用户登录都会生成一个唯一的会话id
②iframe消除cookie：
    在浏览器的同域下可以实现
    跨域: 在Chrome无痕/Safari下无效
③token的加密算法: AES加密算法(对称加密算法)
@startuml
'https://plantuml.com/sequence-diagram

autonumber
participant "浏览器" as 浏览器
participant "系统A(局部登录态)" as 系统A
participant "认证中心(全局登录态)\n(缓存登录态:{userId + tokenId: token})" as 认证中心
participant "系统B(局部登录态)" as 系统B

浏览器 -> 系统A: 登出
系统A -> 系统A: 销毁局部登录态
系统A -> 认证中心: 登出
认证中心 -> 认证中心: 销毁全局登录态

浏览器 -> 系统B: 访问
系统B -> 认证中心: 验证局部登录态
认证中心 -> 系统B: 局部登录态失效
系统B -> 浏览器: 重新登录页

@enduml
