import { startIdentityVerification } from '@ks/identity-verification';
import { request } from '@/core/request';
import { generateCallback, signReqParams } from '../utils';
import { getConfig } from '@/core/configInit';
import { getDeviceInfo } from '@/core/deviceInfo';

/**
 * 授权认证公共接口
 */
export type AuthTokenResponse = {
    result: number;
    error_msg?: string;
    sid: string;
    ssecurity: string;
    userId: number;
    stsUrl: string;
    followUrl: string;
    isNewUser: boolean;
    passToken: string;
    userInfos?: any[];
} & {
    [key: string]: string;
};
export interface AuthTokenResult {
    serviceOwnToken?: string;
    result: number;
    error_msg?: string;
    authToken?: string;
    sid?: string;
    callback?: string;
    serviceToken?: string;
    ssecurity?: string;
    userId?: number;
    stsUrl?: string;
    followUrl?: string;
    isNewUser?: boolean;
    multiUserToken?: string;
    userInfos?: any[];
}

export async function getLoginToken(url: string, params: any): Promise<AuthTokenResult> {
    const config = getConfig();
    const currentSid = params.sid ?? config.sid;
    const deviceInfo = await getDeviceInfo();
    if (deviceInfo) {
        // 增加风控参数
        params.__NS_asfinfo = deviceInfo;
    }

    // 请求参数加签
    params = signReqParams(params);

    return request<AuthTokenResponse>(url, params)
        .then(
            ({
                [currentSid + '.at']: authToken,
                sid = currentSid,
                [currentSid + '_st']: serviceToken,
                userId,
                ssecurity,
                stsUrl,
                followUrl,
                isNewUser,
                [currentSid + '.mt']: multiUserToken,
                userInfos = [],
                result,
                error_msg,
                passToken,
                bUserId,
            }) => {
                return {
                    [currentSid + '.at']: authToken,
                    authToken,
                    sid,
                    ssecurity,
                    callback: config.callback ? generateCallback(config.callback, { authToken, sid }) : undefined,
                    [currentSid + '_st']: serviceToken,
                    serviceToken,
                    userId,
                    stsUrl,
                    followUrl,
                    isNewUser,
                    [currentSid + '.mt']: multiUserToken,
                    multiUserToken,
                    userInfos,
                    result,
                    error_msg,
                    passToken,
                    bUserId,
                };
            },
        )
        .catch((res) => {
            const { result } = res;
            // 返回在400001-410999之间时，视为需要快手验证码，会使用统一身份认证SDK打开验证页面
            if (result >= 400001 && result <= 410999) {
                return (
                    startIdentityVerification({
                        url: res.url,
                        language: getConfig().language as Parameters<typeof startIdentityVerification>[0]['language'],
                        qrcodeEnv: getConfig().env,
                        platform: getConfig().platform,
                    })
                        // 滑块验证通过
                        .then((res) => {
                            if (res.result === -999) {
                                // 行为验证码和端外短信验证出现取消，直接向下透传
                                return Promise.reject(res);
                            }
                            return getLoginToken(url, {
                                ...params,
                                ztIdentityVerificationType: res.type,
                                ztIdentityVerificationCheckToken: res.token,
                            });
                        })
                        // 滑块验证失败
                        .catch((res) => {
                            // result=2表示captchaSession 过期，需要重新获取 captchaSession
                            if (res.result === 2) {
                                // 重新获取captchaSession
                                return getLoginToken(url, params);
                            }
                            // 其他错误result视为非常规错误，直接抛出由业务方处理
                            return Promise.reject(res);
                        })
                );
            }
            return Promise.reject(res);
        });
}
