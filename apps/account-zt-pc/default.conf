# THIS is a template for default.conf
# SEE https://hub.docker.com/_/nginx Using environment variables part
# You should add/copy it to /etc/nginx/templates/default.conf.template

server {
    listen       80 default_server;
    listen  [::]:80;
    server_name  _;

    #charset koi8-r;
    access_log  /var/log/nginx/host.access.log  main;

    # 响应健康检查
    location / {
        return 200 OK;
    }

    # 只处理/pc 请求
    location /pc {
        alias /usr/share/nginx/html/pc;
        try_files $uri $uri/ /pc/index.html;
    }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny  all;
    #}
}
