<template>
    <div class="multi-accounts">
        <div v-if="!isSmallWebview" class="navbar">
            <div class="back-icon" @click="back"></div>
        </div>
        <div class="choose-account">
            <div class="tips">
                轻触头像选择登录账号
            </div>
            <div class="separator"></div>
            <div class="accounts">
                <div
                    class="account"
                    @click="chooseAccount(item)"
                    v-for="item in accounts"
                    :key="item.userId"
                >
                    <div class="avator" :style="{'background-image': `url(${item.headUrl})`}"></div>
                    <div class="username">
                        {{item.name}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from '@vue/composition-api';
import { ACCOUNT } from '../types';

export default defineComponent({
    props: {
        accounts: {
            type: Array as PropType<ACCOUNT[]>,
            required: true,
        },
        isSmallWebview: {
            type: Boolean,
            default: false,
        },
    },
    setup(props, { emit }) {
        return {
            back() {
                emit('back');
            },
            chooseAccount(account: ACCOUNT) {
                emit('choose-account', account);
            },
        };
    },
});
</script>

<style lang="less" scoped>
@import "~@/assets/css/mixins.less";
.multi-accounts {
    .navbar {
        position: fixed;
        left: 0.22rem;
        top: 0.04rem;
        .back-icon {
            width: 0.8rem;
            height: 0.8rem;
            background: url(~@/assets/login-back.png) center /100% 100% no-repeat;
        }
    }
    .choose-account {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 0.48rem;
        .tips {
            font-size: 0.48rem;
            line-height: 0.68rem;
        }
        .separator {
            width: 2.4rem;
            height: 1px;
            margin-top: 1.28rem;
            background: #E6E6E6;
        }
        .accounts {
            display: flex;
            justify-content: center;
            margin-top: 1.28rem;
            .account {
                display: flex;
                flex-direction: column;
                align-items: center;
                width: 1.92rem;
                text-align: center;
                &:not(:first-of-type) {
                    margin-left: 1rem;
                }
                .avator {
                    width: 1.44rem;
                    height: 1.44rem;
                    border-radius: 50%;
                    background: center /100% 100% no-repeat;
                }
                .username {
                    width: 100%;
                    margin-top: 0.16rem;
                    font-size: 0.32rem;
                    line-height: 0.48rem;
                    .one-line-ellipsis();
                }
            }
        }
    }
}
</style>
