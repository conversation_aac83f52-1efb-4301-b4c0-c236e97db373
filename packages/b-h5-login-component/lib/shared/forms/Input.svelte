<script lang="ts">
    import IconClear from "$lib/shared/icons/IconClear.svelte";


    export let value: any = undefined;
    export let type = 'text';
    export let errorMsg = '';
    export let onClear = () => {};
    export let onFocus: Function;
    export let onBlur: Function;

    // let iconClicked = false;
    let focused = false;
</script>

<div class="input-wrapper"
>
    <input {...$$restProps}
           bind:value
           on:focus={() => {
               focused = true;
               onFocus && onFocus();
           }}
           on:blur={() => {
               setTimeout(() => {
                   focused = false;
                   onBlur && onBlur();
               });
           }}
           on:click
           on:change
           on:click
           on:contextmenu
           on:keydown
           on:keypress
           on:keyup
           on:mouseover
           on:mouseenter
           on:mouseleave
           on:paste
           on:input
           {...{ type }}
    />
    {#if value && focused}
        <span style="margin-right: 12px">
        <IconClear
                on:click={() => {
                    onClear();
                }}
        />
        </span>
    {/if}
    {#if $$slots.right}
        <slot name="right"></slot>
    {/if}
    {#if errorMsg}
        <div class="error-msg"
        >
            {errorMsg}
        </div>
    {/if}
</div>

<style>
    .input-wrapper {
        border-bottom: 1px solid #EAEAEA;
        display: flex;
        align-items: center;
        position: relative;
    }
    input {
        width: 100%;
        line-height: 24px;
        padding-top: 18px;
        padding-bottom: 18px;
        font-size: 16px;
        font-weight: 400;
        border: none;
        outline: none;
    }
    input::placeholder {
        color: #9C9C9C;
        font-size: 16px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 22px;
    }
    .error-msg {
        position: absolute;
        bottom: -26px;
        color: #FE3421;
        font-size: 13px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 18.2px;
        text-align: left;
        vertical-align: top;
    }
</style>
