import { writable } from 'svelte/store';

type DialogOptions = {
    title?: string;
    message?: string;
    cancelText?: string;
    confirmText?: string;
    onConfirm?: () => void;
    onCancel?: () => void;
};

type DialogState = DialogOptions & {
    open: boolean;
};

const createDialogStore = () => {
    const { subscribe, set, update } = writable<DialogState>({
        open: false,
        title: '',
        message: '',
        cancelText: '取消',
        confirmText: '确认',
    });

    const confirm = (options: DialogOptions) => {
        update(state => ({
            ...state,
            ...options,
            open: true
        }));

        return new Promise<boolean>((resolve) => {
            const handleConfirm = () => {
                options.onConfirm?.();
                resolve(true);
                close();
            };

            const handleCancel = () => {
                options.onCancel?.();
                resolve(false);
                close();
            };

            update(state => ({
                ...state,
                onConfirm: handleConfirm,
                onCancel: handleCancel
            }));
        });
    };

    const close = () => {
        update(state => ({ ...state, open: false }));
    };

    return {
        subscribe,
        confirm,
        close
    };
};

export const dialog = createDialogStore();