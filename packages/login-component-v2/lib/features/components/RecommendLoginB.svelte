<script lang="ts">
import IconKuaiLogo from "$lib/utils/IconKuaiLogo.svelte";
import { createEventDispatcher, getContext } from "svelte";

const configStore = getContext('loginPageConfig');
const isMainAccountPage = getContext('isMainAccountPage');
const switchCurrentIsC = getContext('switchCurrentIsC');

const dispatch = createEventDispatcher();

function switchCurrentState() {
    switchCurrentIsC && switchCurrentIsC?.();
}
</script>

{#if isMainAccountPage && $configStore?.currentMainState === 2}
    <div>
        <div class="recommend-login-wrapper flex w-full items-center">
            <div class="divide-line flex-1"></div>
            <div class="recommend-login">其他登录方式</div>
            <div class="divide-line flex-1"></div>
        </div>
        <div class="mt-[20px] flex gap-[10px] justify-center items-center">
            <IconKuaiLogo allowHover={false} />
            <div class="login-text hover:cursor-pointer"
                 on:click={switchCurrentState}
            >
                快手APP账号授权登录
            </div>
        </div>
    </div>
{/if}

<style>
    .recommend-login-wrapper {
        margin-top: 20px;
        padding-top: 2px;
        padding-bottom: 2px;
    }
    .recommend-login {
        color: #9C9C9C;
        font-size: 12px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: left;
        vertical-align: top;
        padding: 0 16px;
    }
    .divide-line {
        border-bottom: 1px solid #EDEFF2;
        height: 1px;
    }
    .login-text {
        color: #666666;
        font-size: 16px;
        font-weight: 600;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 22px;
        text-align: center;
        vertical-align: top;
    }
</style>
