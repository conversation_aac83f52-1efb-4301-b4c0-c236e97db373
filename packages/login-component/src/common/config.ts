/**
 * 简单页面
 */
export enum ENUM_INDEX_PAGE_SIMPLE {
    /** 扫码登录 */
        'login-qrcode' = 1,
    /** 密码登录 */
        'login-password' = 2,
    /** 验证码登录 */
        'login-verify-sms' = 3,
    /** 注册 */
        'register' = 4,
}

/**
 * 新样式
 */
export enum ENUM_INDEX_PAGE_WITH_QRCODE {
    /** 密码登录 + 二维码 */
        'login-password-qrcode' = 12,
    /** 验证码登录 + 二维码 */
        'login-verify-sms-qrcode' = 13,
    /** 注册 + 二维码 */
        'register-qrcode' = 14,
}


export type IndexPageQrcodeType = keyof typeof ENUM_INDEX_PAGE_WITH_QRCODE
export type IndexPageType = (keyof typeof ENUM_INDEX_PAGE_SIMPLE | IndexPageQrcodeType);

/**
 * 是并列显示二维码类型
 * @param type
 */
export function isLoginQrcodeType(type: IndexPageType) {
    return type in ENUM_INDEX_PAGE_WITH_QRCODE;
}
