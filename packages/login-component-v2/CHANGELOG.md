# @mfe/login-component-v2

## 0.0.5-beta.14

### Patch Changes

- 修复 emailInput 可能为空的情况

## 0.0.5-beta.13

### Patch Changes

- 错误提示长度调至 25

## 0.0.5-beta.12

### Patch Changes

- 修复 scanConfig 为空的情况

## 0.0.5-beta.11

### Patch Changes

- 新增绑定冲突逻辑

## 0.0.5-beta.10

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.14-beta.9

## 0.0.5-beta.9

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.14-beta.8

## 0.0.5-beta.8

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.14-beta.7

## 0.0.5-beta.7

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.14-beta.6

## 0.0.5-beta.6

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.2-beta.2

## 0.0.5-beta.5

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.14-beta.5

## 0.0.5-beta.4

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.2-beta.1

## 0.0.5-beta.3

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.2-beta.0

## 0.0.5-beta.2

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.14-beta.4

## 0.0.5-beta.1

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.14-beta.3

## 0.0.5-beta.0

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.14-beta.2

## 0.0.4

### Patch Changes

- c1cdb7dd: 重置密码的接口替换成无登录态
- Updated dependencies [f7c1ed77]
- Updated dependencies
- Updated dependencies [8dd33263]
- Updated dependencies [264b2f32]
- Updated dependencies [15842a97]
- Updated dependencies [5372b11a]
- Updated dependencies [0b01d712]
- Updated dependencies [e467370c]
- Updated dependencies [5fe80378]
- Updated dependencies [69063c2b]
- Updated dependencies [0977e900]
- Updated dependencies [e467370c]
- Updated dependencies [0b01d712]
- Updated dependencies [15842a97]
- Updated dependencies [3884b7c4]
- Updated dependencies [c1cdb7dd]
  - @ks/general-sso@0.2.13
  - @ks/sso@2.10.1

## 0.0.4-beta.12

### Patch Changes

- 重置密码的接口替换成无登录态

## 0.0.4-beta.11

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.1-beta.9

## 0.0.4-beta.10

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.13-beta.4

## 0.0.4-beta.9

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.1-beta.8

## 0.0.4-beta.8

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.1-beta.7

## 0.0.4-beta.7

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.1-beta.6

## 0.0.4-beta.6

### Patch Changes

- Updated dependencies
- Updated dependencies
  - @ks/sso@2.10.1-beta.5
  - @ks/general-sso@0.2.13-beta.3

## 0.0.4-beta.5

### Patch Changes

- Updated dependencies
- Updated dependencies
  - @ks/sso@2.10.1-beta.4
  - @ks/general-sso@0.2.13-beta.2

## 0.0.4-beta.4

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.13-beta.1

## 0.0.4-beta.3

### Patch Changes

- Updated dependencies
- Updated dependencies
  - @ks/sso@2.10.1-beta.3
  - @ks/general-sso@0.2.12-beta.0

## 0.0.4-beta.2

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.1-beta.2

## 0.0.4-beta.1

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.1-beta.1

## 0.0.4-beta.0

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.1-beta.0

## 0.0.3

### Patch Changes

- 8337a133: bugfix
- 3786b140: 一键绑定返回 authToken
- 42e50931: 支持子账号
- 26cfab53: bugfxi
- b31c3798: UI 走查
- 87404c9a: fix: 修复快速切换 tab 导致接口 loading 的问题
- fa424810: 兼容单一登录方式
- ddbd9947: bugfix
- e02ea3fd: bugfix
- c34d03f5: 提供 reRenderComponent 方法实现重渲染
- 1fd28032: 升级 sso
- ab5e0116: C 端授权失败情况处理
- 8ed2bda5: 导出类型
- 2afa18bf: chore
- 721eb8b0: bugfix
- 27bd3fd0: 扫码兜底文案
- ********: 修正 isMainAccountPage 传值
- 51a38081: bugfix
- d0499e16: 可配置化
- 00e0b5b1: bugfix
- 472b3399: bugfix
- 706984df: 密码登录增加绑定逻辑
- 0bb9964e: bugfix
- 5d198b35: 配置接口失败兜底处理
- 6fa6c96c: reRender 手动清除
- fa5a8872: 增加 prt 环境参数
- acdf8ba2: 增加 setTabIndex 方法
- dc75a291: 扫码适配
- 4ef03be9: 扫码引导文案支持自定义配置
- ccf40f7c: 细节修改
- 53582ad6: onLoginStart 传入 name 参数
- c03e3b0f: reRender 增加参数
- 01209ea2: bugfix
- 9ae9f5b4: 交互功能
- 0a22791c: 上传版本号
- 282fd6e3: setTabIndex 区分 B 端主子账号
- a9532ba3: 逻辑优化
- 5b9b30fb: chore
- 8e3476cc: UI 改动
- ********: 增加类型定义
- 0e1fdcc8: bugfix
- 490a4c0c: 初始化组件时上报日志
- 3af7486d: chore
- 2c4482b0: 联调修改
- c92f5b78: 初始化调整
- 51cc584f: UI
- a5b70477: C 端扫码授权走旧版扫码
- ac54198b: 限制 tailwindcss 全局样式
- 08acbbc6: 高度自适应
- e35a3e9f: 简化 reRender api
- ec10edad: 扫码间距调整
- aacbdfc3: chore
- 9884a477: 最外层不用 tailwind
- b334e5fe: 区分 B 端主子账号
- 611afb89: 完善日志和关键动作
- 23e5fd8d: 先发一版
- 249a4896: 扫码样式修改
- f568699c: fix
- 9d96ac06: chore
- 70c7d62b: C 端重置密码
- 05cb516f: fix
- c72940a2: UI 修改
- 2c9eea23: bugfix
- e9fc8446: bugfix
- d9724cc5: bugfix
- 167faf3c: UI 改版
- 8ed6bd81: B 端登录增加基础协议
- 96e26d51: 电商扫码 qrType 支持自定义
- ac7cfdfc: 增加密码提示
- fddcddc6: chore
- 50ad29a3: 扫码逻辑增加绑定
- Updated dependencies [7686da94]
- Updated dependencies [636fadc8]
- Updated dependencies [84baf3d8]
- Updated dependencies [611afb89]
- Updated dependencies [314bd4fd]
- Updated dependencies [d97835cc]
- Updated dependencies [87404c9a]
- Updated dependencies [5204ea4b]
- Updated dependencies [d97835cc]
- Updated dependencies [826bc58f]
- Updated dependencies [ac613c6f]
- Updated dependencies [167faf3c]
- Updated dependencies [acf1200c]
- Updated dependencies [fe4814fc]
- Updated dependencies [16df36bd]
- Updated dependencies [3c8008da]
- Updated dependencies [167faf3c]
- Updated dependencies [87b9cee7]
- Updated dependencies [ac613c6f]
- Updated dependencies [9ae9f5b4]
- Updated dependencies [dfff78eb]
- Updated dependencies [5b9b30fb]
- Updated dependencies [a5b70477]
- Updated dependencies [df987171]
- Updated dependencies [30c38cdd]
- Updated dependencies [fe4814fc]
- Updated dependencies [175e95f8]
- Updated dependencies [8337a133]
- Updated dependencies [42e50931]
- Updated dependencies [175e95f8]
- Updated dependencies [4470ff32]
- Updated dependencies [cbf491c7]
- Updated dependencies [c92f5b78]
- Updated dependencies [0a22791c]
- Updated dependencies [0a22791c]
- Updated dependencies [7686da94]
- Updated dependencies [7a7ee215]
- Updated dependencies [87404c9a]
- Updated dependencies [16df36bd]
- Updated dependencies [dc75a291]
- Updated dependencies [7a7ee215]
  - @ks/general-sso@0.2.11
  - @ks/sso@2.10.0

## 0.0.3-beta.1

### Patch Changes

- Updated dependencies
- Updated dependencies
  - @ks/sso@2.10.0-beta.28
  - @ks/general-sso@0.2.11-beta.22

## 0.0.3-beta.0

### Patch Changes

- B 端登录增加基础协议

## 0.0.2

### Patch Changes

- 8337a133: bugfix
- 3786b140: 一键绑定返回 authToken
- 42e50931: 支持子账号
- 26cfab53: bugfxi
- b31c3798: UI 走查
- fa424810: 兼容单一登录方式
- ddbd9947: bugfix
- e02ea3fd: bugfix
- c34d03f5: 提供 reRenderComponent 方法实现重渲染
- 1fd28032: 升级 sso
- ab5e0116: C 端授权失败情况处理
- 8ed2bda5: 导出类型
- 2afa18bf: chore
- 721eb8b0: bugfix
- 27bd3fd0: 扫码兜底文案
- ********: 修正 isMainAccountPage 传值
- 51a38081: bugfix
- d0499e16: 可配置化
- 00e0b5b1: bugfix
- 472b3399: bugfix
- 706984df: 密码登录增加绑定逻辑
- 0bb9964e: bugfix
- 5d198b35: 配置接口失败兜底处理
- 6fa6c96c: reRender 手动清除
- fa5a8872: 增加 prt 环境参数
- acdf8ba2: 增加 setTabIndex 方法
- dc75a291: 扫码适配
- 4ef03be9: 扫码引导文案支持自定义配置
- ccf40f7c: 细节修改
- 53582ad6: onLoginStart 传入 name 参数
- c03e3b0f: reRender 增加参数
- 01209ea2: bugfix
- 9ae9f5b4: 交互功能
- 0a22791c: 上传版本号
- 282fd6e3: setTabIndex 区分 B 端主子账号
- a9532ba3: 逻辑优化
- 5b9b30fb: chore
- 8e3476cc: UI 改动
- ********: 增加类型定义
- 0e1fdcc8: bugfix
- 490a4c0c: 初始化组件时上报日志
- 3af7486d: chore
- 2c4482b0: 联调修改
- c92f5b78: 初始化调整
- 51cc584f: UI
- a5b70477: C 端扫码授权走旧版扫码
- ac54198b: 限制 tailwindcss 全局样式
- 08acbbc6: 高度自适应
- e35a3e9f: 简化 reRender api
- ec10edad: 扫码间距调整
- aacbdfc3: chore
- 9884a477: 最外层不用 tailwind
- b334e5fe: 区分 B 端主子账号
- 611afb89: 完善日志和关键动作
- 23e5fd8d: 先发一版
- 249a4896: 扫码样式修改
- f568699c: fix
- 9d96ac06: chore
- 70c7d62b: C 端重置密码
- 05cb516f: fix
- c72940a2: UI 修改
- 2c9eea23: bugfix
- e9fc8446: bugfix
- d9724cc5: bugfix
- 167faf3c: UI 改版
- 96e26d51: 电商扫码 qrType 支持自定义
- ac7cfdfc: 增加密码提示
- fddcddc6: chore
- 50ad29a3: 扫码逻辑增加绑定
- Updated dependencies [7686da94]
- Updated dependencies [636fadc8]
- Updated dependencies [84baf3d8]
- Updated dependencies [611afb89]
- Updated dependencies [314bd4fd]
- Updated dependencies [d97835cc]
- Updated dependencies [5204ea4b]
- Updated dependencies [d97835cc]
- Updated dependencies [826bc58f]
- Updated dependencies [ac613c6f]
- Updated dependencies [167faf3c]
- Updated dependencies [acf1200c]
- Updated dependencies [fe4814fc]
- Updated dependencies [16df36bd]
- Updated dependencies [3c8008da]
- Updated dependencies [167faf3c]
- Updated dependencies [87b9cee7]
- Updated dependencies [ac613c6f]
- Updated dependencies [9ae9f5b4]
- Updated dependencies [dfff78eb]
- Updated dependencies [5b9b30fb]
- Updated dependencies [a5b70477]
- Updated dependencies [df987171]
- Updated dependencies [30c38cdd]
- Updated dependencies [fe4814fc]
- Updated dependencies [175e95f8]
- Updated dependencies [8337a133]
- Updated dependencies [42e50931]
- Updated dependencies [175e95f8]
- Updated dependencies [4470ff32]
- Updated dependencies [cbf491c7]
- Updated dependencies [c92f5b78]
- Updated dependencies [0a22791c]
- Updated dependencies [0a22791c]
- Updated dependencies [7686da94]
- Updated dependencies [7a7ee215]
- Updated dependencies [16df36bd]
- Updated dependencies [dc75a291]
- Updated dependencies [7a7ee215]
  - @ks/general-sso@0.2.11
  - @ks/sso@2.10.0

## 0.0.2-beta.88

### Patch Changes

- 初始化组件时上报日志

## 0.0.2-beta.87

### Patch Changes

- Updated dependencies
- Updated dependencies
  - @ks/sso@2.10.0-beta.27
  - @ks/general-sso@0.2.11-beta.21

## 0.0.2-beta.86

### Patch Changes

- onLoginStart 传入 name 参数

## 0.0.2-beta.85

### Patch Changes

- reRender 增加参数

## 0.0.2-beta.84

### Patch Changes

- chore

## 0.0.2-beta.83

### Patch Changes

- setTabIndex 区分 B 端主子账号

## 0.0.2-beta.82

### Patch Changes

- 增加 setTabIndex 方法

## 0.0.2-beta.81

### Patch Changes

- 扫码引导文案支持自定义配置

## 0.0.2-beta.80

### Patch Changes

- 扫码间距调整

## 0.0.2-beta.79

### Patch Changes

- 电商扫码 qrType 支持自定义

## 0.0.2-beta.78

### Patch Changes

- 最外层不用 tailwind

## 0.0.2-beta.77

### Patch Changes

- 限制 tailwindcss 全局样式

## 0.0.2-beta.76

### Patch Changes

- C 端扫码授权走旧版扫码
- Updated dependencies
  - @ks/sso@2.10.0-beta.26

## 0.0.2-beta.75

### Patch Changes

- Updated dependencies
- Updated dependencies
  - @ks/sso@2.10.0-beta.25
  - @ks/general-sso@0.2.11-beta.20

## 0.0.2-beta.74

### Patch Changes

- 增加 prt 环境参数

## 0.0.2-beta.73

### Patch Changes

- reRender 手动清除

## 0.0.2-beta.72

### Patch Changes

- 简化 reRender api

## 0.0.2-beta.71

### Patch Changes

- 提供 reRenderComponent 方法实现重渲染

## 0.0.2-beta.70

### Patch Changes

- 增加类型定义

## 0.0.2-beta.69

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.11-beta.19

## 0.0.2-beta.68

### Patch Changes

- bugfix

## 0.0.2-beta.67

### Patch Changes

- bugfix

## 0.0.2-beta.66

### Patch Changes

- chore

## 0.0.2-beta.65

### Patch Changes

- bugfix

## 0.0.2-beta.64

### Patch Changes

- chore

## 0.0.2-beta.63

### Patch Changes

- 一键绑定返回 authToken

## 0.0.2-beta.62

### Patch Changes

- 扫码样式修改

## 0.0.2-beta.61

### Patch Changes

- 扫码兜底文案

## 0.0.2-beta.60

### Patch Changes

- Updated dependencies
- Updated dependencies
  - @ks/sso@2.10.0-beta.24
  - @ks/general-sso@0.2.11-beta.18

## 0.0.2-beta.59

### Patch Changes

- UI 走查

## 0.0.2-beta.58

### Patch Changes

- Updated dependencies
- Updated dependencies
  - @ks/sso@2.10.0-beta.23
  - @ks/general-sso@0.2.11-beta.17

## 0.0.2-beta.57

### Patch Changes

- chore

## 0.0.2-beta.56

### Patch Changes

- UI

## 0.0.2-beta.55

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.11-beta.16

## 0.0.2-beta.54

### Patch Changes

- 先发一版

## 0.0.2-beta.53

### Patch Changes

- 兼容单一登录方式

## 0.0.2-beta.52

### Patch Changes

- chore
- Updated dependencies
  - @ks/sso@2.10.0-beta.22

## 0.0.2-beta.51

### Patch Changes

- 配置接口失败兜底处理

## 0.0.2-beta.50

### Patch Changes

- UI 修改

## 0.0.2-beta.49

### Patch Changes

- 高度自适应

## 0.0.2-beta.48

### Patch Changes

- C 端重置密码

## 0.0.2-beta.47

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.0-beta.21

## 0.0.2-beta.46

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.11-beta.15

## 0.0.2-beta.45

### Patch Changes

- 扫码适配

## 0.0.2-beta.44

### Patch Changes

- bugfxi

## 0.0.2-beta.43

### Patch Changes

- bugfix

## 0.0.2-beta.42

### Patch Changes

- bugfix

## 0.0.2-beta.41

### Patch Changes

- 增加密码提示

## 0.0.2-beta.40

### Patch Changes

- chore

## 0.0.2-beta.39

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.0-beta.20

## 0.0.2-beta.38

### Patch Changes

- e0a7575a: bugfix
- Updated dependencies [e0a7575a]
  - @ks/sso@2.10.0-beta.19

## 0.0.2-beta.37

### Patch Changes

- bugfix

## 0.0.2-beta.36

### Patch Changes

- bugfix

## 0.0.2-beta.35

### Patch Changes

- bugfix

## 0.0.2-beta.34

### Patch Changes

- bugfix

## 0.0.2-beta.33

### Patch Changes

- 交互功能
- Updated dependencies
  - @ks/sso@2.10.0-beta.18

## 0.0.2-beta.32

### Patch Changes

- bugfix

## 0.0.2-beta.31

### Patch Changes

- bugfix

## 0.0.2-beta.30

### Patch Changes

- 8ee21f28: bugfix

## 0.0.2-beta.29

### Patch Changes

- fix

## 0.0.2-beta.28

### Patch Changes

- fix

## 0.0.2-beta.27

### Patch Changes

- UI 改动

## 0.0.2-beta.26

### Patch Changes

- UI 改版
- Updated dependencies
- Updated dependencies
  - @ks/general-sso@0.2.11-beta.14
  - @ks/sso@2.10.0-beta.17

## 0.0.2-beta.25

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.11-beta.13

## 0.0.2-beta.24

### Patch Changes

- 联调修改

## 0.0.2-beta.23

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.11-beta.12

## 0.0.2-beta.22

### Patch Changes

- 初始化调整
- Updated dependencies
  - @ks/sso@2.10.0-beta.16

## 0.0.2-beta.21

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.0-beta.15

## 0.0.2-beta.20

### Patch Changes

- Updated dependencies
- Updated dependencies
  - @ks/sso@2.10.0-beta.14
  - @ks/general-sso@0.2.11-beta.11

## 0.0.2-beta.19

### Patch Changes

- 可配置化

## 0.0.2-beta.18

### Patch Changes

- Updated dependencies
- Updated dependencies
  - @ks/general-sso@0.2.11-beta.10
  - @ks/sso@2.10.0-beta.13

## 0.0.2-beta.17

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.11-beta.9

## 0.0.2-beta.16

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.0-beta.12

## 0.0.2-beta.15

### Patch Changes

- 完善日志和关键动作
- Updated dependencies
  - @ks/sso@2.10.0-beta.11

## 0.0.2-beta.14

### Patch Changes

- C 端授权失败情况处理

## 0.0.2-beta.13

### Patch Changes

- 修正 isMainAccountPage 传值

## 0.0.2-beta.12

### Patch Changes

- 区分 B 端主子账号

## 0.0.2-beta.11

### Patch Changes

- 逻辑优化

## 0.0.2-beta.10

### Patch Changes

- 扫码逻辑增加绑定

## 0.0.2-beta.9

### Patch Changes

- 密码登录增加绑定逻辑

## 0.0.2-beta.8

### Patch Changes

- 1fd28032: 升级 sso
- Updated dependencies
  - @ks/sso@2.10.0-beta.10

## 0.0.2-beta.7

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.0-beta.9

## 0.0.2-beta.6

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.11-beta.8

## 0.0.2-beta.5

### Patch Changes

- 导出类型

## 0.0.2-beta.4

### Patch Changes

- 细节修改

## 0.0.2-beta.3

### Patch Changes

- 支持子账号
- Updated dependencies
  - @ks/sso@2.10.0-beta.8

## 0.0.2-beta.2

### Patch Changes

- 上传版本号
- Updated dependencies
- Updated dependencies
  - @ks/general-sso@0.2.11-beta.7
  - @ks/sso@2.10.0-beta.7

## 0.0.2-beta.1

### Patch Changes

- Updated dependencies
- Updated dependencies
  - @ks/general-sso@0.2.11-beta.6
  - @ks/sso@2.10.0-beta.6

## 0.0.2-beta.0

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.0-beta.5

## 0.0.1

### Patch Changes

- C 端页面扫码授权

## 0.0.1-beta.25

### Patch Changes

- 删除 purgeCss 插件

## 0.0.1-beta.24

### Patch Changes

- 增加 renderAuthorizeComponent

## 0.0.1-beta.23

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.0-beta.4

## 0.0.1-beta.22

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.11-beta.5

## 0.0.1-beta.21

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.11-beta.4

## 0.0.1-beta.20

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.0-beta.3

## 0.0.1-beta.19

### Patch Changes

- Updated dependencies
  - @ks/sso@2.10.0-beta.2

## 0.0.1-beta.18

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.11-beta.3

## 0.0.1-beta.17

### Patch Changes

- 升级至最新版本号

## 0.0.1-beta.16

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.11-beta.2

## 0.0.1-beta.15

### Patch Changes

- feat：bid 组件升级

## 0.0.1-beta.14

### Patch Changes

- Updated dependencies
  - @ks/general-sso@0.2.11-beta.1

## 0.0.1-beta.13

### Patch Changes

- chore: 增加 publish 命令

## 0.0.1-beta.12

### Patch Changes

- 样式修改

## 0.0.1-beta.11

### Patch Changes

- @ks/login-component@3.0.6-beta.11

## 0.0.1-beta.10

### Patch Changes

- 线上和 staging 扫码配置

## 0.0.1-beta.9

### Patch Changes

- 增加 baseUrl 和登录回调
  - @ks/login-component@3.0.6-beta.10

## 0.0.1-beta.8

### Patch Changes

- @ks/login-component@3.0.6-beta.9

## 0.0.1-beta.7

### Patch Changes

- @ks/login-component@3.0.6-beta.8

## 0.0.1-beta.6

### Patch Changes

- @ks/login-component@3.0.6-beta.7

## 0.0.1-beta.5

### Patch Changes

- @ks/login-component@3.0.6-beta.6

## 0.0.1-beta.4

### Patch Changes

- @ks/login-component@3.0.6-beta.5

## 0.0.1-beta.3

### Patch Changes

- @ks/login-component@3.0.6-beta.4

## 0.0.1-beta.2

### Patch Changes

- @ks/login-component@3.0.6-beta.3

## 0.0.1-beta.1

### Patch Changes

- chore: add alias

## 0.0.1-beta.0

### Patch Changes

- chore: 调整代码结构，引入 vite-plugin-tailwind-purgecss
