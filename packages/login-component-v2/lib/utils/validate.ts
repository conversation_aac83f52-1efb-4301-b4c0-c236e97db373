import { EnumInputLoginType } from "$lib/shared/const";

export const isValidPhone = (phone: string): boolean => {
    const re = /^\+\d{11,13}$|^\d{8,11}$/
    return re.test(phone)
}

export const isValidEmail = (email: string): boolean => {
    const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
    return re.test(String(email).toLowerCase())
}

export const isValidSmsCode = (smsCode: string): boolean => {
    const re = /^(\d{4}|\d{6})$/
    return re.test(smsCode)
}

export const isValidAccount = (account: string, type: EnumInputLoginType) => {
    if (type === EnumInputLoginType.phone) {
        return isValidPhone(account);
    } else if (type === EnumInputLoginType.email) {
        return isValidEmail(account);
    } else {
        return isValidPhone(account) || isValidEmail(account);
    }
}

export function filterInput(event: Event) {
    const input = event.target;
    const value = input?.value;
    // 定义允许的字符范围
    const allowedChars = /[a-zA-Z0-9!"@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`]+$/;

    // 过滤掉非法字符
    input.value = value.replace(/[^a-zA-Z0-9!"@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`]/g, '');

    // 防止默认行为
    if (!allowedChars.test(event.key)) {
        event.preventDefault();
        return true;
    }
    return false;
}

export function validateLowSafety(str: string) {
    if (str.length === 0) {
        return false;
    }

    // 定义正则表达式
    const hasNumbers = /\d/; // 包含数字
    const hasLetters = /[a-zA-Z]/; // 包含字母
    const hasSpecialChars = /[^a-zA-Z0-9]/; // 包含特殊字符

    // 检查字符串中是否包含数字、字母或特殊字符
    const containsNumbers = hasNumbers.test(str);
    const containsLetters = hasLetters.test(str);
    const containsSpecialChars = hasSpecialChars.test(str);

    // 确保字符串只包含一种类型
    const onlyOneType = (containsNumbers && !containsLetters && !containsSpecialChars) ||
        (!containsNumbers && containsLetters && !containsSpecialChars) ||
        (!containsNumbers && !containsLetters && containsSpecialChars);

    return onlyOneType;
}

export function validateMiddleSafety(str: string) {
    // 定义正则表达式
    const hasDigit = /\d/; // 检查是否包含数字
    const hasLetter = /[a-zA-Z]/; // 检查是否包含字母
    const hasSpecialChar = /[^a-zA-Z0-9]/; // 检查是否包含特殊字符

    // 统计包含的类型
    let typeCount = 0;

    if (hasDigit.test(str)) {
        typeCount++;
    }
    if (hasLetter.test(str)) {
        typeCount++;
    }
    if (hasSpecialChar.test(str)) {
        typeCount++;
    }
    // 如果包含两种或以上类型，返回 true
    return typeCount === 2;
}
