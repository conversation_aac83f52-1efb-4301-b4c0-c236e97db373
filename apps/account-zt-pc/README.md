# 帐号中台 PC 端登录相关服务

## 页面说明

- `/pc/account/login`: PC 端统一登录页
- `/pc/account/third-part-login-page/:type`: `type`可以是`qq`或`wechat`，第三方登录页
- `/pc/account/login-component-page`: 基于`@ks/login-component`封装的 PC 端登录页面，可通过调用`@ks/passport-iframe-client`这个 SDK 以弹窗内 iframe 的形式来打开这个页面。但不推荐使用，目前正在让各个已使用该页面的业务方下线，改成直接使用`@ks/login-component`。
- `/pc/login-component-page/`: 同上，这两个路径是等价的，因为历史原因产生了这两个路径
- `/pc/account-qrcode/`: 直播伴侣 windows 客户端开播前的「人脸、实名」二维码
- `/pc/identity/qrcode`: web端「人脸、实名」二维码


## 部署

- [流水线（KFX）](https://halo.corp.kuaishou.com/devcloud/pipeline/history/514643)
- [静态部署](https://halo.corp.kuaishou.com/devcloud/kfx/app/e4a79b8b51/deploy?serviceId=1426540)

## 监控查看

- [雷达监控](https://radar.corp.kuaishou.com/project/6adea114f3/dashboard)

## 功能模块

| 日期       | 模块                 | 说明              |
| ---------- | :------------------- | ----------------- |
| 2021-02-02 | account-qrcode       | 直播伴侣 扫码开播 |
| 2021-10-11 | login-compoennt-page | 车机登录页对接    |

prt: https://zt-passport.prt.kuaishou.com/
online: https://passport.kuaishou.com/
