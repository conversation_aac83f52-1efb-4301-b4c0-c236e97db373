<script lang="ts">
    import Input from "$lib/shared/forms/Input.svelte";
    import IconClear from "$lib/shared/icons/IconClear.svelte";
    import IconSwitch from "$lib/shared/icons/IconSwitch.svelte";
    import IconKuaiLogo from "$lib/shared/icons/IconKuaiLogo.svelte";
    import Agreements from "$lib/features/Agreements.svelte";
    import IconHide from "$lib/shared/icons/IconHide.svelte";
    import IconSee from "$lib/shared/icons/IconSee.svelte";
    import Button from "$lib/shared/button/index.svelte";
    import ChooseUser from "$lib/features/components/ChooseUser.svelte";
    import { createPasswordStore } from "$lib/store/passwordStore";
    import { createEventDispatcher, getContext } from "svelte";
    import { EnumLoginType, CODE_MULTICHOOSE, CODE_CANCEL_CAPTCHA } from "$lib/shared/const";
    import { useToast } from "$lib/shared/toast/toastStore";
    import BottomAlert from "$lib/features/components/BottomAlert.svelte";
    import { passwordLogin as passwordLoginC } from '@ks/sso';
    import { accountPasswordLoginV2 as passwordLoginB } from '@ks/general-sso';
    import { loginMultiUserInfo } from "$lib/store/multiUserInfo";
    import { appendParams } from "$lib/utils/url";

    const toast = useToast();
    const store = createPasswordStore(["phone", "email"]);
    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    const onLoginStart = getContext('onLoginStart');
    const configStore = getContext('loginPageConfig');
    const env = getContext('env');

    const currentMainState = $configStore.currentMainState;

    const dispatch = createEventDispatcher();


    let buttonForbidden = false;
    store.subscribe(() => {
        buttonForbidden = store.reducers.isButtonForbidden();
    });

    let showPassword = false;
    let loginErrorMsg = '';

    let buttondisabled = false;

    async function loginAction() {
        const loginType = currentMainState === 1 ? EnumLoginType.passwordC : EnumLoginType.passwordB;
        onLoginStart({
            name: loginType,
        });
        try {
            buttondisabled = true;
            const res = currentMainState === 1 ? await passwordLoginC({
                account: $store.account,
                password: $store.password,
            }) : await passwordLoginB({
                account: $store.account,
                password: $store.password,
            });
            buttondisabled = false;
            console.log('res is', res, onSuccess);
            onSuccess({
                loginType: loginType,
                result: res,
            });
        } catch (error) {
            buttondisabled = false;
            console.log('error', error);
            if (error?.result === CODE_MULTICHOOSE) {
                // 存在两个账号的情况，需要选择
                loginMultiUserInfo.set({
                    userInfos: error.userInfos,
                    multiUserToken: error.multiUserToken,
                    phone: $store.account,
                    loginType: 'password',
                });
                store.setValues({
                    chooseUserOpen: true,
                });
                return;
            } else if (error?.result === CODE_CANCEL_CAPTCHA) {
                onFail({
                    loginType: loginType,
                    error,
                });
                return;
            }
            loginErrorMsg = error.error_msg || '未知错误，请稍后重试';
            toast.show({
                message: loginErrorMsg,
            });
            onFail({
                loginType: loginType,
                error,
            });
        }
    }
    async function onLogin() {
        if (!store.reducers.validateLogin()) {
            toast.show({
                message: $store.loginErrorMsg,
            });
            return;
        }
        if ($store.agreementChecked) {
            await loginAction();
        } else {
            store.setValues({
                bottomAlertOpen: true,
            });
        }
    }
    async function onAcceptLogin() {
        store.reducers.onBottomAccept();
        await loginAction();
    }

    function goToSmsLogin() {
        dispatch('updateLoginMethod', 'code');
    }
    function goToReset() {
        dispatch('updateLoginMethod', 'reset');
    }

    function closeChooseUser() {
        store.setValues({
            chooseUserOpen: false,
        });
    }

    const appIdMap = {
        staging: {
            appId: 'ks694117295173644432',
            baseUrl: 'https://open-platform.staging.kuaishou.com/fe/authorization/h5',
        },
        production: {
            appId: 'ks667377170011306237',
            baseUrl: 'https://open.kuaishou.com/fe/authorization/h5',
        }
    };

    function jumpToCLogin() {
        console.log('jump to c login');
        // 拼接 sid 和 redirect_url，跳转到C端页面
        const url = appendParams(appIdMap[env].baseUrl, {
            app_id: appIdMap[env].appId,
            scope: 'user_info',
            response_type: 'code',
            redirect_uri: appendParams(location.href, {
                loginType: 'KSAuthorize'
            }),
            auth_mode: '1,2,3',
        });
        location.href = url;
    }
</script>

<div>
    <div class="login-body">
        <Input
                placeholder="请输入邮箱/手机号"
                bind:value={$store.account}
                onClear={() => {
                    store.setValues({
                        account: '',
                    });
                }}
        >
        </Input>
        <Input
                placeholder="请输入密码"
                bind:value={$store.password}
                type={showPassword ? 'text': 'password'}
                maxlength={20}
                onClear={() => {
                    store.setValues({
                        password: '',
                    });
                }}
        >
            <slot slot="right">
                <div class="password-slot-right">
                    {#if showPassword}
                        <IconSee
                                on:click={() => (showPassword = !showPassword)}
                        />
                    {:else}
                        <IconHide
                                on:click={() => (showPassword = !showPassword)}
                        />
                    {/if}
                    <span
                            on:click={goToReset}
                    >
                        忘记密码
                    </span>
                </div>
            </slot>
        </Input>
        <Agreements
                bind:checked={$store.agreementChecked}
        />
        <Button
                on:click={onLogin}
                disabled={buttondisabled}
                forbidden={buttonForbidden}
        >
            登录
        </Button>

        <div class="vice-operate">
            <div style="display: flex"
                 on:click={goToSmsLogin}
            >
                <div class="switch-button">
                    <IconSwitch />
                    验证码登录
                </div>
            </div>
            {#if [2, 3].includes(currentMainState)}
                <div on:click={jumpToCLogin}>
                    <IconKuaiLogo />
                </div>
            {/if}
        </div>
        <BottomAlert
                isOpen={$store.bottomAlertOpen}
                onAccept={onAcceptLogin}
                onReject={store.reducers.onBottomReject}
        />
        <ChooseUser
                isOpen={$store.chooseUserOpen}
                onClose={closeChooseUser}
        />
    </div>
</div>


<style>
    .login-body {
        padding-left: 35px;
        padding-right: 35px;
        margin-top: 40px;
    }

    .common-login-btn {
        background: #326BFB;
        border-radius: 28px;
        opacity: 0.5;

        color: #FFFFFF;
        font-size: 16px;
        font-weight: 500;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 24px;
        text-align: center;
        vertical-align: top;

        padding: 16px 32px;
    }
    .switch-button {
        box-sizing: border-box;
        border-style: solid;
        border-width: 1px;
        border-color: #EAEAEA;
        border-radius: 100px;
        padding: 10px 16px;
        display: flex;
        gap: 8px;
        align-items: center;

        color: #222222;
        font-size: 14px;
        font-weight: 500;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 21px;
        text-align: center;
        vertical-align: top;
    }
    .send-code {
        color: #C6C6C6ff;
        white-space: nowrap;
    }
    .activate {
        color: #326BFBff;
    }
    .vice-operate {
        margin-top: 24px;
        display: flex;
        gap: 10px;
        justify-content: center;
        align-items: center;
    }
    .password-slot-right {
        display: flex;
        white-space: nowrap;
        gap: 12px;
        align-items: center;
    }
    .password-slot-right span {
        color: #326BFB;
        font-size: 16px;
        font-weight: 400;
        line-height: 22px;
        text-align: right;
        vertical-align: middle;
    }
</style>
