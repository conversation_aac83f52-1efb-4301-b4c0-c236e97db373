import { setBaseUrl } from '@/core/request';
import { initKsDeviceInfo } from './deviceInfo';
import { InitParam } from '../types';
import Cookies from 'js-cookie';
import { initRadar } from '@/core/radar';

let config!: InitParam;

/**
 * 设置语言参数
 * @param language
 */
export function setLanguage(language: string) {
    config.language = language;
}

export function init(params: InitParam): void {
    const {
        sid,
        callback,
        env = 'production',
        platform,
        qrType,
        kpn,
        encryptStrategy,
        enableSig4,
        customRequestHeader,
    } = params;
    config = {
        ...config,
        platform,
        sid,
        env,
        appName: params.appName,
        baseUrl: params.baseUrl,
        callback: callback || location.href,
        language: params.language,
        qrType,
        kpn,
        collectConfig: params.collectConfig,
        encryptStrategy,
        enableSig4: enableSig4 ?? false,
        customRequestHeader,
    };

    setBaseUrl(config.baseUrl);
    // setKPN(config.kpn);
    initRadar(sid);

    if (config.collectConfig) {
        // 初始化风控
        initKsDeviceInfo(config.collectConfig);
    }
}

export function getConfig(): InitParam {
    return config;
}

function setKPN(kpn?: string) {
    if (!!kpn) {
        const domain = location.hostname.replace(/.*?(.\w+.\w+)$/, '$1');
        Cookies.set('kpn', kpn, { domain });
    }
}
