<script lang="ts">
    import {getContext} from "svelte";

    export let show = true;
    export let agreementType = 'login';
    export let agreements: Array<{
        title: string;
        url: string;
        onClick?: Function;
    }> = [];

    function onClick(url: string, callback?: Function) {
        callback && callback();
        window.open(url, '_blank');
    }

</script>

{#if show && agreements?.length > 0}
    <div class="agreements">
        {agreementType === 'login' ? '登录' : '注册'}即代表同意
        <div class="agreement"
             on:click={() => onClick(agreements[0].url, agreements[0]?.onClick)}
        >
            &nbsp;{agreements[0].title}&nbsp;
        </div>
        {#each agreements.slice(1) as agreement}
        和<div class="agreement"
               on:click={() => onClick(agreement.url, agreement?.onClick)}
        >
            &nbsp;{agreement.title}&nbsp;
        </div>
        {/each}
    </div>
{/if}
<style>
    .agreements {
        background: #F8F8F8;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding-top: 14px;
        padding-bottom: 14px;
        font-size: 14px;
        font-weight: 400;
        color: #9C9C9C;
        padding-left: 48px;
        padding-right: 48px;
        flex-wrap: wrap;
        line-height: 20px;
        border: 1px solid #F8F8F8;
        border-top: none;
        border-bottom-left-radius: var(--containerRadius);
        border-bottom-right-radius: var(--containerRadius);
    }
    .agreement {
        color: #666666;
        line-height: 20px;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        text-align: left;
        vertical-align: top;
    }
    .agreement:hover {
        cursor: pointer;
    }
</style>
