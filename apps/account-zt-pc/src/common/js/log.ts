import Weblog from '@ks/weblogger';
import type { EventDimension } from '@ks/weblogger/lib/types/plugins/radar/interface';

// 按需引用插件
import Radar from '@ks/weblogger/lib/plugins/radar';

const radar = new Radar({
    // @see  https://radar.corp.kuaishou.com/project/6adea114f3/dashboard
    projectId: '6adea114f3', // 雷达平台上的唯一标识
    sampling: 1, // 上报采样率，1 为全部上报
    // @ts-ignore
    APIHook(apiData) {
        const { request, response, duration } = apiData;
        const data = JSON.parse(response.data);
        return {
            response_code: data.result, // 业务定义的接口返回的code，number类型
            response_msg: data.err_msg || data.message, // 业务定义的接口返回code的解释，string类型，限制100字符以内
            // 业务定义的api是否失败，api成功率依靠这个指标来进行判断。默认值为!(status >= 200 && status < 300)，boolean类型,平台显示的api成功率依赖该字段
            custom_failed: data.result === 1 ? false : true,
        };
    },
});
radar.setDimensions({
    c_dimension1: 'v1',
});

// 初始化 weblogger
export const webLog = new Weblog(
    {
        // 如果项目中有变量替换且值为 production/development，可以直接使用，例如 process.env.NODE_ENV
        env: process.env.NODE_ENV || 'production', // 正式环境
        // 配置插件
        plugins: [
            radar,
        ],
        proto: 'v2', // 上报 PB 格式
        autoPV: false,
    },
    {
        /* eslint-disable max-len */
        /**
         * 产品定义
         * @see https://git.corp.kuaishou.com/data-platform/kuaishou-common-log-proto/-/blob/master/src/main/proto/kuaishou/log/client_log/client_common.proto
         */
        /* eslint-enable max-len */
        product: 30, // KUAISHOU_VISION 快手-PC主站首页
    },
);

export function sendRadarCustomEvent(evt: EventDimension, value?: object) {
    webLog.plugins.radar.event(evt, value);
}

export function sendRadarFmp() {
    webLog.plugins.radar.fmp();
}

webLog.sendImmediately('PV', {
    page: 'ACCOUNT_LOGIN_DEFAULT',
    type: 'enter',
    params: {
        source_url: document.referrer || '',
    },
});

sendRadarCustomEvent({
    name: 'account-zt-pc',
    extra_info: JSON.stringify({
        referrer: document.referrer || '',
    }),
});
