import typescript from 'rollup-plugin-typescript2';
import resolve from 'rollup-plugin-node-resolve';
import commonjs from 'rollup-plugin-commonjs';
// import livereload from 'rollup-plugin-livereload';
import { terser } from 'rollup-plugin-terser';
import json from '@rollup/plugin-json';
import pkg from './package.json';

const IS_PROD = process.env.NODE_ENV === 'production';

export default {
    input: 'src/index.ts',
    output: [{
        file: pkg.module,
        format: 'es',
        sourcemap: true,
    },{
        file: pkg.main,
        format: 'cjs',
        name: 'QrcodeVerify',
        sourcemap: true,
    }],
    plugins: [
        commonjs(),
        json(),
        // ts
        typescript({
            clean: true,
            tsconfig: 'tsconfig.json',
            tsconfigOverride: {
                compilerOptions: {
                    target: 'es2015',
                },
            },
            rollupCommonJSResolveHack: false,
        }),
        resolve({
            browser: true,
        }),
        IS_PROD && terser(),
    ]
}
