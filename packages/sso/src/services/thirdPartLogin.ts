import { APP_ID_MAP } from '../config/enums';
import { privateGetConfig } from '../core/configInit';

export interface ThirdPartLoginInfo {
    type: keyof typeof APP_ID_MAP;
    followUrl: string;
    captchaToken?: string;
}

/**
 * 跳转三方登录页面（API不稳定，未来可能会变动，不建议使用）
 */
export function thirdPartLogin({type, followUrl, captchaToken}: ThirdPartLoginInfo): void {
    const config = privateGetConfig();
    const appId = APP_ID_MAP[type];
    // TODO拼参数得改一下
    const url = [
        config.baseUrl,
        '/pass/kuaishou/login/sns/auth',
        `?appId=${encodeURIComponent(appId)}`,
        `&sid=${encodeURIComponent(config.sid)}`,
        `&__jumpHtml=${encodeURIComponent(followUrl)}`,
    ];
    if (captchaToken) {
        url.push(`&captchaToken=${encodeURIComponent(captchaToken)}`);
    }
    location.replace(url.join(''));
}
