import axios from '@/common/js/axios';
import { LOGIN_API_HOST, LOGIN_LOGO, LOGIN_BACKGROUND } from '@/common/js/const';

export enum LOGIN_CONFIG_BG_TYPE {
    IMAGE,
    /**
     * 预留 视频背景
     */
    VIDEO,
}

export interface PageInfo {
    result: number;
    background: string[];
    callbackUrls: string[];
    logo: string[];
    thirdPartyLogin: boolean;
    type: LOGIN_CONFIG_BG_TYPE // 背景类型，0 是图片，1 是视频
}

export function getLoginPageInfoFetcher(sid: string) {
    return (): Promise<PageInfo> => axios.get<PageInfo>(`${LOGIN_API_HOST}/pass/kuaishou/pc/pageInfo`, {
        params: {
            sid,
        },
    }).then(data => {
        if (data.background.length === 0) {
            data.background.push(
                LOGIN_BACKGROUND,
            );
        }
        if (data.logo.length === 0) {
            data.logo.push(
                LOGIN_LOGO,
            );
        }

        return data;
    }).catch(() => {
        return {
            result: 1,
            error_msg: 'demo',
            background: [
                LOGIN_BACKGROUND,
            ],
            logo: [
                LOGIN_LOGO,
            ],
            type: LOGIN_CONFIG_BG_TYPE.IMAGE,
            thirdPartyLogin: false,
            callbackUrls: ['gifshow.com', 'kuaishou.com'],
        };
    });
}

