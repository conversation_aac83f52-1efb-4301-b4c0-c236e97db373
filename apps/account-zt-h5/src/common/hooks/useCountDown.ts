import { ref, onBeforeUnmount } from '@vue/composition-api';

/**
 * 取得当前时间对应秒数
 */
function getNowSecond() {
    return Math.floor(Date.now() / 1000);
}
/**
 * 生成一个倒计时
 * @param countDownFrom 倒数开始值 单位秒
 */
export default function useCountDown(countDownFrom: number) {
    /** 倒计时开始时间 */
    const FROM = ref(countDownFrom);
    const value = ref(countDownFrom);
    const timer = ref<number>(0);
    const endTime = ref(0);
    const running = ref(false);
    const hasRunned = ref(false);


    function stop() {
        running.value = false;
        value.value = FROM.value;
        timer.value && clearInterval(timer.value);
    }

    function tick() {
        const now = getNowSecond();
        value.value = endTime.value - now;
        if (value.value <= 0) {
            stop();
        }
    }

    function start() {
        stop();
        const now = getNowSecond();
        hasRunned.value = true;
        running.value = true;
        endTime.value = now + FROM.value;
        timer.value = setInterval(() => tick(), 1000);
    }

    onBeforeUnmount(() => {
        stop();
    });

    return {
        start,
        stop,
        /** 倒计时开始时间 */
        value,
        /** 正在运行 */
        running,
        hasRunned,
    };
}