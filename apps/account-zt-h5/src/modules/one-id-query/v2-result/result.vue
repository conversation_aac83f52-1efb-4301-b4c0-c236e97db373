<style scoped lang="less" src="./result.less" />
<script lang="ts" src="./result.ts" />

<template>
    <div class="one-id-query-container">
        <div style="text-align: center; margin-top: 2.13em; margin-bottom: 2em;">
            <div class="phoneNumber">
                {{ accountBindInfo.phone }}
            </div>
            <div style="margin-top: -0.125em; margin-bottom: -0.375em;">
                <span class="gray-text">
                    {{ accountBindInfo.bindAccount && accountBindInfo.bindAccount.length > 0 ? '绑定的快手账号信息如下' : '暂无绑定的快手账号信息' }}
                </span>
            </div>
        </div>

        <div
            v-if="!(accountBindInfo.bindAccount && accountBindInfo.bindAccount.length > 0)"
            style="text-align: center; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);"
        >
            <img src="@/assets/no-record.svg">
            <div style="margin-top: .5em;">
                <span
                    class="black-text"
                    style="font-size: 1.0625em;font-style: normal;font-weight: 500;line-height: 1.5em;"
                >
                    暂无记录
                </span>
            </div>
        </div>

        <template v-for="account of accountBindInfo.bindAccount">
            <div class="white-panel" style="margin: 1em auto; padding: 0.8421em; line-height: 1;">
                <table class="bind-info-table">
                    <tbody>
                        <tr>
                            <td class="nick-name-td">
                                <span class="gray-text">账号昵称：</span>
                            </td>
                            <td>
                                <span class="black-text">{{ account.nickName }}</span>
                            </td>
                            <td :rowspan="2" class="unbind-btn-td">
                                <template v-if="account.__fe_unbound">
                                    <div class="unbound-btn">解绑成功</div>
                                </template>
                                <template v-else>
                                    <div class="unbind-btn" @click="onUnbindBtnClick(account.userId);">解绑</div>
                                </template>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <span class="gray-text">绑定时间：</span>
                            </td>
                            <td>
                                <span class="black-text">{{ account.bindTime || '-' }}</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </template>
    </div>
</template>
