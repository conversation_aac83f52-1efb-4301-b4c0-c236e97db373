<script lang="ts">
    import { getContext, createEventDispatcher, onDestroy } from 'svelte';
    import { cancelQrLogin, getQRLoginInfo, getUserLoginInfo, scanQRLoginResult } from '@ks/sso';
    import { onMount } from "svelte";
    import { bindPhoneInfo } from "$lib/features/stage1/kuaishou/store";
    import { EnumPageState, EnumLoginType } from "$lib/shared/const";
    import IconPass from "$lib/utils/IconPass.svelte";
    import { CODE_NEED_BIND, CODE_NEED_BIND_V2 } from "$lib/shared/const";
    import { twMerge } from "tailwind-merge";

    export let height:number;
    export let displayType = 'absolute';

    let scanTextClass = twMerge(
        'text-[12px] font-[400] leading-[20px] flex flex-col items-center',
        displayType === 'absolute' ? 'mt-[24px]' : 'mt-[16px]',
    );

    // let containerClass = twMerge(
    //     'flex flex-col items-center',
    //     // displayType === 'absolute' ? '' : 'pt-[28px]',
    // );

    const dispatch = createEventDispatcher();

    const configStore = getContext('loginPageConfig');
    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    const onLoginStart = getContext('onLoginStart');

    let status = 'scan';
    let qrLoginInfo = {};
    let errorMsg = '';

    async function start() {
        onLoginStart({
            name: EnumLoginType.scanC,
        });
        console.log('start', start);
        // 重新开始的时候 清除一下错误
        // setErrorMsg('');
        cancelQrLogin();
        try {
            qrLoginInfo = await getQRLoginInfo();
            status = 'scan';
            // dispatch('code-need-refresh', status);
            // 这块的res可能为user(老扫码) 或者 {qrLoginSignature: 'xxx'}(新扫码)
            const res = await getUserLoginInfo({
                qrLoginSignature: qrLoginInfo.qrLoginSignature,
                qrLoginToken: qrLoginInfo.qrLoginToken,
            });
            status = 'confirm';
            const result = await scanQRLoginResult({
                // 这块扫码有两套 新的扫码优先使用 scanResult 返回的签名
                // 老的扫码用 start 返回的签名
                qrLoginSignature: res.qrLoginSignature || qrLoginInfo.qrLoginSignature,
                qrLoginToken: qrLoginInfo.qrLoginToken,
            });
            console.log('result', result);
            onSuccess({
                loginType: EnumLoginType.scanC,
                result,
            });
        } catch (e) {
            console.log('e', e);
            if ([CODE_NEED_BIND, CODE_NEED_BIND_V2].includes(e?.result)) {
                bindPhoneInfo.set({
                    phone: e.bindInfo.phone,
                    bindAuthToken: e.bindInfo.bindAuthToken,
                    quickLoginResponse: e,
                });
                dispatch('updatePageState', EnumPageState.bindPhone);
                return;
            }
            onFail({
                loginType: EnumLoginType.scanC,
                error: e,
            });
            status = 'timeout';
        }
    }
    onMount(() => {
        start();
    });
    onDestroy(() => {
        cancelQrLogin();
    });
</script>

<div class="qrcode-container"
     style="height: {height}px"
>
    {#if displayType === 'absolute'}
        <div class="qrcode-title">扫码登录</div>
    {/if}
    <div class="qrcode-img-wrapper">
        {#if qrLoginInfo.imageData}
            <img
                class="qrcode-img"
                class:mask={errorMsg || ['confirm', 'timeout'].includes(status)}
                src="data:image/png;base64,{qrLoginInfo.imageData}"
                alt="qrcode"
            />
        {/if}
        <div class="qrcode-status qrcode-status-{status}"
        >
            {#if errorMsg}
                <div class="text-[14px] font-[600] leading-[22px]">{errorMsg+'二维码已过期'}</div>
                <div class="qrcode-refresh bg-[#1D59F2] w-[64px] text-[#FFFFFF] text-[12px] font-[400] leading-[20px] py-[2px] currentC hover:cursor-pointer"
                     on:click={start}>
                    刷新
                </div>
            {:else if status === 'confirm'}
                <IconPass />
                <p class="qrcode-desc">扫码成功，请确认登录</p>
            {:else if status === 'timeout'}
                <strong>二维码已过期</strong>
                <div class="scan-button"
                     on:click={start}>
                    刷新
                </div>
            {/if}
        </div>
    </div>
    <div class={scanTextClass}>
        {#if $configStore?.cAccountConfig?.scanConfig?.rawDescHTML}
            {@html $configStore?.cAccountConfig?.scanConfig?.rawDescHTML}
        {:else}
            <div>打开 <span class="appName">快手</span> 或 <span class="appName">快手极速版</span> APP</div>
            <div>点击左上角侧边栏 扫一扫</div>
        {/if}
    </div>
</div>

<style>
    span.appName {
        color: var(--colorC);
    }
    .qrcode-status {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;

        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 8px;
    }
    .mask {
        opacity: 0.05;
    }
    .qrcode-desc {
        color: #000000;
        font-size: 14px;
        font-weight: 600;
        line-height: 22px;
        text-align: left;
        vertical-align: top;
    }
    .currentC {
        background: var(--colorC);
    }
    .qrcode-container {
        /*padding: 48px;*/
        width: 100%;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
    }
    .qrcode-title {
        color: #000000;
        font-size: 20px;
        font-weight: 600;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 28px;
        text-align: left;
        vertical-align: top;
        margin-bottom: 22px;
    }
    .scan-button {
        background: var(--colorC);
        border-radius: var(--buttonRadius);
        padding-top: 2px;
        padding-bottom: 2px;
        padding-left: 20px;
        padding-right: 20px;

        color: #FFFFFF;
        font-size: 12px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: left;
        vertical-align: middle;
    }
    .scan-button:hover {
        cursor: pointer;
    }
    .timeout {
        color: #222222;
        font-size: 14px;
        font-weight: 600;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 22px;
        text-align: left;
        vertical-align: top;
    }
    .qrcode-img-wrapper {
        position: relative;
        height: 100%;
        aspect-ratio: 1;
        max-height: 240px;
        border: 1px solid #E6E9ED;
        border-radius: var(--buttonRadius);
    }
    .qrcode-img {
        width: 100%;
        max-width: 240px;
        border-radius: var(--buttonRadius);
    }
</style>
