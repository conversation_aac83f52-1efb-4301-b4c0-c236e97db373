<script lang="ts">
    import { twMerge } from 'tailwind-merge';
    import ToolbarButton from '$lib/shared/toolbar/ToolbarButton.svelte';
    export let name: string = 'IconPass';
    export let width = 36;
    export let height = 36;
</script>

<ToolbarButton on:click {name} {...$$restProps} class={twMerge($$props.class)} let:svgSize
>
    <svg xmlns="http://www.w3.org/2000/svg" width={width} height={width} fill="none" viewBox="0 0 36 36">
        <path fill="#078F59" fill-rule="evenodd" d="M3 18C3 9.716 9.716 3 18 3c8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15Zm14.242 5.242 9-9-1.485-1.485-8.257 8.258-4.508-4.508-1.485 1.485 5.25 5.25.743.743.742-.742Z" clip-rule="evenodd"/>
    </svg>
</ToolbarButton>

<style>
</style>
