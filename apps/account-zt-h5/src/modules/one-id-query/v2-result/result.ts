import Vue from 'vue';

import { Toast, Dialog } from '@ks/sharp-ui';
import { AccountBindInfoResult, unbindForPhone } from '@/api/one-id-query';
import { setTopBarBackgroundColor } from '@/modules/one-id-query/common/utils';
import { ONE_ID_QUERY_SID, COUNTRY_CODE } from '@/common/config/constants';

export default Vue.extend({
    name: 'result',
    data() {
        return {
            accountBindInfo: {} as AccountBindInfoResult,
        };
    },
    created() {
        setTopBarBackgroundColor();
    },
    /** 从 sessionStorage 取回结果 **/
    async mounted() {
        try {
            this.accountBindInfo = JSON.parse(sessionStorage.accountBindInfo);
            // eslint-disable-next-line no-empty
        } catch (e) {}
    },
    /** 解绑后, 将新的信息缓存回 sessionStorage **/
    watch: {
        accountBindInfo: {
            handler(newVal) {
                sessionStorage.accountBindInfo = JSON.stringify(newVal);
            },
            deep: true,
        },
    },
    methods: {
        // 解绑按钮被点击时触发
        async onUnbindBtnClick(userId: number) {
            const choice = await new Promise<boolean>((res) => {
                const dialog = Dialog.confirm({
                    title: '注意',
                    content: '手机号码解绑后，将与此账号解除绑定关系',
                    cancelText: '取消',
                    confirmText: '确认解绑',
                    maskCloseable: false,
                    btns: [
                        {
                            text: '取消',
                            handler() {
                                res(false);
                                dialog.close();
                            },
                        },
                        {
                            text: '确认解绑',
                            color: 'red',
                            handler() {
                                res(true);
                                dialog.close();
                                setTopBarBackgroundColor();
                            },
                        },
                    ],
                });

                dialog.$on('hide', (r: any) => {
                    setTopBarBackgroundColor();
                    res(false);
                });

                setTimeout(() => {
                    setTopBarBackgroundColor('#959595');
                }, 0);

                dialog.$el.querySelectorAll('a')[1].style.color = '#FE3666';
            });

            if (!choice) {
                return;
            }

            try {
                const res = await unbindForPhone({
                    sid: ONE_ID_QUERY_SID,
                    countryCode: COUNTRY_CODE,
                    phone: this.accountBindInfo.phone,
                    userId,
                    verifyToken: this.accountBindInfo.verifyToken,
                    // accessTime: this.accountBindInfo.accessTime,
                    // accessPhone: this.accountBindInfo.accessPhone,
                });
                if (res.result !== 1) {
                    throw res;
                }
                Toast.success('解绑成功!');
                const index = this.accountBindInfo.bindAccount.findIndex((ba) => ba.userId === userId);
                // this.accountBindInfo.bindAccount.splice(index, 1);
                this.$set(this.accountBindInfo.bindAccount[index], '__fe_unbound', true);
            } catch (res) {
                Dialog.alert({
                    title: '手机号与账号解绑失败',
                    content: res?.error_msg || '网络异常',
                    confirmText: '我知道了',
                });
            }
        },
    },
});
