<script lang="ts">
    import { twMerge } from 'tailwind-merge';

    interface Option {
        label: string
        value: string
        key: string
    }

    export let visible = false
    export let tooltips = ''
    export let options: Option[]
    export let current = -1

    export let renderItem = ( label: string, value: string ) => {
        return `<span>${ label }</span>`
    }
    export let onSelect: ( value: string, index: number ) => void
</script>

<div class={twMerge('component-dropdown', $$props.class)}>
    {#if visible}
        <ul>
            {#if tooltips}
                <li class="tooltips">{tooltips}</li>
            {/if}
            {#each options as option, index (option.key)}
                <li
                    on:click={() => {
                        onSelect && onSelect(option.value, index)
                      }}
                    class:active={current === index}
                >
                    {@html renderItem(option.label, option.value)}
                </li>
            {/each}
        </ul>
    {/if}
</div>

<style>
    .component-dropdown ul {
        /*width: 304px;*/
        max-height: 218px;
        overflow: auto;
        margin: 0;
        padding: 4px 0;
        box-sizing: border-box;
        background: #ffffff;
        border-radius: 4px;
        box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 12%), 0 6px 16px 0 rgba(0, 0, 0, 8%), 0 9px 28px 8px rgba(0, 0, 0, 5%);
        list-style: none;
    }
    .component-dropdown li {
        padding: 5px 24px;
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        cursor: pointer;
        text-align: left;
    }
    li.tooltips {
        color: #BFBFBF;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        text-align: left;
        vertical-align: top;
        padding-left: 12px;
    }
    li:hover {
        background-color: #F8F8F8;;
        /*color: gainsboro;*/
    }
    .currentC li.active {
        color: var(--colorC);
    }
    .currentB li.active {
        color: var(--colorB);
    }
</style>
