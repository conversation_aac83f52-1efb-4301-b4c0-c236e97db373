
import { request } from '@/core/request';
import { DEFAULT_COUNTRY_CODE } from '@/config/constants'
import { privateGetConfig } from '@/core/configInit';
import { AuthTokenResult, BaseRes, QuickLoginResult } from '../types/services';
import { getLoginToken } from '../core/loginToken';
import { wrapLoginRequest } from '../core/bidLoginAdapter';
import { genEncryptReqParams } from '@/utils/encrypt';

/**
 * 获取多用户快手账号列表
 */
export interface getKsAccountsRes extends BaseRes {
    multiUserToken?: string;
    userInfos?: Array<{
        userId: number;
        name: string;
        headUrl: string;
    }>;
}

export interface getKsAccountsForPhoneOptions {
    countryCode: string;
    phone: string;
    smsCode: string | number;
}

export interface getKsAccountsForAccountOptions {
    account: string;
    smsCode: string | number;
}

export function getKsAccounts({account, smsCode}: getKsAccountsForAccountOptions): Promise<getKsAccountsRes>;
export function getKsAccounts({countryCode, phone, smsCode}: getKsAccountsForPhoneOptions): Promise<getKsAccountsRes>;
export function getKsAccounts({countryCode = DEFAULT_COUNTRY_CODE, phone, account, smsCode}: any) {
    const config = privateGetConfig();
    return request<getKsAccountsRes>(config.baseUrl + '/pass/kuaishou/pwd/checkKsIdCount', {
        sid: config.sid,
        countryCode,
        phone,
        account,
        smsCode,
    });
}

export interface GetKsAccountsWithoutLoginOptions {
    countryCode?: string;
    phone?: string;
    account?: string;
    smsCode: string | number;
}
export function getKsAccountsWithoutLogin(options: GetKsAccountsWithoutLoginOptions) {
    const config = privateGetConfig();
    const { countryCode, smsCode, account, phone} = options;
    const encryptReqParams = genEncryptReqParams({
        account,
        phone,
    });
    return request<getKsAccountsRes>(config.baseUrl + '/pass/kuaishou/pwd/checkKsIdCountWithoutLogin/v2', {
        sid: config.sid,
        countryCode,
        smsCode,
        ...encryptReqParams,
    });
}

export interface MultiUserInfoForPhone {
    userId: number;
    multiUserToken: string;
    phone: string;
    countryCode?: string;
    setCookie?: boolean;
}

export interface MultiUserInfoForAccount {
    userId: number;
    multiUserToken: string;
    account: string;
    setCookie?: boolean;
}

/**
 * 选择用户账号登录。一般在 login 或 passwordLogin 方法返回 result:********* 的情况下搭配使用
 */
export function chooseUser({userId, multiUserToken, account, setCookie}: MultiUserInfoForAccount): Promise<AuthTokenResult | QuickLoginResult>;
export function chooseUser({userId, multiUserToken, phone, countryCode, setCookie}: MultiUserInfoForPhone): Promise<AuthTokenResult | QuickLoginResult>;
export function chooseUser({userId, multiUserToken, account = '', phone = '', countryCode = DEFAULT_COUNTRY_CODE, setCookie = true}: any) {
    const config = privateGetConfig();
    const r = getLoginToken('/pass/kuaishou/login/multiUserToken', {
        sid: config.sid,
        countryCode,
        phone,
        account,
        targetUserId: userId,
        multiUserToken,
        setCookie,
        // kuaishouAuth: config.kuaishouAuth ? true: undefined,
    });
    return wrapLoginRequest(r);
}

