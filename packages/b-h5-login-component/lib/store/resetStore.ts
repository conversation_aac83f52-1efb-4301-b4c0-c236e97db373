import { EMAIL_SOURCE, EnumInputLoginType } from '$lib/shared/const'
import { writable } from 'svelte/store'
import { filterInput, isValidAccount, isValidPhone } from "$lib/utils/validate";
import {
    resetPasswordByEmail as resetPasswordByEmailC,
    resetPasswordByPhone as resetPasswordByPhoneC,
    requestMobileCode as requestMobileCodeC,
    requestEmailCode as requestEmailCodeC,
    getKsAccounts,
} from "@ks/sso";


export function createResetStore(passwordLoginTypes: string[]) {
    let loginType: EnumInputLoginType
    if (passwordLoginTypes.length === 2) {
        loginType = EnumInputLoginType.phoneAndEmail
    } else if (passwordLoginTypes.includes(EnumInputLoginType.phone)) {
        loginType = EnumInputLoginType.phone
    } else {
        loginType = EnumInputLoginType.email
    }
    let state = {

        account: '',
        accountErrorMsg: '',
        emailInput: undefined as any,
        emailDropdownList: EMAIL_SOURCE,
        emailDropdownVisible: false,
        emailDropdown: null as any,
        emailHighlightIndex: 0,

        password1: '',
        password2: '',
        password1ErrorMsg: '',
        password2ErrorMsg: '',

        smsCode: '',
        smsCodeErrorMsg: '',

        countDown: 0,

        loginType,
        placeholder: loginType === EnumInputLoginType.phoneAndEmail ? '请输入手机号/邮箱' : loginType === EnumInputLoginType.email ? '请输入邮箱' : '请输入手机号',

        loginErrorMsg: '',
        chooseResetOpen: false,
    }

    const { set, update, subscribe } = writable({
        ...state,
    });

    subscribe(value => {
        state = value
    })

    const setValues = (value: Partial<Parameters<typeof set>[0]>) => {
        set({
            ...state,
            ...value,
        })
    }

    const validateAccount = () => {
        let passed = true;
        if (!state.account) {
            setValues({
                loginErrorMsg: state.placeholder,
            });
            passed = false;
        } else if (!isValidAccount(state.account, loginType)) {
            let msg = '手机/邮箱格式不正确';
            if (loginType === EnumInputLoginType.phone) {
                msg = '手机号码格式不正确';
            } else if (loginType === EnumInputLoginType.email) {
                msg = '邮箱格式不正确';
            }
            setValues({
                loginErrorMsg: msg,
            });
            passed = false;
        }
        if (passed) {
            setValues({
                loginErrorMsg: '',
            });
        }
        return passed;
    }

    const validatePassword1 = () => {
        let passed = true;
        if (state.password2 && state.password1 !== state.password2) {
            setValues({
                loginErrorMsg: '您输入的两次密码不一致，请检查',
            });
            passed = false;
        }
        if (!state.password1 || (state.password1?.length < 8 || state.password1?.length > 20)) {
            setValues({
                loginErrorMsg: '请输入8-20个字符的密码',
            });
            passed = false;
        }
        if (passed) {
            setValues({
                loginErrorMsg: '',
            });
        }
        return passed;
    }
    const validatePassword2 = () => {
        let passed = true;
        if (state.password1 !== state.password2) {
            setValues({
                loginErrorMsg: '您输入的两次密码不一致，请检查',
            });
            passed = false;
        }
        if (!state.password2) {
            setValues({
                loginErrorMsg: '请确认密码',
            });
            passed = false;
        } else if (state.password1?.length < 8 || state.password1?.length > 20) {
            setValues({
                loginErrorMsg: '请输入8-20个字符的密码',
            });
            passed = false;
        }
        if (passed) {
            setValues({
                loginErrorMsg: '',
            });
        }
        return passed;
    }

    const validateSmsCode = () => {
        let passed = true;
        if (!state.smsCode) {
            setValues({
                loginErrorMsg: '请输入验证码',
            });
            passed = false;
        } else if (state.smsCode?.length !== 6) {
            setValues({
                loginErrorMsg: '验证码应为6位数字',
            });
            passed = false;
        }
        if (passed) {
            setValues({
                loginErrorMsg: '',
            });
        }
        return passed;
    }

    const reducers = {
        updateInputValue(e: Event) {
            if (filterInput(e)) {
                return;
            }
            let value = e.target.value || ''
            setValues({
                account: value,
                accountErrorMsg: '',
            })
            const emailPrefix = value.slice(0, value.indexOf('@'))

            const emailDropdownList = EMAIL_SOURCE.map(item => {
                return {
                    label: `${emailPrefix}@${item.label}`,
                    value: `${emailPrefix}@${item.value}`,
                    key: `${emailPrefix}@${item.value}`,
                }
            }).filter(item => item.value.startsWith(value))

            const emailDropdownVisible =
                !!emailDropdownList.length &&
                value.indexOf('@') !== -1 &&
                value.indexOf('@') === value.lastIndexOf('@')

            setValues({
                emailDropdownList,
                emailDropdownVisible,
                emailHighlightIndex: 0,
            })
        },

        clearInputValue() {
            setValues({
                account: '',
                accountErrorMsg: '',
            })
        },

        handleEmailSelect(value: string) {
            setValues({
                account: value,
                emailDropdownVisible: false,
            });
            validateAccount();
        },
        updateHighlight(index: number) {
            let emailHighlightIndex: number

            if (index < 0) {
                emailHighlightIndex = state.emailDropdownList.length - 1
            } else if (index > state.emailDropdownList.length - 1) {
                emailHighlightIndex = 0
            } else {
                emailHighlightIndex = index
            }

            setValues({
                emailHighlightIndex,
            })
        },

        validateSend() {
            return validateAccount();
        },

        validateLogin() {
            let bool1 = validateAccount();
            let bool2 = validatePassword1();
            let bool3 = validatePassword2();
            let bool4 = validateSmsCode();
            return bool1 && bool2 && bool3 && bool4;
        },

        filterSmsCode() {
            setValues({
                smsCode: state.smsCode.replace(/[^\d]/g, ''),
            })
        },
        onAccountBlur() {
            validateAccount();
        },
        onPassword1Blur() {
            validatePassword1();
        },
        onPassword2Blur() {
            validatePassword2();
        },
        onSmsCodeBlur() {
            let passed = true;
            if (!state.smsCode) {
                // 空状态不校验
            } else if (state.smsCode?.length !== 6) {
                setValues({
                    smsCodeErrorMsg: '验证码应为6位数字',
                });
                passed = false;
            }
            if (passed) {
                setValues({
                    smsCodeErrorMsg: '',
                });
            }
            return passed;
        },
        isButtonForbidden() {
            // const bool1 =  isValidAccount(state.account, loginType);
            // const bool2 = state.password1?.length >= 8 && state.password1?.length <= 20 && state.password1 === state.password2;
            const bool1 = state.account;
            const bool2 = state.password1 && state.password2;
            const bool3 = state.smsCode?.length === 6;
            return !(bool1 && bool2 && bool3);
        },
        smsCodeSendC: async () => {
            if (isValidPhone(state.account)) {
                return await requestMobileCodeC({
                    phone: state.account,
                    countryCode: '+86',
                    type: 1485,
                });
            } else {
                return await requestEmailCodeC({
                    email: state.account,
                    type: 19,
                })
            }
        },
        resetPhoneC: async ()  => {
            try {
                await getKsAccounts({
                    phone: state.account,
                    countryCode: '+86',
                    smsCode: state.smsCode,
                });
            } catch (e) {
                console.log('throw e', e);
                throw e;
            }
            return await resetPasswordByPhoneC({
                phone: state.account,
                smsCode: state.smsCode,
                password: state.password1,
                targetUserId: 0,
                multiUserToken: '',
            });
        },
        resetEmailC: async () => {
            return await resetPasswordByEmailC({
                email: state.account,
                emailCode: state.smsCode,
                password: state.password1,
            });
        },
        async resetPasswordC() {
            if (isValidPhone(state.account)) {
                return await this.resetPhoneC();
            } else {
                return await this.resetEmailC();
            }
        }
    }

    return {
        update,
        subscribe,
        set,
        setValues,
        reducers,
    }
}
