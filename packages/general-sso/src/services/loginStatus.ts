import { getConfig } from '@/core/configInit';
import { createDevLog, stringify } from '@/utils';
import { PASSPORT_PREFIX, REFRESH_LOGIN_STATUS_TIMEOUT } from '@/config';
import { getLoginToken } from '@/core/loginToken';
import { pageReq } from '@/core/request';

const devLog = createDevLog('general-sso:service');

/**
 * @deprecated
 * 同步token
 */
export function passToken(
    sid?: string,
    options?: {
        baseUrl?: string;
    },
) {
    const config = getConfig();
    return getLoginToken(`/pass/${config.appName}/web/login/passToken`, {
        sid: sid ?? config.sid,
        baseUrl: options?.baseUrl,
    });
}

export interface RefreshLoginParam {
    sid: string;
    passTokenUrl: string;
    stsUrl: string;
    followUrl?: string;
    openFollowUrlInNewWindow?: boolean;
    pullTokenFromCenter?: boolean;
    // 超时时间 单位毫秒
    timeout?: number;
    setRootDomain?: boolean; // 根域下设置登录态
    // pullTokenFromCenter 为 true 的时候，这个参数是必传的！！
    centerAccountDomain?: string;
}

export interface RefreshLoginResult {
    result: number;
    message?: string;
}

export interface MessageEventWithRefreshResult extends MessageEvent {
    data: string;
}

/**
 * 同步登录状态
 *
 * @param {String} param.sid 业务sid
 * @param {String} param.passTokenUrl 当前业务域名可以携带passToken Cookie 的地址
 * @param {String} param.stsUrl 更新种植Cookie 地址
 * @param {String?} param.followUrl [可选] 成功后去往的地址
 * @param {Boolean?} param.pullTokenFromCenter [可选] 使用中心账户，需要 init 配置 bizIdHost。默认值false
 *
 * successUrl [内部使用]通知本函数调用成功
 * failureUrl [内部使用]通知本函数调用失败
 * pullTokenURL 从中心账号拉取token网址
 *
 * iframe 访问网址依次为
 *     (1) passTokenURL
 *     ┣ 错误 →    ┳ pullTokenFromCenter === true → (4)pullTokenURL  ┳ 错误 failureUrl
 *     | 成功      ↓ pullTokenFromCenter ==== false                  ↓ 成功
 *     ↓            failureUrl                                    successUrl
 *     (2) stsURL
 *     ┣ 错误 → failureUrl
 *     ↓ 成功
 *     (3) successUrl
 *
 * 成功后如果有followURL值，则 location.href 跳转 followURL
 *
 * @see 文档地址： https://docs.corp.kuaishou.com/d/home/<USER>
 *
 * @return {Promise} promise 调用成功{result: 1}, 失败{result: 0, message?: 'reason' }
 */
export async function refreshLoginStatus(param: RefreshLoginParam): Promise<RefreshLoginResult> {
    const config = getConfig();
    const {
        passTokenUrl,
        stsUrl,
        sid: paramSid,
        pullTokenFromCenter = false,
        followUrl,
        openFollowUrlInNewWindow = false,
        centerAccountDomain,
    } = param;
    const sid = paramSid ?? config.sid;
    const MSG_ID = 'G_SSO_' + Date.now();
    devLog('params', { passTokenUrl, stsUrl, sid });
    const baseResultUrl =
        config.env === 'production'
            ? `${PASSPORT_PREFIX.production}/pc/account/passToken/result`
            : `${PASSPORT_PREFIX.development}/pc/account/passToken/result`;
    const successUrl = `${baseResultUrl}?${stringify({
        successful: true,
        id: MSG_ID,
    })}`;
    const failureUrl = `${baseResultUrl}?${stringify({
        successful: false,
        id: MSG_ID,
    })}`;
    let passTokenFailureUrl: string;
    if (pullTokenFromCenter) {
        const baseUrl = config?.baseUrl ?? '';
        if (baseUrl === '') {
            console.warn('pullTokenFromCenter mode need config baseUrl in init');
            console.warn('');
        }
        devLog('pullTokenUrl', {
            base: `${centerAccountDomain}/pass/${config.appName}/web/login/pull`,
            callback: successUrl + '&for=pullToken',
            __loginPage: failureUrl + '&for=pullToken',
            sid,
            edgeDomain: config.baseUrl,
        });
        passTokenFailureUrl = `${centerAccountDomain}/pass/${config.appName}/web/login/pull?${stringify({
            sid,
            callback: successUrl + '&for=pullToken',
            __loginPage: failureUrl + '&for=pullToken',
            edgeDomain: config.baseUrl,
        })}`;
    } else {
        passTokenFailureUrl = failureUrl + '&for=pullTokenFail';
    }
    devLog('passTokenFailureUrl', {
        pullTokenFromCenter,
        value: passTokenFailureUrl,
    });
    devLog('passTokenSuccessUrl', {
        base: stsUrl,
        followUrl: successUrl + '&for=passTokenSuccess',
        loginUrl: failureUrl + '&for=passTokenSuccess',
    });
    const passTokenSuccessUrl = `${stsUrl}?${stringify({
        followUrl: successUrl + '&for=passTokenSuccess',
        failUrl: failureUrl + '&for=passTokenSuccess',
        setRootDomain: !!param.setRootDomain,
    })}`;
    devLog('finalPassTokenUrl', {
        base: passTokenUrl,
        callback: passTokenSuccessUrl,
        __loginPage: passTokenFailureUrl,
        sid,
    });
    const finalPassTokenUrl = `${passTokenUrl}?${stringify({
        callback: passTokenSuccessUrl,
        __loginPage: passTokenFailureUrl,
        sid,
    })}`;

    devLog('pass token generated final pass token url: ', finalPassTokenUrl);

    const iframe = document.createElement('iframe');
    iframe.src = finalPassTokenUrl;
    iframe.style.border = '0';
    iframe.style.width = '0';
    iframe.style.height = '0';
    document.body.appendChild(iframe);

    return new Promise((resolve) => {
        const listener = (event: MessageEventWithRefreshResult) => {
            let data;
            devLog('pass token refresh result: ', event);
            try {
                data = JSON.parse(event.data);
            } catch {
                // Vue Devtools 插件会发送 postMessage 信息，但其 event.data 不是个字符串，此处防止报错
            }
            if (data?.id !== MSG_ID) {
                // 忽略其他的
                devLog('ignore msg', data?.id);
                return;
            }
            const successful = data.successful === 'true';
            if (successful && followUrl) {
                if (openFollowUrlInNewWindow) {
                    window.open(followUrl);
                } else {
                    location.href = followUrl;
                }
            }
            const resolveResult: RefreshLoginResult = { result: successful ? 1 : 0 };
            data.failReason && (resolveResult.message = data.failReason);
            resolve(resolveResult);
            document.body.removeChild(iframe);
            window.removeEventListener('message', listener);
        };
        window.addEventListener('message', listener);
        const timeout = param.timeout && param.timeout > 0 ? param.timeout : REFRESH_LOGIN_STATUS_TIMEOUT;
        setTimeout(
            () =>
                resolve({
                    result: 0,
                    message: `timeout (max ${timeout}ms)`,
                }),
            timeout,
        );
    });
}

export function _syncLoginStatus(param: {
    sid?: string;
    followUrl: string;
}) {
    const config = getConfig();
    // 拿到passToken之后调用sync同步登录状态
    return pageReq(`/pass/${config.appName}/web/login/sync`, {
        sid: param?.sid ?? config.sid,
        followUrl: param.followUrl,
    });
}
