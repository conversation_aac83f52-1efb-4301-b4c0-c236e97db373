# @mfe/shell-login

## qrcode-terminal 打包问题

目前`qrcode-terminal` 0.12.0 版本包含了 8 进制字面量，这在严格模式下是禁止的，因此打包时会报错：

`[!] (plugin commonjs--resolver) SyntaxError: Octal literal in strict mode (3:13) in /Users/<USER>/kuaishou/account-zt-general/node_modules/qrcode-terminal/lib/main.js`

目前看有个[PR](https://github.com/gtanner/qrcode-terminal/pull/29/commits/3a20aa45ec9c89653c92c90d25c41cac2d3f7edd)是解决这个问题的，但是并没有被合入。

如果项目打包的话，先按上面的 PR 修改一下`node_modules/qrcode-terminal/lib/main.js`。

## ESLINT 问题

先跳过 ESLINT 检测，之后改成全局的
