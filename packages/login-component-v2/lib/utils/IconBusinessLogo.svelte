<script lang="ts">
    import { twMerge } from 'tailwind-merge';
    import ToolbarButton from '$lib/shared/toolbar/ToolbarButton.svelte';
    export let name: string = 'BusinessLogo';
    export let width = 24;
    export let height = 24;
</script>

<ToolbarButton on:click {name} {...$$restProps} class={twMerge($$props.class)} let:svgSize
>
    <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="24" height="24" rx="12" fill="#326BFB"/>
        <circle cx="12" cy="7.5" r="2.75" stroke="white" stroke-width="1.5"/>
        <path d="M18 18V13.7808C18 13.3219 17.6877 12.9219 17.2425 12.8106L16 12.5L14.7782 12.1946C14.3235 12.0809 13.8509 12.2983 13.6413 12.7175L12.1789 15.6422C12.1052 15.7896 11.8948 15.7896 11.8211 15.6422L10.3587 12.7175C10.1491 12.2983 9.67648 12.0809 9.22178 12.1946L8 12.5L6.75746 12.8106C6.3123 12.9219 6 13.3219 6 13.7808V18" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
    </svg>
</ToolbarButton>

<style>
    path.currentC {
        fill: var(--colorC);
    }
    path.currentB {
        fill: var(--colorB);
    }
</style>
