export const NOT_REGIST_KWAI = 21;

// 101006 未成年人充值
export const TEENAGER_RECHARGE_CODE = 101006;

// ********* h5统一登录多账号返回码
export const MUTI_ACCOUNTS_CODE = *********;

// 国家码 固定为 '+86'
export const COUNTRY_CODE = '+86';

// 发短信验证码：登录 bizType
export const BIZ_TYPE_LOGIN = '53';

// 发短信验证码：换绑 bizType
export const BIZ_TYPE_REBIND = '1483';

// 一证通查 sid
export const ONE_ID_QUERY_SID = 'kuaishou.one.pass.unbind';

// 测试账号：phone/code: *********** / 666666
// 换绑接口：登录失效，109，还有 6001、6002、 -401
export const LOGIN_STATUS_INVALID = [109, 6001, 6002, -401];

