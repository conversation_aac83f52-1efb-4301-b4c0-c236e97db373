import Weblog from '@ks/weblogger/lib/log.browser';
import RadarCore from '@ks-radar/radar-core';
import RadarEventCollect from '@ks-radar/radar-event-collect';

export interface EventDimension {
    name: string;
    category?: string | string[];
    event_type?: string | string[];
    src?: string;
    result_type?: string;
    message?: string;
    extra_info?: object | string;
    yoda_version?: string;
    webview_type?: string;
    is_official?: 1;
}

export interface ActionStartDimension {
    name: string;
    extra_info?: object;
}
export interface ActionEndDimension {
    name: string;
    extra_info?: object;
    result_type?: string;
}

// radar 自定义事件上报实例
let eventCollect: null | RadarEventCollect = null;
export const initRadar = (sid?: string) => {
    setTimeout(() => {
        if (eventCollect) {
            return;
        }
        // webLog 实例
        const weblog = new Weblog(
            {
                env: 'production',
            },
            {
                product_name: 'KUAISHOU',
            },
        );

        // radar 实例
        const radarCore = new RadarCore({
            weblogger: weblog, // 传入weblogger实例
            projectId: 'bee31c7ebb', // 传入项目id
            customDimensions: {
                c_dimension1: sid,
                c_dimension2: 'login-component-v2@' + __PACKAGE_VERSION__,
            }
        });
        eventCollect = new RadarEventCollect({
            core: radarCore,
        });
    })
}
export const sendEvent = (event: EventDimension, value?: object & {
    duration?: number;
    event_count?: number;
}) => {
    if (!eventCollect) {
        initRadar();
    }
    e();
    function e() {
        if (eventCollect) {
            setTimeout(() => {
                eventCollect.event(event, value);
            });
        } else {
            setTimeout(e, 500);
        }
    }
}

export const sendActionStart = (dimension: ActionStartDimension) => {
    if (!eventCollect) {
        initRadar();
    }
    e();
    function e() {
        if (eventCollect) {
            setTimeout(() => {
                eventCollect.action.start(dimension);
            });
        } else {
            setTimeout(e, 500);
        }
    }
}

export const sendActionEnd = (dimension: ActionEndDimension) => {
    if (!eventCollect) {
        initRadar();
    }
    e();
    function e() {
        if (eventCollect) {
            setTimeout(() => {
                eventCollect.action.end(dimension);
            });
        } else {
            setTimeout(e, 500);
        }
    }
}
