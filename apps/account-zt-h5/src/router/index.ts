import Vue from 'vue';
import VueRouter from 'vue-router';
import kwadSDK from '@/modules/kwad-sdk/routes';
import QrScannedH5Page from '@/modules/qr-scanned-h5/routes';
import OneIdQuery from '@/modules/one-id-query/routes';
import CommomLogin from '@/modules/common-login/routes';
import HomeModule from '@/modules/home/<USER>';

Vue.use(VueRouter);

const routes = [
    ...CommomLogin,
    ...HomeModule,
    ...OneIdQuery,  // 工信部: 一证通查页面
    ...kwadSDK,
    ...QrScannedH5Page,
];

const router = new VueRouter({
    base: '/account-h5/',
    mode: 'history',
    routes,
});

export { routes };

export default router;
