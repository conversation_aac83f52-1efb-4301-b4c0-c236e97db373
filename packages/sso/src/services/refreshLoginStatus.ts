
import { privateGetConfig } from '../core/configInit';
import { stringify } from '../utils/utils';
import { createDevLog } from '@/utils/logDev';
import { getZtPassportFrontEndOrigin } from '@ks-passport/url-kit';
import { RefreshLoginResult, MessageEventWithRefreshResult } from '../types/services'

const REFRESH_LOGIN_STATUS_TIMEOUT = 3000 // 3s

const devLog = createDevLog('sso:');

export interface RefreshLoginParam {
    sid: string,
    passTokenUrl: string,
    stsUrl: string,
    followUrl?: string,
    openFollowUrlInNewWindow?: boolean,
    pullTokenFromCenter?: boolean,
    // 超时时间 单位毫秒
    timeout?: number,
    setRootDomain?: boolean; // 根域下设置登录态
}


/**
 * 在隐藏的iframe内刷新登录态
 *
 * @param {String} param.sid 业务sid
 * @param {String} param.passTokenUrl 账号中台 passToken 接口地址，
 *      - 针对快手普通业务填：
 *          - production: `https://id.kuaishou.com/pass/kuaishou/login/passToken`
 *          - development: `https://ksid.test.gifshow.com/pass/kuaishou/login/passToken`
 *          - staging: `https://ksid-staging.corp.kuaishou.com/pass/kuaishou/login/passToken`
 *      - 若用户之前（在本业务或其他业务）登录过，该接口下会存在全局的 passToken Cookie
 *      - 请求该接口，会使用全局 passToken 获取到 authToken
 * @param {String} param.stsUrl 业务域名下的`sts`接口地址，业务方域名注意区分环境
 *      - 填 https://业务方域名/rest/infra/sts，该接口在 nginx 层会转发到账号中台的服务上
 *      - 访问 passToken 接口获取到 authToken 后会 302 到该接口
 *      - 该接口会用 authToken 获取到 serviceToken，并种植 Cookie 到业务方域名下
 * @param {String?} param.followUrl [可选] 登录成功后去往的地址
 * @param {Boolean?} param.pullTokenFromCenter [可选] 使用中心账户，需要 init 配置 bizIdHost。默认值 false
 *      - 注意，这个功能目前后端没上线，先不要用
 *
 * successUrl [内部使用]通知本函数调用成功
 * failureUrl [内部使用]通知本函数调用失败
 * pullTokenURL 从中心账号拉取token网址
 *
 *
 * iframe 访问网址依次为
 *
 *     (1) passTokenURL
 *      |
 *      ├─ fail  -->  pullTokenFromCenter === true
 *      |                         |
 *      |                         ├─ yes  -------------->    (3) pullTokenURL
 *      |                         |                           |
 *      |  success                |                           ├─ fail     -->  failureUrl
 *      |                         |                           |
 *      ↓                         └─ no  -->  failureUrl      └─ success  -->  successUrl
 *     (2) stsURL
 *      |
 *      ├─ fail     -->  failureUrl
 *      |
 *      └─ success  -->  successUrl
 *
 * 1、前端请求 passTokenURL，后续请求 stsURL、pullTokenURL、successUrl、failureUrl 都是通过 302 实现的
 * 2、请求 successUrl 时会种植 cookie
 *
 * 成功后如果有 followURL 值，则 location.href 跳转 followURL
 *
 * @see 文档地址： https://docs.corp.kuaishou.com/d/home/<USER>
 *
 * @return {Promise} promise 调用成功{result: 1}, 失败{result: 0, message?: 'reason' }
 */
export async function refreshLoginStatus(param: RefreshLoginParam): Promise<RefreshLoginResult> {
    const config = privateGetConfig();
    const {
        passTokenUrl,
        stsUrl,
        sid: paramSid,
        pullTokenFromCenter = false,
        followUrl,
        openFollowUrlInNewWindow = false,
    } = param;
    const sid = paramSid ?? config.sid;
    const MSG_ID = 'SSO_' + Date.now();
    devLog("params",{ passTokenUrl, stsUrl, sid });

    // 该地址是个 HTML 的地址，（在 iframe 里）会通过 postMessage 发送登录结果给业务方页面
    const baseResultUrl = `${getZtPassportFrontEndOrigin(config.env)}/pc/account/passToken/result`;

    const successUrl = `${baseResultUrl}?${stringify({
        successful: true,
        id: MSG_ID,
    })}`;

    const failureUrl = `${baseResultUrl}?${stringify({
        successful: false,
        id: MSG_ID,
    })}`;

    // 若不存在全局的 passToken，则调用 passToken 接口请求失败后会 302 重定向到该地址
    let passTokenFailureUrl: string;
    if (pullTokenFromCenter) {
        // 这段逻辑后端没上线，先不管
        const baseUrl = config?.baseUrl ?? '';
        if(baseUrl === '') {
            console.warn('pullTokenFromCenter mode need config baseUrl in init');
        }

        passTokenFailureUrl = `${config.baseUrl}/pass/kuaishou/login/pull?${stringify({
            sid,
            followUrl: successUrl + '&for=pullToken',
            loginUrl: failureUrl + '&for=pullToken',
        })}`;
        devLog("pullTokenUrl", {
            base: `${config.baseUrl}/pass/kuaishou/login/pull`,
            followUrl: successUrl + '&for=pullToken',
            loginUrl: failureUrl + '&for=pullToken',
            sid,
        });
    } else {
        passTokenFailureUrl = failureUrl + '&for=pullTokenFail';
    }
    devLog("passTokenFailureUrl",{
        pullTokenFromCenter,
        value: passTokenFailureUrl
    });

    // sts 接口地址，passToken 接口请求成功后会 302 重定向到该地址
    const passTokenSuccessUrl = `${stsUrl}?${stringify({
        followUrl: successUrl + '&for=passTokenSuccess',
        failUrl: failureUrl + '&for=passTokenSuccess',
        setRootDomain: !!param.setRootDomain,
    })}`;
    devLog("passTokenSuccessUrl",{
        base: stsUrl,
        followUrl: successUrl + '&for=passTokenSuccess',
        loginUrl: failureUrl + '&for=passTokenSuccess',
    });

    // passToken 接口的地址
    const finalPassTokenUrl = `${passTokenUrl}?${stringify({
        callback: passTokenSuccessUrl,
        __loginPage: passTokenFailureUrl,
        sid,
    })}`;
    devLog("finalPassTokenUrl",{
        base: passTokenUrl,
        callback: passTokenSuccessUrl,
        __loginPage: passTokenFailureUrl,
        sid,
    });

    devLog('pass token generated final pass token url: ', finalPassTokenUrl);

    const iframe = document.createElement('iframe');
    iframe.src = finalPassTokenUrl;
    iframe.style.border = '0';
    iframe.style.width = '0';
    iframe.style.height = '0';
    document.body.appendChild(iframe);

    return new Promise((resolve) => {
        const listener = (event: MessageEventWithRefreshResult) => {
            let data;
            devLog('pass token refresh result: ', event);
            // 需要判断 event.data 为字符串再进行下一步
            try {
                data = JSON.parse(event.data);
            } catch {
                // Vue Devtools 插件会发送 postMessage 信息，但其 event.data 不是个字符串，此处防止报错
            }
            if (data?.id !== MSG_ID) {
                // 忽略其他的
                devLog('ignore msg', data?.id);
                return;
            }
            const successful = data.successful === 'true';
            if (successful && followUrl) {
                if (openFollowUrlInNewWindow) {
                    window.open(followUrl);
                } else {
                    location.href = followUrl;
                }
            }
            const resolveResult: RefreshLoginResult = { result: successful ? 1 : 0 };
            data.failReason && (resolveResult.message = data.failReason);
            resolve(resolveResult);
            document.body.removeChild(iframe);
            window.removeEventListener('message', listener);
        };
        window.addEventListener('message', listener);
        const timeout = param.timeout && param.timeout > 0
            ? param.timeout
            : REFRESH_LOGIN_STATUS_TIMEOUT;
        setTimeout(() => resolve({
            result: 0,
            message: `timeout (max ${timeout}ms)`
        }), timeout);
    });
}
