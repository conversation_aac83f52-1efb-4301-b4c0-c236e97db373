export enum ENCRYPT_HEADER_KEYS {
    TIMESTAMP = 'yzzh-tsp', // 时间戳
    NONCE = 'yzzh-nc', // 随机字符串
    VERSION = 'yzzh-vs', // 版本
    VERIFY_SIGNATURE = 'yzzh-vy', // 参数加签
}
export type EncryptHeaders = {
    [ENCRYPT_HEADER_KEYS.TIMESTAMP]: number;
    [ENCRYPT_HEADER_KEYS.NONCE]: string;
    [ENCRYPT_HEADER_KEYS.VERSION]: string;
};

export type EncryptOptions = MixOptions & {
    format?: TextFormat;
    isMix?: boolean;
};

export type EncryptContent = {
    encryptText: string;
    ignoreEncrypt: boolean;
};

export function genEncryptCommonParams(keyVersion: string) {
    const timestamp = getTimeStamp();
    const nonce = genRandomString(10);
    const headers: EncryptHeaders = {
        [ENCRYPT_HEADER_KEYS.TIMESTAMP]: timestamp,
        [ENCRYPT_HEADER_KEYS.NONCE]: nonce,
        [ENCRYPT_HEADER_KEYS.VERSION]: keyVersion,
    };
    const options: EncryptOptions = {
        timestamp,
        nonce,
    };
    return {
        timestamp,
        nonce,
        headers,
        options,
    };
}

/**
 * 获取Unix时间戳
 */
function getTimeStamp(): number {
    const timestamp = new Date().getTime();
    return timestamp;
}

/**
 * 生成随机字符串: nonce   (默认长度10位)
 * @param total
 */
function genRandomString(total: number): string {
    const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
    const str = new Array(total)
        .fill(1)
        .map(() => {
            return chars.charAt(Math.floor(Math.random() * chars.length));
        })
        .join('');
    return str;
}

export type TextFormat = 'base64' | 'hex';

interface MixOptions {
    timestamp?: number;
    nonce?: string;
}
/**
 * 生成待加密的混入文本
 * @param rawText
 */
export function genMixContent(rawText: string, options?: MixOptions): string {
    const timestamp = (options && options.timestamp) || getTimeStamp();
    const nonce = (options && options.nonce) || genRandomString(10);
    return [timestamp, nonce, rawText].join('_');
}
