<script>
    import { getContext } from 'svelte';
    import QrCode from './qrCode.svelte';
    import { sendCommonPV } from '../../common/logger';

    const themeColor = getContext('themeColor');

    let refreshStatus = false;

    sendCommonPV('SCAN_CODE_LOGIN');
    function codeNeedRefresh(e) {
        if(!e || !e.detail) {
            return
        }
        if (e.detail === 'scan') {
            refreshStatus = false;
            return
        }
        refreshStatus = true;
    }
</script>

<div class="qr-login">
    <QrCode
        width={162}
        height={162}
        on:login-success
        on:login-fail
        on:code-need-refresh={codeNeedRefresh}
    />
</div>
<p class="qr-login-download">
    打开
    <strong class="{themeColor}">快手APP</strong>
    扫一扫登录
</p>

<style type="text/stylus">
    @import '../../common/variables.styl';
.qr-login
    margin-top 57px
    background-size 140px auto
    height 180px
    text-align center
    &-download
        margin-top 24px
        font-size 13px
        line-height 22px
        text-align center;
        color font-color-primary
        strong
            color brand-primary
            text-decoration none;
            &.blue
                color brand-primary-blue
    &-download-desc
        margin-top 3px
        font-size 13px
        line-height 22px
        text-align center;
        color #9c9c9c
</style>
