<script lang="ts">
    import { twMerge } from 'tailwind-merge';
    import Frame from "$lib/utils/Frame.svelte";
    import { dialog } from './dialog';

    export let backdropClass: string = 'fixed inset-0 z-40 bg-gray-900 bg-opacity-50';
    export let defaultClass: string = '';

    $: frameCls = twMerge(defaultClass, $$props.class);
</script>

{#if $dialog.open}
    <!-- backdrop -->
    <div class={backdropClass}></div>

    <!-- dialog -->
    <div class="dialog-outer" tabindex="-1" aria-modal="true" role="dialog">
        <Frame rounded
               shadow
               {...$$restProps}
               class={frameCls}
               color='default'
        >
            <!-- Dialog content -->
            <div class="confirm-dialog">
                <div class="dialog-title">{$dialog.title}</div>
                {#if $dialog.message}
                    <div class="dialog-message">{$dialog.message}</div>
                {/if}

                <!-- Buttons -->
                <div class="button-container">
                    <button
                        class="cancel-button"
                        on:click={$dialog.onCancel}
                    >
                        {$dialog.cancelText}
                    </button>
                    <button
                        class="confirm-button"
                        on:click={$dialog.onConfirm}
                    >
                        {$dialog.confirmText}
                    </button>
                </div>
            </div>
        </Frame>
    </div>
{/if}

<style>
    .dialog-outer {
        position: fixed;
        inset: 0;
        z-index: 50;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 16px;
    }
    .confirm-dialog {
        width: 280px;
        border-radius: 12px;
        background-color: #FFFFFF;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        padding-top: 24px;
    }
    .dialog-title {
        color: #222222;
        font-size: 18px;
        font-weight: 500;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 26px;
        text-align: center;
        vertical-align: middle;
        margin-bottom: 12px;
    }

    .dialog-message {
        color: #666666;
        font-weight: 400;
        font-family: PingFang SC;
        line-height: 24px;
        margin-bottom: 24px;
        text-align: center;
        font-size: 16px;
        vertical-align: middle;
    }

    .button-container {
        display: flex;
        border-top: 1px solid #EDEFF2;
    }

    button {
        flex: 1;
        font-family: PingFang SC;
        font-size: 17px;
        font-weight: 400;
        line-height: 24px;
        padding-top: 11px;
        padding-bottom: 12px;
        outline: none;
        background: transparent;
        border: none;
        cursor: pointer;
    }

    .cancel-button {
        color: #666666;
        border-right: 1px solid #EDEFF2;
    }

    .cancel-button:hover {
        color: #333333;
    }

    .confirm-button {
        color: #4E6EF2;
    }

    .confirm-button:hover {
        color: #3A5AE0;
    }
</style>
