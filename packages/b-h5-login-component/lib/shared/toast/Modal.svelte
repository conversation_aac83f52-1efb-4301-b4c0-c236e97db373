<script lang="ts">
    import { twMerge } from 'tailwind-merge';
    import { createEventDispatcher, type ComponentProps } from 'svelte';
    import Frame from "$lib/utils/Frame.svelte";
    import type { ModalPlacementType } from "$lib/shared/toast/types";

    export let enableBackdrop: boolean = false;
    export let open: boolean = false;
    export let placement: ModalPlacementType = 'center';
    export let title: string = '';
    export let dismissable: boolean = true;
    export let backdropClass: string = 'fixed inset-0 z-40 bg-gray-900 bg-opacity-50 dark:bg-opacity-80';
    export let classBackdrop: string | undefined = undefined;
    export let dialogClass: string = 'fixed top-0 start-0 end-0 h-modal md:inset-0 md:h-full z-50 w-full p-4 flex inset-0';
    export let classDialog: string | undefined = undefined;
    export let defaultClass: string = 'relative flex flex-col mx-auto';
    export let headerClass: string = 'flex justify-between items-center p-4 md:p-5 rounded-t-lg';
    export let classHeader: string | undefined = undefined;
    export let bodyClass: string = 'p-4 md:p-5 space-y-4 flex-1 overflow-y-auto overscroll-contain';
    export let classBody: string | undefined = undefined;
    export let footerClass: string = 'flex items-center p-4 md:p-5 space-x-3 rtl:space-x-reverse rounded-b-lg';
    export let classFooter: string | undefined = undefined;

    const dispatch = createEventDispatcher();
    $: dispatch(open ? 'open' : 'close');

    const hide = (e: Event) => {
        e.preventDefault();
        open = false;
    };

    const getPlacementClasses = (placement: ModalPlacementType) => {
        switch (placement) {
            // top
            case 'top-left':
                return ['justify-start', 'items-start'];
            case 'top-center':
                return ['justify-center', 'items-start'];
            case 'top-right':
                return ['justify-end', 'items-start'];

            // center
            case 'center-left':
                return ['justify-start', 'items-center'];
            case 'center':
                return ['justify-center', 'items-center'];
            case 'center-right':
                return ['justify-end', 'items-center'];

            // bottom
            case 'bottom-left':
                return ['justify-start', 'items-end'];
            case 'bottom-center':
                return ['justify-center', 'items-end'];
            case 'bottom-right':
                return ['justify-end', 'items-end'];

            default:
                return ['justify-center', 'items-center'];
        }
    };

    $: frameCls = twMerge(defaultClass, 'divide-y', $$props.class);
    $: backdropCls = twMerge(backdropClass, classBackdrop);
    $: dialogCls = twMerge(dialogClass, classDialog, getPlacementClasses(placement));

    $: bodyCls = twMerge(bodyClass, classBody);
</script>

{#if open}
    <!-- backdrop -->
    {#if enableBackdrop}
        <div class={backdropCls}></div>
    {/if}
    <!-- dialog -->
    <!-- svelte-ignore a11y-no-noninteractive-element-interactions -->
    <div on:wheel|preventDefault|nonpassive class={dialogCls} tabindex="-1" aria-modal="true" role="dialog">
        <div class="flex relative max-w-2xl w-full max-h-full">
            <Frame rounded
                   shadow
                   {...$$restProps}
                   class={frameCls}
                   color='default'
            >
            <!-- Modal body -->
                <div class={bodyCls} role="document" on:wheel|stopPropagation|passive>
                    <slot></slot>
                </div>
            </Frame>
        </div>
    </div>
{/if}
