const path = require('path');
const RuntimePublicPathPlugin = require('webpack-runtime-public-path-plugin');

const isMock = process.env.MOCK === 'true';

console.log('process.env.NODE_ENV: ', process.env.NODE_ENV);

const isLocal = process.env.NODE_ENV !== 'production' || process.env.USE_LOCAL === 'true';

const publicPath = process.env.PUBLIC_PATH || (isLocal ? '/' : '//var----h5----var.kskwai.com/kos/nlav10761/');

// 非生产环境，添加 /account-h5 前缀，主要是为了在 AP 配置 ksid-staging.corp.kuaishou.com/account-h5/login 转发到 webops 上
const assetsDir = isLocal ? './account-h5' : '';

/**
 *
 * @param time 毫秒级时间戳
 * @return {string}
 */
function timeStamp2String(time) {
    const ret = new Date(time)
        .toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false,
            weekday: 'short',
        })
        .replace(/\//g, '-');
    return ret.slice(0, 10) + ' ' + ret.slice(10);
}

module.exports = {
    publicPath,
    assetsDir,
    pages: {
        main: {
            entry: 'src/main.ts',
            title: '授权登录页',
            template: 'public/index.html',
            filename: 'index.html',

            buildingDateTime: timeStamp2String(Date.now()),
            gitCommitLog: (() => {
                const ret = String(process.env.gitCommitLog).split(' ');
                ret.push(timeStamp2String(Number(ret.pop()) * 1000));
                return ret.join(' ');
            })(),
        },
    },
    chainWebpack: (config) => {
        config.resolve.alias.set('@', path.join(__dirname, 'src'));

        config.plugin('define').tap((args) => {
            if (args[0] && args[0]['process.env']) {
                Object.assign(args[0]['process.env'], {
                    NODE_ENV: JSON.stringify(process.env.NODE_ENV || 'production'),
                    USE_LOCAL: JSON.stringify(process.env.USE_LOCAL || ''),
                    PREFIX: JSON.stringify(isMock ? '/api' : ''),
                });
            }
            return args;
        });
        // 非本地环境
        config.when(!isLocal, (config) =>
            config.plugin('RuntimePublicPathPlugin').use(RuntimePublicPathPlugin, [
                {
                    runtimePublicPath: "cdn_public_path + '/kos/nlav10761/'",
                },
            ]),
        );
    },
    transpileDependencies: ['@vue/composition-api'],
    devServer: {
        disableHostCheck: true,
        historyApiFallback: {
            rewrites: [
                {
                    // from: /^\/.*/,
                    from: /^\/account-h5/,
                    to: '/index.html',
                },
            ],
        },
        proxy: {
            [`^${isMock ? '' : '/disabled'}/api`]: {
                // target: 'https://mock.corp.kuaishou.com/mock/5843/',
                target: 'https://ksidtest-h5.corp.kuaishou.com/',
                pathRewrite: { '^/api': '' },
                ws: false,
                changeOrigin: true,
            },
            '^/pass': {
                target: 'https://ksid-staging.corp.kuaishou.com/',
                ws: false,
                changeOrigin: true,
            },
        },
    },
};
