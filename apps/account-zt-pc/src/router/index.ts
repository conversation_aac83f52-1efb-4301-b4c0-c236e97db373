import Vue from 'vue';
import VueRouter, { RouteConfig } from 'vue-router';
import LoginRoutes from '@/modules/account-login/routes';
import LoginProtectRoutes from '@/modules/account-login-protect/routes';
import LiveQrcodeRoutes from '@/modules/account-qrcode/routes';
import IdentityQrcodeRoutes from '@/modules/identity-qrcode/routes';
import LoginComponentPage from '@/modules/login-component-page/routes';
import ThirdPartLogin from '@/modules/third-part-login/routes';
import NotFoundPage from '@/modules/not-found-page/routes';

Vue.use(VueRouter);

const routes: Array<RouteConfig> = [
    ...IdentityQrcodeRoutes,
    ...LiveQrcodeRoutes,
    ...LoginRoutes,
    ...LoginProtectRoutes,
    ...LoginComponentPage,
    ...ThirdPartLogin,
    ...NotFoundPage, // 请确保 404 页面放在最后。
];

const router = new VueRouter({
    mode: 'history',
    base: '/pc',
    routes,
});

export default router;
