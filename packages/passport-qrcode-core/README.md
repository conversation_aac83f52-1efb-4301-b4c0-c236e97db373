# 二维码扫描核心逻辑

在线文档: https://docs.corp.kuaishou.com/k/home/<USER>/fcADneHri_RB1UteSOteyNgB5

## 安装

```shell
# npm
npm install @ks-passport/qrcode-core

# yarn
yarn add @ks-passport/qrcode-core
```

## 使用

```javascript
import { startAccountQrcodeVerify } from '@ks-passport/qrcode-core';

const accountQrcodeVerify = startAccountQrcodeVerify({
    env: 'local', // 本地开发环境
    kpn: 'KUAISHOU',
    sid: 'kuaishou.api',
    bizName: 'LIVE_STREAM', // 直播业务线
});

accountQrcodeVerify.on('change-qrcode-image', (url) => {
    // 更新二维码显示;
})

// 1. 通过 Promise 接收成功结果
accountQrcodeVerify.start()
    .then(res => {
        // 授权成功
    })
    .catch(res => {
        // 失败
    })

// 2. 或者监听成功事件
accountQrcodeVerify.on('success', (res) => {
    // 授权成功
})
accountQrcodeVerify.start()

```

## API

### startAccountQrcodeVerify
