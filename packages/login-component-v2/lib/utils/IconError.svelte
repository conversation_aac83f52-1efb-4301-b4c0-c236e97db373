<script lang="ts">
    import { twMerge } from 'tailwind-merge';
    import ToolbarButton from '$lib/shared/toolbar/ToolbarButton.svelte';
    export let name: string = 'Error';
    export let width = 16;
    export let height = 16;
</script>

<ToolbarButton on:click {name} {...$$restProps} class={twMerge($$props.class)} let:svgSize
>
    <svg width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M1.33325 7.99967C1.33325 4.31834 4.31859 1.33301 7.99992 1.33301C11.6813 1.33301 14.6666 4.31834 14.6666 7.99967C14.6666 11.681 11.6813 14.6663 7.99992 14.6663C4.31859 14.6663 1.33325 11.681 1.33325 7.99967ZM9.41405 10.3565C9.67472 10.6171 10.0961 10.6171 10.3567 10.3565C10.6174 10.0958 10.6174 9.67447 10.3567 9.41381L8.94272 7.99981L10.3567 6.58581C10.6174 6.32514 10.6174 5.90314 10.3567 5.64314C10.0961 5.38247 9.67472 5.38247 9.41405 5.64314L8.00005 7.05714L6.58605 5.64314C6.32539 5.38247 5.90339 5.38247 5.64338 5.64314C5.38272 5.90314 5.38272 6.32514 5.64338 6.58581L7.05738 7.99981L5.64338 9.41381C5.38272 9.67447 5.38272 10.0958 5.64338 10.3565C5.90339 10.6171 6.32539 10.6171 6.58605 10.3565L8.00005 8.94247L9.41405 10.3565Z" fill="#E02C1F"/>
    </svg>

</ToolbarButton>

<style>
</style>
