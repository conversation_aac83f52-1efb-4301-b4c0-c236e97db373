import {
    getQrcodeInfo,
    getScaned,
    getAcceptResult,
    ENUM_RESULT_CODE
} from './service';

import EventEmitter from 'eventemitter3';
// import setTimeout = jest.setTimeout;

export interface QrcodeVerifyResult {
    result: number;
    errorMsg: string;
    qrLoginSignature: string;
    serviceOwnToken: string;
    serviceOwnType: number;
    qrAuthToken: string;
}
type EventTypes = 'change-step' | 'change-qrcode-image' | 'success' | 'timeout' | 'error' | 'deny'

export default class QrcodeVerify {
    private sid = '';
    private kpn = '';
    private qrType = '';
    public serviceOwnParams = '';

    private next = 'init';
    private event = new EventEmitter();

    // start
    qrLoginSignature = '';
    qrLoginToken = '';
    imageData = '';
    // acceptResult
    serviceOwnToken = '';
    serviceOwnType = 0;
    qrAuthToken = '';

    constructor(option: {
        sid: string;
        kpn: string;
        qrType: string;
        serviceOwnParams: string;
    }) {
        Object.assign(this, option);
    }

    on(eventName: EventTypes, fn: () => void) {
        return this.event.on(eventName as string, fn);
    }

    off(eventName: string, fn: () => void) {
        return this.event.off(eventName, fn);
    }

    once(eventName: string, fn: () => void) {
        return this.event.once(eventName, fn);
    }

    protected emit(eventName: string, ...args: any[]) {
        return this.event.emit(eventName, ...args);
    }

    get imageURL() {
        const { imageData } = this;
        return imageData
            ? `data:image/png;base64,${imageData}`
            : '';
    }

    setNext(to: string) {
        const { next: from } = this;
        this.next = to;
        console.info(`step from ${from} to ${to}`);
        this.emit('change-step', to, from);
    }

    async start(): Promise<{
        result: number;
        errorMsg: string;
        qrLoginSignature: string;
        serviceOwnToken: string;
        serviceOwnType: number;
        qrAuthToken: string;
    }> {
        try {
            this.setNext('init');
            await this.getQrcodeInfo();
            if (this.imageData) {
                this.emit('change-qrcode-image', this.imageURL);
            }
            await this.getScanResult();
            await this.getAcceptResult();
            return this.emitSuccess();
        } catch (e: any) {
            if (e?.result === ENUM_RESULT_CODE.TIME_OUT) {
                // 超时
                this.setNext('timeout');
            } else if (e?.result === ENUM_RESULT_CODE.USER_CANCEL) {
                this.setNext('userCancel');
            } else {
                this.emit('error', e);
            }
            return {
                result: 0,
                errorMsg: `${this.next}${e.errorMsg || e.error_msg}`,
                qrLoginSignature: '',
                serviceOwnToken: '',
                serviceOwnType: 0,
                qrAuthToken: '',
            }
        }
    }

    /**
     * 获取二维码信息
     *
     */
    async getQrcodeInfo() {
        const {
            sid,
            kpn,
            qrType,
            serviceOwnParams,
        } = this;
        console.info('get QrCode Info', {
            sid,
            kpn,
            qrType,
            serviceOwnParams,
        })
        return getQrcodeInfo({
            sid,
            kpn,
            qrType,
            serviceOwnParams,
        })
            .then(
                (res) => {
                    const {
                        next,
                        imageData,
                        qrLoginSignature,
                        qrLoginToken,
                    } = res;
                    if (next === 'scanResult') {
                        this.setNext(next);
                        this.imageData = imageData;
                        this.qrLoginSignature = qrLoginSignature;
                        this.qrLoginToken = qrLoginToken;
                        return res;
                    } else {
                        throw res;
                    }
                },
                res => {
                    this.emit('error')
                    throw res;
                }
            )
    }

    async getScanResult() {
        const {
            qrLoginSignature,
            qrLoginToken,
        } = this;
        console.log('get scan info', qrLoginSignature, qrLoginToken);
        return getScaned(qrLoginSignature, qrLoginToken)
            .then(
                (res) => {
                    const { next, qrLoginSignature } = res;
                    if (next === 'acceptResult') {
                        this.setNext(next);
                        this.qrLoginSignature = qrLoginSignature;
                        return res;
                    } else {
                        throw res;
                    }
                },
                res => {
                    this.emit('deny')
                    throw res;
                }
            )
    }

    async getAcceptResult() {
        const {
            qrLoginSignature,
            qrLoginToken,
        } = this;
        return getAcceptResult(qrLoginSignature, qrLoginToken)
            .then(
                (res) => {
                    const {
                        next,
                        qrLoginSignature,
                        serviceOwnToken,
                        qrAuthToken,
                    } = res;
                    if (next === 'succ') {
                        this.setNext(next);
                        this.qrLoginSignature = qrLoginSignature;
                        try {
                            const parseRes = JSON.parse(serviceOwnToken)
                            this.serviceOwnToken = parseRes.checkToken;
                            this.serviceOwnType = parseRes.checkTokenType
                        } catch (error) {
                            this.serviceOwnToken = serviceOwnToken;
                        }
                        this.qrAuthToken = qrAuthToken
                        return res;
                    } else {
                        throw res;
                    }
                },
                res => {
                    this.emit('deny')
                    throw res;
                }
            )
    }

    emitSuccess() {
        const {
            qrLoginSignature,
            serviceOwnToken,
            serviceOwnType,
            qrAuthToken,
            next,
        } = this;
        if (next !== 'succ') {
            return {
                result: 0,
                errorMsg: '',
                qrLoginSignature,
                serviceOwnToken,
                serviceOwnType,
                qrAuthToken,
            }
        }
        const successResult = {
            result: 1,
            errorMsg: '',
            qrLoginSignature,
            serviceOwnToken,
            serviceOwnType,
            qrAuthToken,
        }

        this.emit('success', successResult);
        return successResult;
    }
}
