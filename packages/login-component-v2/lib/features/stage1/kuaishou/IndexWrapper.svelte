<script lang="ts">
import ComponentStage1 from '$lib/features/stage1/kuaishou/index.svelte';
import Agreements from "$lib/features/components/Agreements.svelte";
import { createEventDispatcher, getContext } from "svelte";

const configStore = getContext('loginPageConfig');
const cAccountConfig = $configStore.cAccountConfig;
let showFooterSection = true;
function updateShowFooter(event) {
    showFooterSection = event.detail;
}
function toggleShowFooter() {
    showFooterSection = !showFooterSection;
}
let agreementType = 'login';
function updateAgreementType(event) {
    agreementType = event.detail;
}
</script>

<div>
    <ComponentStage1
            on:toggleShowFooter={toggleShowFooter}
            on:updateShowFooter={updateShowFooter}
            on:updateAgreementType={updateAgreementType}
    />
    <Agreements
            agreements={cAccountConfig.agreements}
            show={showFooterSection}
            agreementType={agreementType}
    />
</div>
