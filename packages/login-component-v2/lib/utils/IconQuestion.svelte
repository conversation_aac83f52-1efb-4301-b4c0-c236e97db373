<script lang="ts">
    import { twMerge } from 'tailwind-merge';
    import ToolbarButton from '$lib/shared/toolbar/ToolbarButton.svelte';
    export let name: string = 'Question';
    export let width = 16;
    export let height = 16;
</script>

<ToolbarButton on:click {name} {...$$restProps} class={twMerge($$props.class)} let:svgSize
>
    <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} fill="none" viewBox="0 0 16 16">
        <path fill="#B9BEC4" fill-rule="evenodd" d="M1.5 8a6.5 6.5 0 1 1 13 0 6.5 6.5 0 0 1-13 0ZM8 2.5a5.5 5.5 0 1 0 0 11 5.5 5.5 0 0 0 0-11Z" clip-rule="evenodd"/>
        <path fill="#B9BEC4" fill-rule="evenodd" d="M5.833 6.667A2.167 2.167 0 1 1 8.71 8.714a.43.43 0 0 0-.174.11c-.034.037-.035.058-.035.065v.778h-1v-.778c0-.607.487-.983.882-1.12a1.167 1.167 0 1 0-1.549-1.102h-1Z" clip-rule="evenodd"/>
        <path fill="#B9BEC4" d="M7.167 11.165a.833.833 0 1 1 1.666 0 .833.833 0 0 1-1.666 0Z"/>
    </svg>
</ToolbarButton>

<style>
</style>
