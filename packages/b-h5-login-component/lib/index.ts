import './styles/main.css';
import { init as ssoInit } from '@ks/sso';
import {init as generalInit} from "@ks/general-sso";
import type { InitParam } from '@ks/general-sso';
import LoginComponent from './features/LoginContainer.svelte';
import { getServiceOwnParams } from '$lib/utils/env';
import { sendActionEnd, sendActionStart, initRadar } from "$lib/core/radar";
import type { ActionStartDimension } from "$lib/core/radar";
import { EnumLoginType, EnumErrorType, EnumDefaultLoginType } from "$lib/shared/const";

export { EnumLoginType, EnumErrorType, EnumDefaultLoginType } from "$lib/shared/const";

export type InitComponentConfigType = Omit<InitParam, "appName"> & {
    target: HTMLElement;
    env: 'staging' | 'production';
    onLoginStart?: (name: EnumLoginType) => void;
    onSuccess?: (res: {
        loginType: EnumLoginType;
        result: object;
    }) => void;
    onFail?: (res: {
        loginType: EnumLoginType | EnumErrorType;
        error: object;
    }) => void;
    enableSig4: boolean;
    loginPageConfig: {
        cAccountConfig: {
            agreements: Array<{
                title: string;
                url: string;
                onClick?: Function;
            }>;
        };
        bAccountConfig: {
            baseAgreementOnClick?: Function;
            agreements: Array<{
                title: string;
                url: string;
                onClick?: Function;
            }>;
        };
    }
    appTitle: string;
    defaultLoginType: EnumDefaultLoginType;
    authorizeLogoUrl?: string;
}

function initPublicMethods(component: LoginComponent, config: InitComponentConfigType) {
    component.reRender = (customConfig?: any) => {
        component.$destroy();
        config.target.innerHTML = '';
        if (customConfig) {
            renderComponent(customConfig);
        } else {
            renderComponent(config);
        }
    }
}

export function renderComponent(config: InitComponentConfigType) {
    generalInit({
        appName: 'bid',
        qrType: 'bid-main',
        kpn: 'BID',
        // serviceOwnParams 不用传
        ...config,
    });
    ssoInit({
        ...config,
        kuaishouAuth: true,
        qrType: 'oauth-web',
        kpn: 'BID',
        serviceOwnParams: getServiceOwnParams(config.env),
    });
    initRadar(config.sid);
    try {
        const component = new LoginComponent({
            target: config.target,
            props: {
                sid: config.sid,
                baseUrl: config.baseUrl,
                onSuccess: (o: { loginType: EnumLoginType; result: object }) => {
                    sendActionEnd({
                        name: o?.loginType,
                    });
                    if (config.onSuccess) {
                        config.onSuccess(o);
                    }
                },
                onFail: (o: { loginType: EnumLoginType | EnumErrorType; error: object }) => {
                    if (config.onFail) {
                        config.onFail(o);
                    }
                },
                onLoginStart: (o: ActionStartDimension) => {
                    sendActionStart(o);
                    if (config.onLoginStart) {
                        config.onLoginStart(o.name as EnumLoginType);
                    }
                },
                agreements: config.agreements,
                appTitle: config.appTitle,
                defaultLoginType: config.defaultLoginType,
                loginPageConfig: config.loginPageConfig,
                authorizeLogoUrl: config.authorizeLogoUrl,
                env: config.env || 'production',
            }
        });
        initPublicMethods(component, config);
        return component;
    } catch (e) {
        console.log('renderComponent error', e);
        throw e;
    }
}
