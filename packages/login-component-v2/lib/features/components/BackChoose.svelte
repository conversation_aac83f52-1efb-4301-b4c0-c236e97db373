<script lang="ts">
    import IconLeftArrow from "$lib/utils/IconLeftArrow.svelte";
    export let otherOperateConfig;

    function onClick() {
        otherOperateConfig?.onClick?.();
    }
</script>


{#if otherOperateConfig?.title}
    <div class="wrapper">
        <div class="flex gap-[8px]"
             on:click={onClick}
        >
            <IconLeftArrow />
            <div class="text"
            >
                {otherOperateConfig.title}
            </div>
        </div>
    </div>
{:else if otherOperateConfig?.rawHTML}
    <div on:click={onClick}>
        {@html otherOperateConfig.rawHTML}
    </div>
{/if}


<style>
    .wrapper {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 24px;
    }
    .text {
        cursor: pointer;
        color: #9C9C9C;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: center;
        vertical-align: top;
    }
</style>
