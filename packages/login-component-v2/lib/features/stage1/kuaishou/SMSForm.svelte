<script lang="ts">
    import { getContext, createEventDispatcher, onMount } from 'svelte';


    import Button from '$lib/shared/button/index.svelte';
    import { getCaptchaToken, login as ssoLogin, requestMobileCode, getCountryCodeList } from '@ks/sso';

    import Input from '$lib/shared/forms/input.svelte';
    import DropDown from "$lib/shared/dropdown/DropDown.svelte";
    import ViceOperate from "$lib/features/components/viceOperate/ViceOperate.svelte";

    import { createSmsCodeStore } from "$lib/store/smsStore";
    import { multiUserInfo, bindPhoneInfo } from '$lib/features/stage1/kuaishou/store';
    import { CODE_MULTICHOOSE, EnumLoginType, EnumPageState, CODE_NEED_BIND, CODE_NEED_BIND_V2, EnumErrorType } from "$lib/shared/const";
    import PhoneInput from "$lib/features/components/PhoneInput.svelte";
    import BackChoose from "$lib/features/components/BackChoose.svelte";
    import RecommendLoginC from "$lib/features/components/RecommendLoginC.svelte";

    const dispatch = createEventDispatcher();

    const configStore = getContext('loginPageConfig');
    let viceOperateConfig = $configStore.cAccountConfig?.viceOperateConfig;
    let otherOperateConfig = $configStore.cAccountConfig?.otherOperateConfig;
    let inputClass = $configStore.cAccountConfig?.inputClass || '';

    const store = createSmsCodeStore();


    let loginErrorMsg = '';

    onMount(() => {
        getCountryCodeList().then((res) => {
            console.log('r', res.countryCodeList.map(item => ({
                label: item.chineseName,
                value: item.countryCode,
                key: item.englishName,
            })));
            store.setValues({
                countryCodeList: res.countryCodeList.map((item, index) => ({
                    label: item.chineseName,
                    value: item.countryCode,
                    key: item.englishName + index,
                })),
            });
        });
    })


    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    const onLoginStart = getContext('onLoginStart');

    let sendCodeClicked = false;
    async function sendCodeAction() {
        if (!store.reducers.validateSend()) {
            return;
        }
        if ($store.countDown > 0) {
            return;
        }
        try {
            await store.reducers.sendCodeAction(53);
            sendCodeClicked = true;
            store.setValues({
                countDown: 60,
            });
            const interal = setInterval(() => {
                if ($store.countDown <= 0) {
                    clearInterval(interal);
                    return;
                }
                store.setValues({
                    countDown: $store.countDown - 1,
                });
            }, 1000);
        } catch (error) {
            console.log('error', error);
            loginErrorMsg = error.error_msg || '网络错误，请稍后重试';
            onFail({
                loginType: EnumErrorType.SendCodeError,
                error,
            });
        }
    }

    let buttondisabled = false;
    async function loginSubmit() {
        if (!store.reducers.validateLogin()) {
            return;
        }
        try {
            onLoginStart({
                name: EnumLoginType.smsCodeC,
            });
            buttondisabled = true;
            // const captchaToken = captchaInfo ? await getCaptcha(captchaInfo) : null;
            const res = await ssoLogin({
                phone: $store.phone!,
                smsCode: $store.smsCode!,
                countryCode: $store.countryCode,
            });
            buttondisabled = false;
            console.log('res is', res, onSuccess);
            onSuccess({
                loginType: EnumLoginType.smsCodeC,
                result: res,
            });
        } catch (error) {
            buttondisabled = false;
            if (error?.result === CODE_MULTICHOOSE) {
                // 存在两个账号的情况，需要选择
                multiUserInfo.set({
                    userInfos: error.userInfos,
                    multiUserToken: error.multiUserToken,
                    phone: $store.phone!,
                    loginType: 'smsCode',
                    countryCode: $store.countryCode,
                });
                dispatch('updatePageState', EnumPageState.chooseUser);
                return;
            } else if ([CODE_NEED_BIND, CODE_NEED_BIND_V2].includes(error?.result)) {
                // 需要跳转到绑定手机号页面
                bindPhoneInfo.set({
                    phone: error.bindInfo.phone,
                    bindAuthToken: error.bindInfo.bindAuthToken,
                    quickLoginResponse: error,
                });
                console.log('EnumPageState.bindPhone', EnumPageState.bindPhone);
                dispatch('updatePageState', EnumPageState.bindPhone);
                return;
            }
            console.log('error', error);
            loginErrorMsg = error.error_msg || '网络错误，请稍后重试';
            onFail({
                loginType: EnumLoginType.smsCodeC,
                error,
            });
        }
    }

    function updatePageState(s) {
        console.log('s is', s);
        dispatch('updatePageState', s.detail);
    }
    let codeClass = '';
    $: {
        if ($store.phone?.length <= 11 && $store.phone?.length >= 8) {
            codeClass = 'activate';
        } else {
            codeClass = '';
        }
    }
</script>

<div class="w-full mt-[8px]">
    <div class="mb-[24px] flex items-center">
        <PhoneInput smsStore={store}
                    on:focus={() => {
                        console.log('onfocus');
                        store.setValues({
                            phoneErrorMsg: ''
                        })
                    }}
                    class={inputClass}
        />
    </div>
    <div class="component-select-items relative z-10">
        <DropDown
                class="absolute w-full top-[-20px] currentC"
                visible={$store.dropdownVisible}
                options={$store.countryCodeList}
                current={$store.highlightIndex}
                renderItem={(label, value) => {
                    return `<span style="display: flex; justify-content: space-between; font-size: 14px">
                              <span>${value}</span>
                              <span>${label}</span>
                            </span>`
                }}
                onSelect={store.reducers.onSelectWrapper}
        />
    </div>
    <!--  因为要对齐最左边，phoneErrorMsg 在外面手动写一下 -->
    {#if $store.phoneErrorMsg}
        <div class="phone-input-msg text-[12px] leading-[20px] text-left text-[#F93920] mt-[-24px] mb-[4px]">{$store.phoneErrorMsg}</div>
    {/if}

    <div class="mb-[44px]">
        <Input placeholder="请输入验证码"
               bind:value={$store.smsCode}
               class={inputClass}
               errorMsg={$store.smsCodeErrorMsg}
               on:focus={() => {
                   store.setValues({
                       smsCodeErrorMsg: '',
                   })
               }}
               on:input={store.reducers.filterSmsCode}
               on:blur={store.reducers.onSmsCodeBlur}
               globalErrorMsg={loginErrorMsg}
               maxlength={6}
        >
            <slot slot="right">
                <div class="hover:cursor-pointer send-code font-[400] text-[14px] leading-[22px] currentC"
                     on:click={sendCodeAction}
                >
                    {#if $store.countDown}
                        <span class="text-[#C6C6C6ff]">{$store.countDown}s</span>
                    {:else}
                        <span class={codeClass}>
                            {#if sendCodeClicked}
                                重新发送
                            {:else}
                                发送验证码
                            {/if}
                        </span>
                    {/if}
                </div>
            </slot>
        </Input>
    </div>
    <Button class="currentC"
            on:click={loginSubmit}
            disabled={buttondisabled}
    >
        快手APP账号授权登录
    </Button>
    <ViceOperate
            currentType="smsForm"
            on:updatePageState={updatePageState}
            viceOperateConfig={viceOperateConfig}
    />
    <BackChoose {otherOperateConfig} />
    <RecommendLoginC />
</div>

<style>
    .activate {
        color: var(--colorC);
    }
    .send-code {
        color: #C6C6C6ff;
    }
    .phone-input-msg {
        color: #E02C1F;
    }
</style>
