<script lang="ts">
import IconBusinessLogo from "$lib/utils/IconBusinessLogo.svelte";
import { createEventDispatcher, getContext } from "svelte";

const configStore = getContext('loginPageConfig');
const switchCurrentIsC = getContext('switchCurrentIsC');


function switchCurrentState() {
    switchCurrentIsC && switchCurrentIsC?.();
}
</script>

{#if $configStore?.currentMainState === 2}
    <div>
        <div class="recommend-login-wrapper flex w-full items-center">
            <div class="divide-line flex-1"></div>
            <div class="recommend-login">推荐登录方式</div>
            <div class="divide-line flex-1"></div>
        </div>
        <div class="mt-[20px] flex gap-[10px] justify-center items-center">
            <IconBusinessLogo />
            <div class="login-text hover:cursor-pointer"
                 on:click={switchCurrentState}
            >
                快手商家账号登录
            </div>
        </div>
    </div>
{/if}

<style>
    .recommend-login-wrapper {
        padding-top: 2px;
        padding-bottom: 2px;
        margin-top: 20px;
    }
    .recommend-login {
        color: #9C9C9C;
        font-size: 12px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: left;
        vertical-align: top;
        padding: 0 16px;
    }
    .divide-line {
        border-bottom: 1px solid #EDEFF2;
        height: 1px;
    }
    .login-text {
        color: #666666;
        font-size: 16px;
        font-weight: 600;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 22px;
        text-align: center;
        vertical-align: top;
    }
</style>
