# ShellLogin SDK 使用说明

## 新建ShellLogin实例
``` ts
const sl = new ShellLogin(sid,env)
```
> 参数说明

| 参数                | 描述        |
| ---------------------- | -------------------------------------- | 
| sid | 各个业务线的业务标识 id
| env                | 运行环境：```"production" / "staging" / "development"```        |

## shell打印登录二维码
``` ts
await sl.printQRCode()
```

## 获取用户信息
等待用户扫码，扫码完成后返回用户基本信息

**注意**：`getUserLoginInfo`须在`printQRCode`完成之后调用

``` ts
let userInfo = await sl.getUserLoginInfo();
```

### 返回值
``` ts
interface BaseUserInfo {
    eid: string;
    headurl: string;
    headurls: Array<{
        cdn: string;
        url: string;
        urlPattern: string;
    }>;
    user_id: number;
    user_name: string;
    user_sex: string;
}
```

## 获取用户扫码结果

与后端建立长链接，等待用户在主 App 点击确认，点击确认后拿到登录结果，authToken用于后端换取登陆凭证

**注意**：`scanQRLoginResult`须在`getUserLoginInfo`之后调用

### 
``` ts
let resultScan = await sl.scanQRLoginResult();
```

### 返回值
``` ts
interface AuthTokenResult {
    authToken: string;
    sid: string;
    callback: string;
    serviceToken: string;
    ssecurity: string;
    userId: number;
    stsUrl: string;
    followUrl: string;
    isNewUser: boolean;
    serviceOwnToken?: string;
}
```

## 取消登录流程
``` ts
sl.cancelQrLogin()
```

## 完整示例
```ts
import { ShellLogin } from "@ks/shell-login";

const sid = '***';

async function test(){
    const sl = new ShellLogin(sid,"staging")
    const res = await sl.printQRCode()

    let userInfo = await sl.getUserLoginInfo();
    console.log("userInfo",userInfo)

    let resultScan = await sl.scanQRLoginResult();
    console.log("resultScan",resultScan)
    
}
test()

```