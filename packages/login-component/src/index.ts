import App from './App.svelte';
import LoginComponent from './LoginComponent.svelte';
import {
    init,
    InitParam,
    logout as ssoLogout,
    logoutV2 as ssoLogoutV2,
    LogoutV2Params,
    logoutV3 as ssoLogoutV3,
    LogoutV3Params,
    AuthTokenResult,
    passToken,
    saveTokenInIframe as ssoSaveTokenInIframe,
    refreshLoginStatus as ssoRefreshLoginStatus,
    RefreshLoginParam
} from '@ks/sso';

/**
 * 只用WebLog类型，不要初始化
 */
import WebLog from '@ks/weblogger';

import { setLogger, mockLogger, WebLogBasic, addRiskPlugin } from "@/common/logger";
import { IndexPageType } from "@/common/config";

// TODO: 升级到 v3 时，需要暴露 initSso 方法，让业务方手动全局初始化一次，类似 @ks/sso

let isSsoInited = false;

export const initSso = (params: InitParam) => {
    if (!isSsoInited) {
        init({
            ...params,
            env: params.env || 'production'
        });
        isSsoInited = true;
    }
}

export interface BaseParam {
    sid: string;
    env: 'production' | 'staging';
    callback?: string;
    logger?: WebLogBasic;
    // 透传的sso的baseUrl
    baseUrl?: string;
}

export interface ModalParam extends BaseParam {
    maskClosable?: boolean;
}

export interface RenderParam extends BaseParam {
    target: HTMLElement;
    indexPage?: IndexPageType;
    kpn?: string;
    qrType?: string;
    // 登录成功
    onSuccess?(res: { result: AuthTokenResult, loginType: string }): any;
    // 登录失败
    onFail?(res: any): any;
    // 注册快手用户 如果传入此回调 那么点击注册的时候只发事件，不做其他处理
    handleRegisterAction?(): any;
    // 新版扫码登录透传参数
    serviceOwnParams?: any;
    // 是否展示第三方登录
    showThirdPartLogin?: boolean;
    // 第三方登录成功之后跳转的地址
    thirdPartLoginFollowUrl?: string;
    // show register
    showRegister?: boolean;
    // 显示切换开关
    showPlatformSwitch?: boolean;
    // 登录的渠道
    channelType?: string;
    // 自定义反馈相关
    appealCustomTitle?: string; // 反馈btn文案
    appealCustomHref?: string; // 反馈btn的跳转链接
    themeColor?: 'default' | 'blue'; // 主题色
    enableSig4: boolean;
}

let app: any = null;

const defaultConfig: BaseParam = {
    // default to production
    env: 'production',
    sid: '',
    logger: mockLogger,
};

// 加一下需要的 config
export const login = (config: ModalParam = defaultConfig): Promise<AuthTokenResult> =>
    new Promise((resolve, reject) => {
        if (config.logger) {
            setLogger(config.logger);
            addRiskPlugin(config);
        }
        function onClose() {
            app.$set({
                isShow: false,
            });
            reject('cancel by user');
        }

        if (!app) {
            // TODO 组件接收环境变量
            initSso({
                sid: config.sid,
                env: config.env,
            })

            const container = document.createElement('div');
            container.classList.add('svelte-login');
            document.body.appendChild(container);
            app = new App({
                target: container,
                props: {
                    isShow: true,
                    resolve,
                    onClose,
                    isProduction: config.env === 'production',
                    maskClosable: config.maskClosable,
                },
            });
        } else {
            app.$set({
                isShow: true,
                resolve,
                onClose,
            });
        }
    });

export const renderComponent = (config: RenderParam) => {
    // 这里可能会传入 kpn、qrType、serviceOwnParams 等参数，不能使用 initSso 方法。
    // 因为用户可能会一进入页面先调用 refreshLoginStatus 刷新登录态，然后再调用 renderComponent 进行登录。这种情况下，若使用 initSso，kpn、qrType、serviceOwnParams 等参数就不会被设置
    init({
        callback: '',
        ...config,
    });
    if (config.logger) {
        setLogger(config.logger);
        addRiskPlugin(config);
    }

    // 需要检查参数：sid, target
    new LoginComponent({
        target: config.target,
        props: {
            indexPage: config.indexPage || 'login-password',
            isProduction: config.env === 'production',
            onSuccess(e: { detail: { result: AuthTokenResult, loginType: string } }) {
                if (config.onSuccess) {
                    config.onSuccess(e.detail)
                }
            },
            onFail(e: any) {
                if (config.onFail) {
                    config.onFail(e.detail);
                }
            },
            handleRegisterAction: config.handleRegisterAction,
            showThirdPartLogin: config.showThirdPartLogin ?? false,
            thirdPartLoginFollowUrl: config.thirdPartLoginFollowUrl ?? '',
            sid: config.sid,
            showRegister: config.showRegister ?? true,
            showPlatformSwitch: config.showPlatformSwitch ?? true,
            appealCustomTitle: config.appealCustomTitle,
            appealCustomHref: config.appealCustomHref,
            themeColor: config.themeColor || 'default', // 主题色
        },
    });
};

export const saveTokenInIframe = ssoSaveTokenInIframe;

export const logout = (config: BaseParam) => {
    if (!app) {
        init({
            callback: '',
            ...config,
        });
    }
    return ssoLogout();
};

export const logoutV2 = (config: BaseParam & LogoutV2Params) => {
    initSso({
        sid: config.sid,
        env: config.env,
    })

    ssoLogoutV2(config);
};

export const logoutV3 = (config: BaseParam & LogoutV3Params) => {
    initSso({
        sid: config.sid,
        env: config.env,
    })

    ssoLogoutV3(config);
};

/**
 * 刷新登录态（在隐藏的 iframe 内）
 */
export const refreshLoginStatus = (config: BaseParam & RefreshLoginParam) => {
    initSso({
        sid: config.sid,
        env: config.env,
    })

    return ssoRefreshLoginStatus(config)
}

// Deprecated
export const changeToken = (config: BaseParam) => {
    if (!app) {
        init({
            callback: '',
            ...config,
        });
    }
    return passToken();
}
