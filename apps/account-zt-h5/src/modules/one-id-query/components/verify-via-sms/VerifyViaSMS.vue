<template>
    <div>
        <div class="white-panel">
            <div style="margin-bottom: 0.1875em; margin-top: 0.25em;">
                <span class="black-text">手机号码</span>
            </div>
            <div class="input-component phoneNumber">
                <div class="area-code">
                    <span> {{ phoneInfo.countryCode }}</span>
                </div>
                <input style="" v-model="phoneInfo.phone" :placeholder="phonePlaceholderText" maxlength="11" />
                <div class="clear-icon">
                    <img
                        v-show="phoneInfo.phone"
                        @click.stop="phoneInfo.phone = ''"
                        class="close-icon"
                        src="@/assets/delete.png"
                    />
                </div>
            </div>
            <div style="margin-top: 0.875em; margin-bottom: 0.1875em;">
                <span class="black-text">短信验证码</span>
            </div>
            <div class="input-component captcha" style="margin-bottom: 0.625em;">
                <input v-model="phoneInfo.smsCode" placeholder="请输入验证码" maxlength="6" />
                <div class="captcha-btn">
                    <div style="position: relative;">
                        <template v-if="counting">
                            <span style="color: #9C9C9C; margin-right: 0.76923em;">{{`重新发送(${countDown})`}}</span>
                        </template>
                        <template v-else>
                            <span @click="sendSmsCode" style="margin-right: 0.461538em;">获取验证码</span>
                        </template>
                        <div class="clear-icon">
                            <img
                                v-show="phoneInfo.smsCode"
                                @click.stop="phoneInfo.smsCode = ''"
                                class="close-icon"
                                src="@/assets/delete.png"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 5em;">
            <div
                @click="onConfirm"
                class="red-btn"
                :style="{ opacity: phoneInfo.smsCode.length + phoneInfo.phone.length === 17 ? 1 : 0.5 }"
            >
                确认
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { reactive, defineComponent } from '@vue/composition-api';
import { Toast } from '@ks/sharp-ui';
import { getZtIdentityVerification, startIdentityVerify } from '@/utils/identity-verification';
import { sendSmsCode as sendSmsCodeApi } from '@/api/query-account-by-phone';
import useCountDown from '@/common/hooks/useCountDown';
import { throttle } from '@/utils/limit';
import { COUNTRY_CODE, BIZ_TYPE_LOGIN, ONE_ID_QUERY_SID } from '@/common/config/constants';
import Cookies from 'js-cookie';

export default defineComponent({
    props: {
      phonePlaceholder: String,
      bizType: String,
    },

    setup(props, context) {
        const phonePlaceholderText = props.phonePlaceholder || '请输入手机号码';
        const bizType = props.bizType || BIZ_TYPE_LOGIN;

        const phoneInfo = reactive({
            countryCode: COUNTRY_CODE,
            // 手机号码
            phone: '',
            // 短信验证码
            smsCode: '',
        });

        const { start, value: countDown, running: counting } = useCountDown(60);

        function checkSmsCode() {
            if (/^\d{6}$/.test(phoneInfo.smsCode)) {
                return true;
            }

            Toast.error(`请输入${phoneInfo.smsCode ? '6位' : ''}验证码`);
            return false;
        }

        function checkPhone() {
            if (/^1[3-9]\d{9}$/.test(phoneInfo.phone)) {
                return true;
            }

            Toast.error(`请输入${phoneInfo.phone ? '正确的' : ''}手机号`);
            return false;
        }

        async function sendSmsCode() {
            if (!checkPhone()) {
              return;
            }

            if (!Cookies.get('kpn')) {
                Cookies.set('kpn', 'KUAISHOU', { domain: '.kuaishou.com' })
            }

            sendSmsCodeApi({
                sid: ONE_ID_QUERY_SID,
                countryCode: phoneInfo.countryCode,
                phone: phoneInfo.phone,
                type: bizType,
                ...getZtIdentityVerification(),
            })
            .then(() => {
                start();
            })
            .catch(async (err) => {
                if (err?.url) {
                    const res = await startIdentityVerify(err.url);
                    if (res) sendSmsCode();
                } else {
                    Toast.error(err?.error_msg || '发送失败，请检查网络');
                }
            });
        }

        function onConfirm() {
          if (!checkSmsCode() || !checkPhone()) {
              return;
          }

          context.emit('confirm', phoneInfo);
        }

        return {
            sendSmsCode: throttle(sendSmsCode),
            phoneInfo,
            countDown,
            counting,
            onConfirm,
            phonePlaceholderText,
        };
    },
});
</script>

<style scoped lang="less">
@import "../../common/styles.less";

/deep/ .input-component {
  position: relative;

  input:not(:focus) + .clear-icon, input:not(:focus) + .captcha-btn .clear-icon {
    opacity: 0;
  }

  .clear-icon {
    position: absolute;
    top: 0;
    bottom: 0;
    // display: grid;
    // place-items: center;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 1em;
      height: 1em;
    }
  }

  &.phoneNumber .clear-icon {
    right: 0.995em;
  }

  &.captcha .clear-icon {
    left: -1.2738em;
    transform: translateX(-100%);

    img {
      font-size: 1.23em;
    }
  }

  // 区号和验证码按钮
  .area-code, .captcha-btn {
    color: #222222;
    // display: grid;
    // place-items: center;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    bottom: 0;
    top: 0;
  }

  &.phoneNumber {
    // 区号
    .area-code {
      width: 3.8em;
      left: 0;
      font-size: 0.9375em;

      font-family: PingFang SC;
      font-style: normal;
      font-weight: 500;
    }

    input {
      padding-left: 3.9em;
    }
  }

  &.captcha {
    // 验证码按钮
    .captcha-btn {
      color: #385080;
      right: 0;
      width: 7em;
      font-size: 0.8125em;

      &:hover {
        opacity: .8;
      }

      &:active {
        opacity: .5;
      }
    }

    input {
      padding-right: 7em;
    }
  }

  input {
    color: #222;

    &::placeholder {
      color: #9C9C9C;
    }

    -webkit-appearance: none;
    box-shadow: none;
    margin: 0;
    resize: none;
    border: none;
    outline: none;

    border-radius: 0.5em;
    background: #F8F8F8;
    width: 100%;

    height: 3.46666667em;
    line-height: 3.46666667em;
    font-size: 0.9375em;
    padding: 0 1em;
  }
}
</style>
