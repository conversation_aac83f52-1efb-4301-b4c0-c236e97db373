import { getConfig } from '@/core/configInit';
import { genEncryptReqParams } from '@/utils';
// import { DEFAULT_COUNTRY_CODE } from '@/config';
import { getLoginToken } from '@/core/loginToken';

/**
 * 密码登录
 */
export interface PhoneQuickBindParams {
    bindAuthToken: string;
}
export function phoneQuickBind({ bindAuthToken }: PhoneQuickBindParams) {
    const config = getConfig();
    const params = {
        sid: config.sid,
        bindAuthToken,
    };

    return getLoginToken(`/pass/${config.appName}/phone/phoneQuickBind`, {
        ...params,
    });
}

export function phoneQuickBindWeb({ bindAuthToken }: PhoneQuickBindParams) {
    const config = getConfig();
    const params = {
        sid: config.sid,
        bindAuthToken,
    };

    return getLoginToken(`/pass/${config.appName}/web/phone/phoneQuickBind`, {
        ...params,
    });
}

export interface PhoneBindParams {
    countryCode: string;
    phone: string;
    smsCode: string;
    bindAuthToken?: string;
}

export function phoneBind({ countryCode, phone, smsCode, bindAuthToken }: PhoneBindParams) {
    const config = getConfig();
    const params = {
        sid: config.sid,
        countryCode,
        phone,
        smsCode,
        bindAuthToken,
    };

    return getLoginToken(`/pass/${config.appName}/phone/bind`, {
        ...params,
    });
}

export function phoneBindV2({ countryCode, phone, smsCode, bindAuthToken }: PhoneBindParams) {
    const config = getConfig();
    const encryptReqParams = genEncryptReqParams({
        phone,
    });
    const params = {
        sid: config.sid,
        countryCode,
        smsCode,
        bindAuthToken,
        ...encryptReqParams,
    };

    return getLoginToken(`/pass/${config.appName}/phone/bind/v2`, {
        ...params,
    });
}

export function phoneBindV2Web({ countryCode, phone, smsCode, bindAuthToken }: PhoneBindParams) {
    const config = getConfig();
    const encryptReqParams = genEncryptReqParams({
        phone,
    });
    const params = {
        sid: config.sid,
        countryCode,
        smsCode,
        bindAuthToken,
        ...encryptReqParams,
    };

    return getLoginToken(`/pass/${config.appName}/web/phone/bind/v2`, {
        ...params,
    });
}


// 手机号一键绑定并登录
// https://kdev.corp.kuaishou.com/web/api-mock/repo/mgr?activeKey=group&apiId=770855&version=-1&viewType=repoView
export function phoneQuickBindAndLogin(authToken: string) {
    const config = getConfig();
    const params = {
        sid: config.sid,
        authToken,
    };

    return getLoginToken(`/pass/${config.appName}/web/phone/phoneQuickBindByAuthToken`, {
        ...params,
    });
}

// 一键换绑并登录
// https://kdev.corp.kuaishou.com/web/api-mock/repo/mgr?activeKey=group&apiId=770856&version=-1&viewType=repoView
export function phoneRebindAndLogin(authToken: string) {
    const config = getConfig();
    const params = {
        sid: config.sid,
        authToken,
    };

    return getLoginToken(`/pass/${config.appName}/web/phone/phoneReBindByAuthToken`, {
        ...params,
    });
}

// 绑定其它手机号并登录
// https://kdev.corp.kuaishou.com/web/api-mock/repo/mgr?activeKey=group&apiId=770857&version=-1&viewType=repoView
export interface BindOtherPhoneParams {
    authToken: string;
    phone: string;
    smsCode: string;
    countryCode?: string;
}
export function bindOtherPhoneAndLogin({authToken, phone, countryCode, smsCode}: BindOtherPhoneParams) {
    const config = getConfig();
    const encryptReqParams = genEncryptReqParams({
        phone,
    });
    const params = {
        sid: config.sid,
        authToken,
        countryCode,
        smsCode,
        ...encryptReqParams,
    };
    return getLoginToken(`/pass/${config.appName}/web/phone/bindOtherPhoneByAuthToken/v2`, {
        ...params,
    });
}

// 暂不绑定并登录
// https://kdev.corp.kuaishou.com/web/api-mock/repo/mgr?activeKey=group&apiId=770858&version=-1&viewType=repoView
export function notBindAndLogin(authToken: string) {
    const config = getConfig();
    const params = {
        sid: config.sid,
        authToken,
    };
    return getLoginToken(`/pass/${config.appName}/web/phone/notBindPhoneByAuthToken`, {
        ...params,
    });
}
