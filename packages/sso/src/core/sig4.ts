import { Jose, SigConfig } from '@ks-cqc/h5-sig4-lite-obf';
import { privateGetConfig } from '@/core/configInit';
import { formatParams } from '@/utils/utils';
import { sendEvent } from '@/core/radar';
import { SIG4_PATH_PREFIX, SIG4_PATH_WHITELIST } from '@/config/constants';

// 前端需要传入信息
interface sigConfig {
    url: string; // 【注意】取protocol://host/path?query#hash仅取其中的path部份（类似于location.pathname）
    query: Record<string, string>; // 【注意】签名版本号caver也需要
    form: Record<string, string>; // 【注意】content-type = 'application/x-www-form-urlencoded'的情况，传入的是请求formdata
    requestBody: Record<string, any>;// 【注意】content-type = 'application/json'的情况，传入的是请求body
    projectInfo?: { //如果想开启日志方便排查问题，需要有projectInfo；如果不传默认不会有日志，业务可以自行将signInput和signResult上传埋点；
        did?: string, // 默认:""
        uid?: string, // 默认:""
        oversea?: boolean,//默认false国内
        appKey: string, // 联系dongtianqi创建
        radarId?: string, //日志上报到雷达，填业务自己的radarId
        sampling?: number //签名上报采样率，1 为全量上报, 0为不上报
    }
}

// 2、签名方法调用：通过config注入签名项，suc回调打印的的signResult即为签名的加密字符串
async function getSig4(config: sigConfig): Promise<{
    signResult?: string;
    signInput?: string;
    error?: Error;
}> {
    return new Promise((resolve, reject) => {
        Jose.call('$encode', [
            config as SigConfig,
            {
                // @ts-ignore
                suc(signResult: string, signInput: string) {
                    if (privateGetConfig().env === 'staging') {
                        console.log('sig4 suc', {
                            signResult,
                            signInput,
                        });
                    }
                    sendEvent({
                         name: 'sig4 suc',
                         extra_info: {
                             signResult,
                             signInput,
                         }
                    });
                    resolve({
                        signResult,
                        signInput,
                    });
                },
                err(error: Error) {
                    sendEvent({
                        name: 'sig4 err',
                        extra_info: {
                            error,
                        }
                    });
                    reject(error);
                },
            },
        ]);
    });
}

// 3、获取签名版本号
function getSig4Ver() {
    return Jose.call('$getCatVersion') || '';
}


// 4、建议在网络库的拦截器里，将sig4参数拼接在url上
export async function getSig4Url(url: string, data: Record<string, any>) {
    const o = formatParams(url);
    // 1. 如果开关关闭，直接返回url
    // 2. 如果不在名单里或者不以 /pass/kuaishou 开头，直接返回url
    if (!privateGetConfig().enableSig4 || (!SIG4_PATH_WHITELIST.includes(o.path) && !o.path.startsWith(SIG4_PATH_PREFIX))) {
        return url;
    }
    const sig4Version = getSig4Ver();
    // const { encryptHeaders, ...bodyData } = data;
    const filteredObject = Object.fromEntries(
        Object.entries(data)
            .filter(([key, value]) => {
                return key !== 'encryptHeaders' && value !== undefined && value !== null;
            })
    );
    console.log('filteredObject', filteredObject);
    // 需要把版本号也传入，参与签名生成；
    const config: sigConfig = {
        url: o.path,
        query: {
            caver: sig4Version as string,
        },
        form: filteredObject, //如果没有，传空对象{}
        requestBody: {}, //如果没有，传空对象{}
        projectInfo: {
            appKey: 'q5yq4bjUut',
            radarId: '0abb8037ac',
        },
    };
    const { signResult } = await getSig4(config);
    const divider = url.includes('?') ? '&' : '?';
    const sig4Url = `${url}${divider}__NS_hxfalcon=${signResult!}&caver=${sig4Version}`;
    return sig4Url;
    // (axios或fetch）请求sig4url
    // fetch(sig4url, {});
}
