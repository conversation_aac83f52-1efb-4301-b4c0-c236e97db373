/**
 * @file
 * 该部分功能为:动态加载依赖
 * 该部分本身非动态加载
 * 异步获取依赖
 * @param url
 */
import {
    ScriptEvent,
    ScriptEventType,
} from './types';
import {
    addAsyncModuleLoadEvent,
    cached,
} from '@/common/utils/common';

type ResolveFn<T = unknown> = (value?: T) => void;
type RejectFn<T = unknown> = (reason?: T) => void;
type ResolveOrRejectFn = ResolveFn | RejectFn;

export const asyncLoad = (uri: string) => {
    let _resolve: ResolveFn;
    let _reject: RejectFn;
    const loadPromise = new Promise((resolve, reject) => {
        [_resolve, _reject] = [resolve, reject];
    });
    // 创建script标签，发起请求
    const script = document.createElement('script') as HTMLScriptElement;
    script.charset = 'utf-8';
    script.src = uri;
    script.crossOrigin = 'anonymous';
    const onScriptComplete = (event: ScriptEvent, executor: ResolveOrRejectFn) => {
        // 避免内存泄漏
        script.onerror = null;
        clearTimeout(timeout);
        executor(event.module);
    }
    // 异步模块加载超时处理
    const timeout = setTimeout(function(){
        const event = { type: ScriptEventType.timeout, target: script };
        onScriptComplete(event, _reject);
    }, 10 * 1000);
    // 模块加载成功
    addAsyncModuleLoadEvent((module: unknown) => {
        const event = { type: ScriptEventType.loaded, target: script, module};
        onScriptComplete(event, _resolve);
    });
    // 模块加载失败
    script.onerror =  () => {
        const event = { type: ScriptEventType.error, target: script};
        onScriptComplete(event, _reject);
    };
    document.head.appendChild(script);
    return loadPromise;
};

