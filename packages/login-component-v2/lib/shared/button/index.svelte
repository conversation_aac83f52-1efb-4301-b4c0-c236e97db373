<script lang="ts">
    import { twMerge } from 'tailwind-merge';
    import { getContext } from 'svelte';
    import type { SizeType } from '$lib/shared/types';
    import type { HTMLButtonAttributes } from 'svelte/elements';
    import Spinner from "$lib/shared/spinner/Spinner.svelte";

    type ButtonColor = keyof typeof colorClasses;

    const group: SizeType = getContext('group');
    export let pill: boolean = false;
    export let outline: boolean = false;
    export let size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = group ? 'sm' : 'md';
    export let href: string | undefined = undefined;
    export let type: HTMLButtonAttributes['type'] = 'button';
    export let color: ButtonColor = group ? (outline ? 'dark' : 'alternative') : 'primary';
    export let shadow: boolean = false;
    export let tag: string = 'button';
    export let checked: boolean | undefined = undefined;
    export let disabled: boolean = false;

    const colorClasses = {
        alternative: 'text-gray-900 bg-white border border-gray-200 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-400 hover:text-primary-700 focus-within:text-primary-700 dark:focus-within:text-white dark:hover:text-white dark:hover:bg-gray-700',
        blue: 'text-white bg-blue-700 hover:bg-blue-800 dark:bg-blue-600 dark:hover:bg-blue-700',
        dark: 'text-white bg-gray-800 hover:bg-gray-900 dark:bg-gray-800 dark:hover:bg-gray-700',
        green: 'text-white bg-green-700 hover:bg-green-800 dark:bg-green-600 dark:hover:bg-green-700',
        light: 'text-gray-900 bg-white border border-gray-300 hover:bg-gray-100 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600',
        primary: 'text-white bg-primary-700 hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700',
        purple: 'text-white bg-purple-700 hover:bg-purple-800 dark:bg-purple-600 dark:hover:bg-purple-700',
        red: 'text-white bg-red-700 hover:bg-red-800 dark:bg-red-600 dark:hover:bg-red-700',
        yellow: 'text-white bg-yellow-400 hover:bg-yellow-500 ',
        none: ''
    };

    let buttonClass: string;
    $: buttonClass = twMerge(
        'text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px]',
        $$props.class
    );
</script>

{#if href && !disabled}
    <a {href} {...$$restProps} class={buttonClass} role="button" on:click on:change on:keydown on:keyup on:touchstart|passive on:touchend on:touchcancel on:mouseenter on:mouseleave>
        <slot />
    </a>
{:else if tag === 'button'}
    <button {type} {...$$restProps} {disabled} class={buttonClass} on:click on:change on:keydown on:keyup on:touchstart|passive on:touchend on:touchcancel on:mouseenter on:mouseleave
            class:disable={disabled}
    >
        {#if disabled}
            <Spinner size="16" color="white" />
        {/if}
        <slot />
    </button>
{:else}
    <svelte:element this={tag} {...$$restProps} class={buttonClass}
    >
        <slot />
    </svelte:element>
{/if}

<style>
    button.currentC {
        background: var(--colorC);
    }
    button.currentB {
        background: var(--colorB);
    }
    .disable {
        opacity: 0.8;
    }
    button {
        border-radius: var(--buttonRadius);
    }
</style>
