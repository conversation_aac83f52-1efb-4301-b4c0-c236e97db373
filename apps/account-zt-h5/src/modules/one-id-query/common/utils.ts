import yoda from '@ks/yoda-js-sdk';
import axios from 'axios';
import { init as ssoInit, login as ssoLogin } from '@ks/sso';
import { isDev } from '@/common/config/base';
import { ONE_ID_QUERY_SID } from '@/common/config/constants';

// 设置顶部导航栏颜色
export function setTopBarBackgroundColor(color: string = '#ffffff') {
    setTimeout(() => {
        yoda.ready().then(
            () =>
                yoda.ui?.setTopBarStyle?.({
                    backgroundColor: color,
                    borderBottomColor: color,
                }),
        );
    });
}

// 在当前 RootDomain 种上登录态
export function setRootDomainToken(authTokenx: string, sidx: string) {
    const authToken = encodeURIComponent(authTokenx);
    const sid = encodeURIComponent(sidx);

    return axios.post(
        `/rest/infra/sts?authToken=${authToken}&sid=${sid}&setRootDomain=true`,
    );
}

// sso 登录
export function loginWithSMS(phoneInfo: { countryCode: string; phone: string; smsCode: string; }) {
    ssoInit({
        sid: ONE_ID_QUERY_SID,
        env: isDev() ? 'staging' : 'production',
        enableSig4: true,
    });

    // 1. 登录校验成功
    // 2. 调用 https://业务方域名/rest/infra/sts 种登录态到当前 root domain
    // 3. 跳转增长页面
    const { countryCode, phone, smsCode } = phoneInfo;
    return ssoLogin({
        countryCode,
        phone,
        smsCode,
    }).then(res => {
        return setRootDomainToken(res.authToken, res.sid);
    });
}
