<script lang="ts">
    import { getContext } from "svelte";
    import Modal from "$lib/shared/toast/Modal.svelte";


    const appTitle = getContext('appTitle');
    // const agreements = getContext('agreements') || [];

    const configStore = getContext('loginPageConfig');
    const baseAgreementOnClick: Function = $configStore?.bAccountConfig?.baseAgreementOnClick;
    const currentMainState = $configStore.currentMainState;
    const baseUrl = 'https://passport.kuaishou.com/b-account-h5/agreement'
    function onBaseAgreementClick() {
        baseAgreementOnClick && baseAgreementOnClick();
        window.open(baseUrl, '_blank');
    }
    let agreements: Array<{
        title: string;
        url: string;
        onClick?: Function;
    }>;
    if (currentMainState === 1) {
        agreements = $configStore?.cAccountConfig?.agreements ?? [];
    } else {
        const base = [{
            title: '《用户协议》',
            url: baseUrl,
            onClick: onBaseAgreementClick
        }];
        const bAgreements = $configStore?.bAccountConfig?.agreements ?? [];
        agreements = [
            ...base,
            ...bAgreements,
        ] ?? base;
    }
    export let isOpen: boolean = false;
    export let onAccept: () => void;
    export let onReject: () => void;

    function onClick(url: string, callback?: Function) {
        callback && callback();
        window.open(url, '_blank');
    }

</script>

<Modal open={isOpen}
       enableBackdrop={true}
       placement="bottom-center"
>
    <div class="bottom-alert">
        <div
                class="close-x"
                on:click={onReject}
        >
            ×
        </div>
        <div class="bottom-alert-title">用户协议及隐私保护</div>
        <div class="bottom-alert-content">
            我已阅读并同意{appTitle}{#each agreements as agreement, index}
            {#if index >= 1}、{/if}<span
                    on:click={() => onClick(agreement.url, agreement?.onClick)}
            >{agreement.title}</span>{/each}并授权{appTitle}获得本机号码
        </div>
        <div class="bottom-buttons">
            <div class="bottom-button"
                 on:click={onReject}
            >
                返回
            </div>
            <div class="bottom-button"
                 on:click={onAccept}
            >
                同意并登录
            </div>
        </div>
    </div>
</Modal>

<style>
    .bottom-alert {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        background: white;
        border-top-left-radius: 16px;
        border-top-right-radius: 16px;
        box-sizing: border-box;
        padding-top: 38px;
        padding-left: 32px;
        padding-right: 32px;
        padding-bottom: 50px;
    }
    .bottom-alert-title {
        color: #000000;
        font-size: 20px;
        font-weight: 500;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 28px;
        text-align: center;
        vertical-align: top;
    }
    .bottom-alert-content {
        color: #9C9C9C;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        text-align: left;
        vertical-align: top;
        margin-top: 10px;
        margin-bottom: 24px;
        padding-left: 8px;
        padding-right: 8px;
        word-break: break-all;
        white-space: normal;
    }
    .bottom-alert-content span {
        color: #385080ff;
        word-break: break-all;
    }
    .bottom-buttons {
        display: flex;
        gap: 16px;

    }
    .bottom-button {
        flex: 1;
        color: #222222;
        font-size: 15px;
        font-weight: 500;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 21px;
        text-align: center;
        vertical-align: top;
        padding-top: 12.5px;
        padding-bottom: 12.5px;
        border-style: solid;
        border-width: 1px;
        border-color: #C6C6C6;
        border-radius: 24px;
    }
    .bottom-button:last-child {
        background: #326BFBff;
        color: #FFFFFF;
        border-color: #326BFBff;
    }
    .close-x {
        position: absolute;
        top: 10px;
        right: 18px;
        font-weight: bolder;
        font-size: 20px;
    }
</style>
