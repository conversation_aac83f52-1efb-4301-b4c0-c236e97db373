<script lang="ts">
    import Wrapper from '$lib/utils/Wrapper.svelte';
    import { twMerge } from 'tailwind-merge';
    import { getContext } from 'svelte';
    import type { InputType, FormSizeType, SizeType } from '../types';
    import GlobalErrorMsg from "$lib/features/components/GlobalErrorMsg.svelte";

    export let type: InputType = 'text';
    export let errorMsg = '';
    export let globalErrorMsg = '';
    export let value: any = undefined;
    export let size: FormSizeType | undefined = undefined;
    export let defaultClass: string = 'input-tag border border-[#EAEAEA] outline-none py-[11px] pl-[12px] pr-[36px] text-[14px] leading-[24px] block w-full disabled:cursor-not-allowed disabled:opacity-50 rtl:text-right hover:border-[#4A84F7] focus:border-[#1D59F2] focus:shadow-[0_0_0_2px_rgba(0,117,255,0.2)]';
    export let color: 'base' = 'base';
    export let floatClass: string = 'flex absolute inset-y-0 items-center text-gray-500 dark:text-gray-400';

    const loginPageConfig = getContext('loginPageConfig');
    let inputClass: string;
    $: {
        // inputClass = [
        //     defaultClass,
        //     errorMsg && 'input-error',
        //     $$props.class,
        //     $loginPageConfig.cAccountConfig?.inputClass,
        // ].join(' ');
        inputClass = twMerge(
            defaultClass,
            errorMsg && 'input-error',
            $$props.class
        )
    }
</script>

<div class="relative w-full">
    <Wrapper class="w-full wrapper" show={$$slots.left || $$slots.right}>
        {#if $$slots.left}
            <div class="{twMerge(floatClass, $$props.classLeft)} start-0 ps-2.5 pointer-events-none">
                <slot name="left" />
            </div>
        {/if}
        <slot props={{ ...$$restProps, class: inputClass }}>
            <input {...$$restProps}
                   bind:value
                   on:focus
                   on:blur
                   on:change
                   on:click
                   on:contextmenu
                   on:keydown
                   on:keypress
                   on:keyup
                   on:mouseover
                   on:mouseenter
                   on:mouseleave
                   on:paste
                   on:input
                   {...{ type }}
                   class={inputClass}
            />
        </slot>
        {#if $$slots.right}
            <div class="{twMerge(floatClass, $$props.classRight)} end-0 pr-[12px]"><slot name="right" /></div>
        {/if}
        {#if errorMsg}
            <div class="error-msg absolute bottom-[-20px]"
            >
                {errorMsg}
            </div>
        {:else if globalErrorMsg}
            <GlobalErrorMsg loginErrorMsg={globalErrorMsg}
                            class="absolute bottom-[-36px]"
            ></GlobalErrorMsg>
        {/if}
    </Wrapper>
</div>
<style>
    .error-msg {
        color: #E02C1F;
        font-size: 12px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: left;
        vertical-align: top;
    }
    .input-tag {
        /*border: 1px solid #EAEAEA;*/
        border-radius: var(--inputRadius);
    }
    .input-tag::-webkit-input-placeholder {
        color: #9C9C9C;
        font-size: 16px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 22px;
        vertical-align: middle;
    }
    .input-tag:hover {
        /*border-color: #4A84F7;*/
    }
    .input-tag:focus {
        /*border-color: #1D59F2;*/
        /*box-shadow: 0 0 0 2px #0075ff33;*/
    }
    .input-error {
        /*background: #FFF2EC;*/
        border-color: #E02C1F;
    }
</style>
