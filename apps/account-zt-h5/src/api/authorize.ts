import request from './request';
import { baseUrl } from '@/common/config/base';

// 看文档: https://docs.corp.kuaishou.com/d/home/<USER>

interface getIconTextParam {
    fromSid: string;
    sid: string;
    'kuaishou.midground.api_st': string;
}

export interface IconText {
    result: number;
    fromIcon: string;
    fromName: string;
    targetIcon: string;
    targetName: string;
    text: string;
    nickName: string;
    headUrl: string;
}
export function getIconText(param: getIconTextParam) {
    return request.post<IconText>(`${baseUrl}/pass/kuaishou/fast/login/getIconText`, param);
}

interface convertTokenParam {
    sid: string;
    'kuaishou.midground.api_st': string;
    followUrl: string;
}

export interface Token {
    result: number;
    loginUrl: string;
}

export function convertToken(param: convertTokenParam) {
    return request.post<Token>(`${baseUrl}/pass/kuaishou/fast/login/convertToken`, param);
}
