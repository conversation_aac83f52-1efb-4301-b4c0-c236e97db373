import QrcodeVerify from "@/QrcodeVerify";
import { getRealNameCheckURL } from "@/account.service";

export class AccountQrcodeVerify extends QrcodeVerify {
    bizName!: string;
    authType!: number;

    constructor(props: {
        bizName: string;
        kpn: string;
        sid: string;
        qrType?: string;
        authType?: number;
    }) {
        const { bizName, authType = 2,qrType = 'identity-type', ...other } = props;
        super({
            ...other,
            qrType,
            serviceOwnParams: '',
        });
        this.bizName = bizName;
        this.authType = authType;
    }

    async start() {
        const params = {
            bizNameForIdCard: this.bizName,
            authType: this.authType
        }
        let { url } = await getRealNameCheckURL(params);
        this.serviceOwnParams = url + '&__f__=accountQrcode';
        return super.start();
    }
}
