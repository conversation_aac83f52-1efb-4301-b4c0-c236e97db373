/**
 * @file 验证的统一处理,不需要在其他文件中单独引包以及保存验证参数
 * // NOTE: 只适用于单接口
 */

import { startIdentityVerification } from '@ks/identity-verification';
import { Toast } from '@ks/sharp-ui';

export interface ZtIdentityVerification {
    ztIdentityVerificationCheckToken: string|undefined;
    ztIdentityVerificationType: number|undefined;
}

// 行为验证码
export const NEED_CAPTACHA_VERICATION = 400002;

/**
 * 获取验证参数
 */
let ztIdentityVerification: ZtIdentityVerification | null = null;

/**
 * 进行验证并保存验证参数用户回放
 * @param url
 * @returns
 */
export async function startIdentityVerify(url: string) {
    const { result, token, type, error_msg = '' } = await startIdentityVerification({ url });
    if (result !== 1) {
        Toast.error(error_msg || '验证失败，请重试');
        // 验证失败
        return false;
    }

    // 验证成功，返回token和type, 需要拿着这俩参数回放请求
    ztIdentityVerification = {
        ztIdentityVerificationCheckToken: token,
        ztIdentityVerificationType: type,
    };
    return true;
}

/**
 * 获取验证参数进行接口回放
 * @returns {ZtIdentityVerification | null}
 */
export function getZtIdentityVerification() {
    const identityVerification = ztIdentityVerification;
    ztIdentityVerification = null;
    return identityVerification;
}
