/**
 * @file 相关类型的定义
 */

export interface TrackerConfig {
    domains: string[]|Promise<string[]>; // 日志上报的可用域名
    callerSDK: { // 调用方信息
        name: string;
        version: string;
    };
    baseSDK: { // 基线版本信息
        name: string;
        version: string;
    };
    sid: string;
    channelType?: string;
}

export type LoggerConfig = TrackerConfig;

export interface ReportParams {
    startTime: number;
    endTime: number;
    result: 'SUCC' | 'EXCEPTION' | 'FAILED';
    request: {
        phone?: string;
        email?: string;
        account?: string;
    };  // 请求的参数
    response: {
        result: number;
        userId?: string;
    }; // 响应的结果
    apiUrl: string; // 接口的请求地址
    // 日志上报的类型
    reportType: 'service' | 'catch';
}

export interface ITraceBodyParams {
    // --- 自己的版本和名称
    ownerSDKName: string;
    ownerSDKVer: string;
    // ---调用方: SSO SDK版本 放在config中
    callerSDKName: string;
    callerSDKVer: string;
    // 接口的url地址(完整的)
    apiUrl: string;
    // 账号的手机号、邮箱等等  登录、注册接口；
    // 扫码拿不到，第三方登录拿不到；
    account?: string;
    //  --- 浏览器页面的站点地址
    siteUrl: string;
    // --- sid
    sid: string;
    // 接口的请求耗时
    duration: number;
    did: string;
    // --- 浏览器标识 上传到kafaka
    userAgent: string;
    // 日志上报的类型 'catch','service'
    reportType: string;
    // --- 渠道类型  PC_PAGE/PC_MODAL/H5_PAGE
    channelType: string;
    // 用户id，登录成功会返回
    userId?: number;
    // 只有失败发送，详细错误原因；服务端返回  // {code, errorMessage}
    // 1成功，非1的成功态值只有这一个：*********
    errorDetail?: Record<string, unknown>;
}

export interface ITraceBody {
    verifySessionId: string;
    // upStreamSpanName, spanName, spanContexts这三个参数目前用不上，随便填的
    upStreamSpanName: string;
    spanName: string;
    spanContexts: string;
    startTime: number;
    endTime: number;
    reportTime: 0;
    result: ReportParams['result'];
    returnResult: {
        resultCode: ReportParams['response']['result'];
    };
    category: string;
    sid: string;
    did: string;
    userId?: string;
    params: ITraceBodyParams;
}

