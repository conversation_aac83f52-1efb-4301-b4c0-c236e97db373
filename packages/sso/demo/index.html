<!DOCTYPE html>
<html>

<head>
    <meta charset="utf8">
    <script type="module" src="index.js"></script>
</head>

<body>
    <fieldset>
        <legend>验证码登录(phone+countryCode)</legend>
        <form onsubmit="login(event); return false;">
            <label>phone:
                <input type="text" name="phone" value="">
            </label>
            <label>countryCode:
                <input type="text" name="countryCode" value="+86">
            </label>
            <label>smsCode:
                <input type="text" name="smsCode" value="">
            </label>
            <span>createId:
                <label><input type="radio" name="createId" value="1" checked="checked">true</label>
                <label><input type="radio" name="createId" value="">false</label>
            </span>|
            <span>setCookie:
                <label><input type="radio" name="setCookie" value="1" checked="checked">true</label>
                <label><input type="radio" name="setCookie" value="">false</label>
            </span>
            <button type="submit">登录</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>多帐户情况下选择用户</legend>
        <form onsubmit="chooseUser(event); return false;">
            <label>phone:
                <input type="text" name="phone" value="">
            </label>
            <label>countryCode:
                <input type="text" name="countryCode" value="+86">
            </label>
            <label>userId:
                <input type="text" name="userId" value="">
            </label>
            <label>multiUserToken:
                <input type="text" name="multiUserToken" value="">
            </label>
            <span>setCookie:
                <label><input type="radio" name="setCookie" value="1" checked="checked">true</label>
                <label><input type="radio" name="setCookie" value="">false</label>
            </span>
            <button type="submit">选择用户登录</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>密码登录(phone+password)</legend>
        <form onsubmit="passwordLogin(event); return false;">
            <label>phone:
                <input type="text" name="phone">
            </label>
            <label>password:
                <input type="text" name="password" value="a666!!!@@@#">
            </label>
            <input type="hidden" name="account">
            <button type="submit">登录</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>密码登录(account:即邮箱/手机号)</legend>
        <form onsubmit="passwordLogin(event); return false;">
            <label>account:
                <input type="text" name="account">
            </label>
            <label>password:
                <input type="text" name="password" value="a666!!!@@@#">
            </label>
            <input type="hidden" name="phone">
            <button type="submit">登录</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>发送验证码</legend>
        <form onsubmit="requestMobileCode(event); return false;">
            <label>phone:
                <input type="text" name="phone">
            </label>
            <label>
                <input type="radio" name="type" value="53" checked="checked">登录
            </label>
            <label>
                <input type="radio" name="type" value="42">注册
            </label>
            <button type="submit">发送</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>发送邮箱验证码</legend>
        <form onsubmit="requestEmailCode(event); return false;">
            <label>email:
                <input type="text" name="email">
            </label>
            <label>
                <input type="radio" name="type" value="160" checked="checked">登录
            </label>
            <label>
                <input type="radio" name="type" value="15">注册
            </label>
            <button type="submit">发送</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>邮箱登录</legend>
        <form onsubmit="emailLogin(event); return false;">
            <label>email:
                <input type="text" name="email" value="+<EMAIL>">
            </label>
            <label>emailCode:
                <input type="text" name="emailCode" value="910688">
            </label>
            <button type="submit">登录</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>注册邮箱</legend>
        <form onsubmit="emailRegister(event); return false;">
            <label>email(测试用+110开头的邮箱):
                <input type="text" name="email" value="+<EMAIL>">
            </label>
            <label>password:
                <input type="text" name="password" value="a666!!!@@@">
            </label>
            <label>emailCode(测试用910688):
                <input type="text" name="emailCode" value="910688">
            </label>
            <span>setCookie:
                <label><input type="radio" name="setCookie" value="1" checked="checked">true</label>
                <label><input type="radio" name="setCookie" value="">false</label>
            </span>
            <button type="submit">注册</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>通过邮箱验证码重置密码</legend>
        <form onsubmit="resetPasswordByEmail(event); return false;">
            <label>email:
                <input type="text" name="email" value="+<EMAIL>">
            </label>
            <label>emailCode(测试用910688):
                <input type="text" name="emailCode" value="910688">
            </label>
            <label>password:
                <input type="text" name="password" value="">
            </label>
            <button type="submit">重置密码</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>通过手机验证码重置密码前选择账号</legend>
        <form onsubmit="getKsAccounts(event); return false;">
            <label>phone:
                <input type="text" name="phone" value="">
            </label>
            <label>countryCode:
                <input type="text" name="countryCode" value="+86">
            </label>
            <label>smsCode:
                <input type="text" name="smsCode" value="">
            </label>
            <button type="submit">请求可选择的账号</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>通过手机验证码重置密码</legend>
        <form onsubmit="resetPasswordByPhone(event); return false;">
            <label>phone:
                <input type="text" name="phone" value="">
            </label>
            <label>countryCode:
                <input type="text" name="countryCode" value="+86">
            </label>
            <label>smsCode:
                <input type="text" name="smsCode" value="">
            </label>
            <label>password:
                <input type="text" name="password" value="">
            </label>
            <label>targetUserId:
                <input type="text" name="targetUserId" value="">
            </label>
            <label>multiUserToken:
                <input type="text" name="multiUserToken" value="">
            </label>
            <button type="submit">重置密码</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>校验密码强度</legend>
        <form onsubmit="checkPassStrength(event); return false;">
            <label>password:
                <input type="text" name="password" value="">
            </label>
            <button type="submit">校验</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>获取国家码列表</legend>
        <form onsubmit="getCountryCodeList(event); return false;">
            <button type="submit">获取</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>验证当前绑定手机（须登录）</legend>
        <form onsubmit="validatePhone(event); return false;">
            <label>smsCode:
                <input type="text" name="smsCode" value="">
            </label>
            <label>type:
                <input type="text" name="type" value="">
            </label>
            <button type="submit">验证</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>验证当前绑定邮箱（须登录）</legend>
        <form onsubmit="validateEmail(event); return false;">
            <label>emailCode:
                <input type="text" name="emailCode" value="">
            </label>
            <label>type:
                <input type="text" name="type" value="">
            </label>
            <button type="submit">验证</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>绑定/换绑手机（须登录）</legend>
        <form onsubmit="bindPhone(event); return false;">
            <label>phone:
                <input type="text" name="phone" value="">
            </label>
            <label>countryCode:
                <input type="text" name="countryCode" value="+86">
            </label>
            <label>newSmsCode:
                <input type="text" name="newSmsCode" value="">
            </label>
            <label>originalSmsCode:
                <input type="text" name="originalSmsCode" value="">
            </label>
            <label>originalEmailCode:
                <input type="text" name="originalEmailCode" value="">
            </label>
            <button type="submit">绑定</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>绑定/换绑邮箱（须登录）</legend>
        <form onsubmit="bindEmail(event); return false;">
            <label>email:
                <input type="text" name="email" value="">
            </label>
            <label>newEmailCode:
                <input type="text" name="newEmailCode" value="">
            </label>
            <label>originalSmsCode:
                <input type="text" name="originalSmsCode" value="">
            </label>
            <label>originalEmailCode:
                <input type="text" name="originalEmailCode" value="">
            </label>
            <button type="submit">绑定</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>获取当前账户信息（须登录）</legend>
        <form onsubmit="getAccountInfo(event); return false;">
            <label>accountInfoType:
                <input type="text" name="accountInfoType" value="PWD_UPDATE_TIME">
            </label>
            <button type="submit">获取</button>
        </form>
    </fieldset>

    <fieldset>
        <legend>登录后 调用 stsUrl </legend>
        <form onsubmit="saveToken(event); return false;">
            <label>phone:
                <input type="text" name="phone" value="***********">
            </label>
            <label>password:
                <input type="text" name="password" value="Pa552021">
            </label>
            <input type="hidden" name="account">
            <button type="submit">登录</button>
        </form>
    </fieldset>

    <fieldset>
        <legend>登录后 调用 stsUrl iframe </legend>
        <form onsubmit="saveTokenInIframe(event); return false;">
            <label>phone:
                <input type="text" name="phone" value="***********">
            </label>
            <label>password:
                <input type="text" name="password" value="Pa552021">
            </label>
            <input type="hidden" name="account">
            <button type="submit">登录</button>
        </form>
    </fieldset>

    <fieldset>
        <legend>检查第三方登录时传入的 callbackUrl/followUrl 是否合法</legend>
        <form onsubmit="isLegalCallbackUrl(event); return false;">
            <label>callbackUrl:
                <input type="text" name="callbackUrl" value="https://www.baidu.com">
            </label>
            <button type="submit">检查</button>
        </form>
    </fieldset>

</body>

</html>
