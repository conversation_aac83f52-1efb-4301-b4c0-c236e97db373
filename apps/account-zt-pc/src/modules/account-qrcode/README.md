# 直播伴侣 开播二维码

* [需求](https://docs.corp.kuaishou.com/d/home/<USER>
* [技术方案](https://docs.corp.kuaishou.com/d/home/<USER>
* [接口](https://wiki.corp.kuaishou.com/pages/viewpage.action?pageId=********#id-4.Http%E6%8E%A5%E5%8F%A3%E8%AE%BE%E8%AE%A1-19.%E8%8E%B7%E5%8F%96%E4%BA%8C%E7%BB%B4%E7%A0%81%E8%AE%A4%E8%AF%81url)


### 测试线地址

http://account-live-qrcode-dev.appdev.test.gifshow.com/pc/account-qrcode/?sid=kuaishou.live.mate&kpn=KUAISHOU_LIVE_MATE&bizName=LIVE_STREAM_START

### 正式环境 搭建中

https://passport.kuaishou.com/pc/account-qrcode/?sid=kuaishou.live.mate&kpn=KUAISHOU_LIVE_MATE&bizName=LIVE_STREAM_START

## 需要的cookie

* `uid`: 用户id
* `did`: 设备id
* `<sid>_st`: 名称与 sid 一致 serviceToken



## 返回数据

通过 `submitData(res: JSON)` 函数返回结果

```typescript
interface AccountQrcodeResult {
  result: number;
  errorMsg: string;
  qrLoginSignature: string;
  serviceOwnToken: string;
  qrAuthToken: string;
}
```
