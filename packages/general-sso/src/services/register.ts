import { getConfig } from '@/core/configInit';
import { DEFAULT_COUNTRY_CODE } from '@/config';
import { getLoginToken } from '@/core/loginToken';
import { genEncryptReqParams } from '@/utils';

/**
 * 手机号+短信验证码+密码注册
 */
export interface smsCodeRegisterData {
    phone: string;
    countryCode?: string;
    smsCode: string | number;
    password: string;
    setCookie?: boolean;
    invitationCode?: string;
}
export function smsCodeRegister({
    phone,
    countryCode = DEFAULT_COUNTRY_CODE,
    smsCode,
    password,
    setCookie = true,
    invitationCode,
}: smsCodeRegisterData) {
    const config = getConfig();
    return getLoginToken(`/pass/${config.appName}/web/register/mobilePassword`, {
        sid: config.sid,
        countryCode,
        phone,
        smsCode,
        password,
        setCookie,
        invitationCode,
    });
}

/**
 * 手机号+短信验证码+密码注册(加密版)
 */
export function smsCodeRegisterV2({
    phone,
    countryCode = DEFAULT_COUNTRY_CODE,
    smsCode,
    password,
    setCookie = true,
    invitationCode,
}: smsCodeRegisterData) {
    const config = getConfig();
    const encryptReqParams = genEncryptReqParams({
        phone,
        password,
    });
    const params = {
        sid: config.sid,
        countryCode,
        smsCode,
        setCookie,
        invitationCode,
        ...encryptReqParams,
    };

    return getLoginToken(`/pass/${config.appName}/web/register/mobilePassword/v2`, {
        ...params,
    });
}

/**
 * 邮箱+邮箱验证码+密码注册
 */
export interface emailRegisterData {
    email: string;
    emailCode: string | number;
    password: string;
    setCookie?: boolean;
    invitationCode?: string;
}
export function emailRegister({ email, emailCode, password, setCookie = true, invitationCode }: emailRegisterData) {
    const config = getConfig();
    return getLoginToken(`/pass/${config.appName}/web/register/emailPassword`, {
        sid: config.sid,
        email,
        emailCode,
        password,
        setCookie,
        invitationCode,
    });
}

export function emailRegisterV2({ email, emailCode, password, setCookie = true, invitationCode }: emailRegisterData) {
    const config = getConfig();
    const encryptReqParams = genEncryptReqParams({
        email,
        password,
    });
    return getLoginToken(`/pass/${config.appName}/web/register/emailPassword/v2`, {
        sid: config.sid,
        emailCode,
        setCookie,
        invitationCode,
        ...encryptReqParams,
    });
}
