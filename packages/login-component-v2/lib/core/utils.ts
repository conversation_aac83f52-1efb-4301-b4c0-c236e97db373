function stringifyPrimitive(v: string | boolean | number | undefined) {
    switch (typeof v) {
        case 'string':
            return v;
        case 'boolean':
            return v ? 'true' : 'false';
        case 'number':
            return isFinite(v) ? v : '';
        default:
            return '';
    }
}
export function stringify(obj: any, sep = '&', eq = '=') {
    if (obj === null) {
        obj = undefined;
    }
    if (typeof obj === 'object') {
        return Object.keys(obj).map((k) => {
            const ks = encodeURIComponent(stringifyPrimitive(k)) + eq;
            if (Array.isArray(obj[k])) {
                return obj[k].map((v: any) => {
                    return ks + encodeURIComponent(stringifyPrimitive(v));
                }).join(sep);
            }
            return ks + encodeURIComponent(stringifyPrimitive(obj[k]));
        }).join(sep);
    }
    return encodeURIComponent(stringifyPrimitive(obj)) + eq + encodeURIComponent(stringifyPrimitive(obj));
}


export function getDomainFormUrl(url: string) : string {
    const regx = /^(?:(?:[a-z]+)?:\/\/)?([^/]+)/
    const match = url.match(regx);
    return match ? match[1] : ''
}
