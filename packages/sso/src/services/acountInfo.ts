import { request } from '@/core/request';
import { privateGetConfig } from '@/core/configInit';
import { BaseRes } from '../types/services';

export interface getAccountInfoOptions {
    accountInfoType: string[];
}

export interface getAccountInfoRes extends BaseRes {
    lastUpdateTime?: number;
    email?: string;
    phone?: string;
    countryCode?: string;
    fullPhoneNumber?: string;
    textFullPhone?: string;
    textPhone?: string;
}

export function getAccountInfo({ accountInfoType }: getAccountInfoOptions) {
    const config = privateGetConfig();
    return request<getAccountInfoRes>(config.baseUrl + '/pass/kuaishou/account/info', {
        sid: config.sid,
        accountInfoType: accountInfoType.join(','),
    });
}

