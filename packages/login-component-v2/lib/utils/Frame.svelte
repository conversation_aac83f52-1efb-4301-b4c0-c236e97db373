<script context="module" lang="ts">
    export type FrameColor = keyof typeof bgColors;
    const bgColors = {
        gray: 'bg-gray-50 ',
        red: 'bg-[#FFF2EC] ',
        yellow: 'bg-yellow-50 ',
        green: 'bg-green-50 ',
        indigo: 'bg-indigo-50  ',
        purple: 'bg-purple-50  ',
        pink: 'bg-pink-50  ',
        blue: 'bg-blue-50  ',
        light: 'bg-gray-50 ',
        dark: 'bg-gray-50 ',
        default: 'bg-white ',
        dropdown: 'bg-white ',
        navbar: 'bg-white ',
        navbarUl: 'bg-gray-50 ',
        form: 'bg-gray-50 ',
        primary: 'bg-primary-50  ',
        orange: 'bg-orange-50 ',
        none: ''
    };
</script>

<script lang="ts">
    import { createEventDispatcher, setContext } from 'svelte';
    import { twMerge } from 'tailwind-merge';

    import type { Action } from 'svelte/action';
    import type { HTMLAnchorAttributes } from 'svelte/elements';
    import { type TransitionConfig } from 'svelte/transition';

    const noop = () => {};


    type TransitionFunc = (node: HTMLElement, params: any) => TransitionConfig;

    interface $$Props extends HTMLAnchorAttributes {
        tag?: string;
        color?: FrameColor;
        rounded?: boolean;
        border?: boolean;
        shadow?: boolean;
        node?: HTMLElement | undefined;
        use?: Action<HTMLElement, any>;
        options?: object;
        class?: string;
        role?: string;
        open?: boolean;
        transition?: TransitionFunc;
        params?: any;
    }

    setContext('background', true);

    export let tag: string = $$restProps.href ? 'a' : 'div';
    export let color: FrameColor = 'default';
    export let rounded: boolean = false;
    export let border: boolean = false;
    export let shadow: boolean = false;

    // For components development
    export let node: HTMLElement | undefined = undefined;
    // Action function and its params
    export let use: Action<HTMLElement, any> = noop;
    export let options = {};

    export let role: string | undefined = undefined;

    // Export a prop through which you can set a desired svelte transition
    export let transition: TransitionFunc | undefined = undefined;
    // Pass in extra transition params
    export let params: object = {};

    export let open: boolean = true;

    const dispatch = createEventDispatcher();
    $: dispatch(open ? 'open' : 'close');
    $: dispatch('show', open);

    $: color = color ?? 'default'; // for cases when undefined
    $: setContext('color', color);


    let divClass: string;
    $: divClass = twMerge(border && 'border', $$props.class);
</script>

{#if transition && open}
    <svelte:element this={tag} transition:transition={params} use:use={options} bind:this={node} {role} {...$$restProps} class={divClass} on:click on:mouseenter on:mouseleave on:focusin on:focusout>
        <slot />
    </svelte:element>
{:else if open}
    <svelte:element this={tag} use:use={options} bind:this={node} {role} {...$$restProps} class={divClass} on:click on:mouseenter on:mouseleave on:focusin on:focusout>
        <slot />
    </svelte:element>
{/if}

<style>
div {
    border-radius: var(--toolTipRadius);
    /*background: #000000;*/
    /*opacity: 0.9;*/
}
</style>
