<script lang="ts">
    import { getContext, createEventDispatcher } from 'svelte';


    import Button from '$lib/shared/button/index.svelte';
    import { passwordLogin } from '@ks/sso';

    import Input from '$lib/shared/forms/input.svelte';
    import CloseButton from "$lib/utils/CloseButton.svelte";
    import SeePassword from "$lib/utils/SeePassword.svelte";
    import HidePassword from "$lib/utils/HidePassword.svelte";
    import DropDown from "$lib/shared/dropdown/DropDown.svelte";
    import ViceOperate from "$lib/features/components/viceOperate/ViceOperate.svelte";
    import RecommendLoginC from "$lib/features/components/RecommendLoginC.svelte";

    import { createPasswordStore } from '$lib/store/passwordStore';
    import { bindPhoneInfo, multiUserInfo } from '$lib/features/stage1/kuaishou/store';
    import { CODE_MULTICHOOSE, CODE_NEED_BIND, CODE_NEED_BIND_V2, EnumLoginType, EnumPageState } from "$lib/shared/const";
    import BackChoose from "$lib/features/components/BackChoose.svelte";
    import { filterInput } from "$lib/utils/validate";

    const configStore = getContext('loginPageConfig');

    let passwordLoginTypes = $configStore.cAccountConfig?.passwordLoginTypes;
    let viceOperateConfig = $configStore.cAccountConfig?.viceOperateConfig;
    let otherOperateConfig = $configStore.cAccountConfig?.otherOperateConfig;
    let inputClass = $configStore.cAccountConfig?.inputClass || '';

    const store = createPasswordStore(passwordLoginTypes);

    const dispatch = createEventDispatcher();


    let showPassword = false;
    let loginErrorMsg = '';

    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    const onLoginStart = getContext('onLoginStart');

    let buttondisabled = false;
    async function loginSubmit() {
        if (!store.reducers.validateLogin()) {
            return;
        }
        try {
            onLoginStart({
                name: EnumLoginType.passwordC,
            });
            buttondisabled = true;
            const res = await passwordLogin({
                account: $store.account,
                password: $store.password,
            });
            buttondisabled = false;
            console.log('res is', res, onSuccess);
            onSuccess({
                loginType: EnumLoginType.passwordC,
                result: res,
            });
        } catch (error) {
            buttondisabled = false;
            console.log('error', error, error?.result);
            if (error?.result === CODE_MULTICHOOSE) {
                // 存在两个账号的情况，需要选择
                multiUserInfo.set({
                    userInfos: error.userInfos,
                    multiUserToken: error.multiUserToken,
                    phone: $store.account,
                    loginType: 'password',
                });
                dispatch('updatePageState', EnumPageState.chooseUser);
                return;
            } else if ([CODE_NEED_BIND, CODE_NEED_BIND_V2].includes(error?.result)) {
                // 需要跳转到绑定手机号页面
                bindPhoneInfo.set({
                    phone: error.bindInfo.phone,
                    bindAuthToken: error.bindInfo.bindAuthToken,
                    quickLoginResponse: error,
                });
                console.log('EnumPageState.bindPhone', EnumPageState.bindPhone);
                dispatch('updatePageState', EnumPageState.bindPhone);
                return;
            }
            loginErrorMsg = error.error_msg || '网络错误，请稍后重试';
            onFail({
                loginType: EnumLoginType.passwordC,
                error,
            });
        }
    }

    function updatePageState(s) {
        console.log('s is', s);
        dispatch('updatePageState', s.detail);
    }
</script>

<div class="w-full mt-[8px]">
    <div class="mb-[24px] relative">
        <Input placeholder={$store.placeholder}
               bind:value={$store.account}
               on:input={store.reducers.updateInputValue}
               on:focus={() => {
                   store.setValues({
                       accountErrorMsg: '',
                   });
               }}
               class={inputClass}
               on:blur={store.reducers.onAccountBlur}
               on:keydown={store.reducers.handleKeydown}
               bind:ref={$store.emailInput}
               errorMsg={$store.accountErrorMsg}
        >
            <slot slot="right">
                {#if $store.account}
                    <CloseButton on:click={store.reducers.clearInputValue}/>
                {/if}
            </slot>
        </Input><DropDown
                class="absolute z-30 w-full currentC"
                visible="{$store.emailDropdownVisible}"
                tooltips="请选择邮箱类型"
                current="{$store.emailHighlightIndex}"
                options="{$store.emailDropdownList}"
                onSelect="{store.reducers.handleEmailSelect}"
        />
    </div>

    <div class="mb-[44px]">
        <Input placeholder="请输入密码"
               bind:value={$store.password}
               on:input={filterInput}
               on:focus={() => {
                   store.setValues({
                       passwordErrorMsg: '',
                   });
               }}
               class={inputClass}
               on:blur={store.reducers.onPasswordBlur}
               type={showPassword ? 'text': 'password'}
               errorMsg={$store.passwordErrorMsg}
               globalErrorMsg={loginErrorMsg}
               maxlength={20}
        >
            <slot slot="right">
                {#if showPassword}
                    <SeePassword on:click={() => (showPassword = !showPassword)}/>
                {:else}
                    <HidePassword on:click={() => (showPassword = !showPassword)}/>
                {/if}
            </slot>
        </Input>
    </div>
    <Button class="currentC"
            on:click={loginSubmit}
            disabled={buttondisabled}
    >
        快手APP账号授权登录
    </Button>
    <ViceOperate
            currentType="passwordForm"
            on:updatePageState={updatePageState}
            viceOperateConfig={viceOperateConfig}
    />
    <BackChoose {otherOperateConfig} />
    <RecommendLoginC />
</div>

<style>
</style>
