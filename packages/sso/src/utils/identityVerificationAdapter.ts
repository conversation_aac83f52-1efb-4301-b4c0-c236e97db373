import {
    startIdentityVerification as _startIdentityVerification,
    StartIdentityVerificationOptionsPC,
    getPackageVersion,
} from '@ks/identity-verification';

interface StartIdentityVerificationOptions extends StartIdentityVerificationOptionsPC {
    platform?: 'PC' | 'H5' | 'OUTSIDE_H5';
}

// 兼容 @ks/identity-verification@0.x 和 @ks/identity-verification@1.x 逻辑
function startIdentityVerification(options: StartIdentityVerificationOptions) {
    // @ks/identity-verification@0.x 逻辑
    if (getPackageVersion().startsWith('0')) {
        return _startIdentityVerification(options).catch(res => {
            if (res.result === 2) {
                return res;
            }

            return Promise.reject(res);
        });
    }

    // @ks/identity-verification@1.x 逻辑
    return _startIdentityVerification(options);
}

export { startIdentityVerification };
