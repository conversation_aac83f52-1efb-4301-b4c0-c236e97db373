# @ks/general-sso

## 0.2.14-beta.9

### Patch Changes

- 透出 passToken 和 bUserId

## 0.2.14-beta.8

### Patch Changes

- bindOtherPhoneAndLogin 修改参数

## 0.2.14-beta.7

### Patch Changes

- bindOtherPhoneAndLogin 接口修改

## 0.2.14-beta.6

### Patch Changes

- 新增绑定冲突相关接口

## 0.2.14-beta.5

### Patch Changes

- 去除 h5-sig4 peers

## 0.2.14-beta.4

### Patch Changes

- 取消动态引入 sig4-lite

## 0.2.14-beta.3

### Patch Changes

- 设置 @ks-cqc/h5-sig4 为 external

## 0.2.14-beta.2

### Patch Changes

- 集成全量版本 sig4

## 0.2.13

### Patch Changes

- f7c1ed77: 提供 nobundle 产物，放在 xiaodian 目录下
- 开启全量 sig4
- 69063c2b: 接口全部开启 sig4
- e467370c: 提供 nobundle 产物，放在 xiaodian 目录下
- 0b01d712: 使用 father 打包 nobundle 产物
- 15842a97: 解析@符号

## 0.2.13-beta.4

### Patch Changes

- 接口全部开启 sig4

## 0.2.13-beta.3

### Patch Changes

- 使用 father 打包 nobundle 产物

## 0.2.13-beta.2

### Patch Changes

- 解析@符号

## 0.2.13-beta.1

### Patch Changes

- 提供 nobundle 产物，放在 xiaodian 目录下

## 0.2.13-beta.0

### Patch Changes

- 提供 nobundle 产物，放在 xiaodian 目录下

## 0.2.11

### Patch Changes

- 7686da94: 增加雷达上报
- 84baf3d8: pull & sync 逻辑
- 5204ea4b: 增加 web 接口
- d97835cc: 登录和发送验证码兼容 account
- 826bc58f: 增加类型声明
- 167faf3c: debug
- acf1200c: 取消设置 cookie 的 kpn
- fe4814fc: sig4 名单配置
- ac613c6f: sig4 过滤 null
- 30c38cdd: sig4 log
- 175e95f8: sig4 兼容加密逻辑
- cbf491c7: multiTokenLogin v2 接口
- 0a22791c: 上传版本号
- 87404c9a: 修改 cancelRequest 以适配 sig4
- 16df36bd: 增加 isWebSig4 参数
- dc75a291: request 逻辑兼容
- 7a7ee215: sig4 日志上传

## 0.2.11-beta.22

### Patch Changes

- 修改 cancelRequest 以适配 sig4

## 0.2.11-beta.21

### Patch Changes

- 登录和发送验证码兼容 account

## 0.2.11-beta.20

### Patch Changes

- sig4 过滤 null

## 0.2.11-beta.19

### Patch Changes

- 取消设置 cookie 的 kpn

## 0.2.11-beta.18

### Patch Changes

- 增加 isWebSig4 参数

## 0.2.11-beta.17

### Patch Changes

- sig4 日志上传

## 0.2.11-beta.16

### Patch Changes

- multiTokenLogin v2 接口

## 0.2.11-beta.15

### Patch Changes

- request 逻辑兼容

## 0.2.11-beta.14

### Patch Changes

- debug

## 0.2.11-beta.13

### Patch Changes

- sig4 log

## 0.2.11-beta.12

### Patch Changes

- pull & sync 逻辑

## 0.2.11-beta.11

### Patch Changes

- sig4 兼容加密逻辑

## 0.2.11-beta.10

### Patch Changes

- sig4 名单配置

## 0.2.11-beta.9

### Patch Changes

- 增加 web 接口

## 0.2.11-beta.8

### Patch Changes

- 增加类型声明

## 0.2.11-beta.7

### Patch Changes

- 上传版本号

## 0.2.11-beta.6

### Patch Changes

- 增加雷达上报

## 0.2.11-beta.5

### Patch Changes

- getCountryCodeList 调整

## 0.2.11-beta.4

### Patch Changes

- package.json 增加 exports

## 0.2.11-beta.3

### Patch Changes

- 升级至最新版本号

## 0.2.11-beta.2

### Patch Changes

- 升级至最新版本号

## 0.2.11-beta.1

### Patch Changes

- 增加 b 端登录逻辑

## 0.2.11-beta.0

### Patch Changes

- 增加邮箱密码登录加密接口

## 0.2.10

### Patch Changes

- ae17e7c6: chore: 修改 passwordChange 为 changePasswordByPassword
- bc587ede: chore: 增加国密加密逻辑、根据密码修改密码接口
- 39234d22: chore: sync
- 39234d22: feat: set kpn in cookie on init
- 39234d22: chore: sync
- 2d9540e0: feat: 修改国密 sm3 加签逻辑
- 39234d22: fix: type error
- 39234d22: chore: sync

## 0.2.10-beta.7

### Patch Changes

- feat: 修改国密 sm3 加签逻辑

## 0.2.10-beta.6

### Patch Changes

- feat: set kpn in cookie on init

## 0.2.10-beta.5

### Patch Changes

- chore: sync

## 0.2.10-beta.4

### Patch Changes

- chore: sync

## 0.2.10-beta.3

### Patch Changes

- chore: sync

## 0.2.10-beta.2

### Patch Changes

- fix: type error

## 0.2.10-beta.1

### Patch Changes

- chore: 修改 passwordChange 为 changePasswordByPassword

## 0.2.10-beta.0

### Patch Changes

- chore: 增加国密加密逻辑、根据密码修改密码接口

## 0.2.9

- 更高版本的 CHANGELOG 另请参见[在线文档](https://docs.corp.kuaishou.com/d/home/<USER>

## [0.2.6](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/general-sso/0.2.6-beta.4...@ks/general-sso/0.2.6) (2022-09-15)

### Features

- 升级验证包 ([1932e30](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/1932e30783b3d424b012d001c76e28a71b151e4d))

## [0.2.6-beta.4](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/general-sso/0.2.6-beta.3...@ks/general-sso/0.2.6-beta.4) (2022-09-01)

### Features

- 升级 identity-verification 版本 ([a3d01d6](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/a3d01d6ba3dd5091ebbdf1b5fa972422104a0a21))

## [0.2.6-beta.3](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/general-sso/0.2.6-beta.2...@ks/general-sso/0.2.6-beta.3) (2022-08-24)

### Features

- 保存 platform ([b0991c5](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/b0991c55c057746b36cdb3402e7cba8a8d1c60b0))

## [0.2.6-beta.2](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/general-sso/0.2.3...@ks/general-sso/0.2.6-beta.2) (2022-08-24)

### Bug Fixes

- 修复 general-sso 密码明文传输问题 ([c4c5fe2](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/c4c5fe2a48d38ce1115f1d16d531888f7751f58b))
- 修复 postMessage 报错问题 ([e21a553](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/e21a5537da962cb230f0e67be8249ddd0b9beea0))
- 修复密码明文传输的问题 ([3fcd327](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/3fcd32765ce37c0238b46557436ea33e8e27a7fc))
- **general-sso:** 修复密码明文传输问题 ([ee09903](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/ee099034add59da0e04c6d8d8a7bec8a118dc30b))

### Features

- gengral-sso 增加 platform ([a0feef8](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/a0feef8fd3e58d8abb29db59d76432764b56cf8e))

## [0.2.6-beta.1](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/general-sso/0.2.6-beta.0...@ks/general-sso/0.2.6-beta.1) (2022-08-03)

### Bug Fixes

- **general-sso:** 修复密码明文传输问题 ([e212c00](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/e212c00a5443907c0c117c05037576c5d0a608be))

## [0.2.6-beta.0](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/general-sso/0.2.5...@ks/general-sso/0.2.6-beta.0) (2022-07-28)

### Bug Fixes

- 修复 general-sso 密码明文传输问题 ([986a9af](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/986a9afac5c17e0b0ee85e72f68e81aea182fed5))
- 修复密码明文传输的问题 ([b8a69af](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/b8a69afa76239ad05cee40b9defc4425b3695f9f))

## [0.2.5](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/general-sso/0.2.2-beta.11...@ks/general-sso/0.2.5) (2022-07-20)

### Bug Fixes

- 修复 postMessage 报错问题 ([e21a553](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/e21a5537da962cb230f0e67be8249ddd0b9beea0))

### Features

- edit changelog.md ([d7fb465](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/d7fb46502460b83d3cd443cae57343340d54ee40))

## [0.2.5-alpha.0](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/general-sso/0.2.3...@ks/general-sso/0.2.5-alpha.0) (2022-07-15)

### Features

- 阻止本地发布 ([f6273ae](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/f6273aee25979377610bca2a667edf41a4799581))
- sh 文件 ([fa0d9f9](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/fa0d9f9b3889ab0ba20e8f78da65f0c13fb4ff1e))

## [0.2.4](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/general-sso/0.2.3...@ks/general-sso/0.2.4) (2022-07-13)

### Bug Fixes

- 修复未构建依赖包 ([bb36192](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/bb36192c77f698ce6c4f1ea28f82d56f1cb2728a))

## [0.2.3](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/compare/@ks/general-sso/0.2.2-beta.11...@ks/general-sso/0.2.3) (2022-07-06)

### Bug Fixes

\*将 sdk-version 开发时依赖 ([737b758](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/737b7584ff42fcc94f7140c642c2f86a1ceb0103))

### Features

- edit changelog.md ([d7fb465](https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general/commits/d7fb46502460b83d3cd443cae57343340d54ee40))

# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [0.2.2-beta.13] 2022-06-24

为 @ks/identity-verification 添加 qrcodeEnv 以启用 PC 二维码

### [0.2.2-beta.12] 2022-06-23

透传 @ks/identity-verification 的-999

### [0.2.2-beta.11] 2022-06-02

#### Features

- `refreshLoginStatus`方法增加`openFollowUrlInNewWindow`参数

### [0.2.2-beta.10] 2022-05-31

根域种 cookie

### [0.2.2-beta.8] 2022-03-18

支持多语言

### [0.2.2-beta.7] 2022-03-10

增加 passToken 方法

### [0.2.2-beta.3] 2022-03-03

升级行为验证依赖包

### [0.2.1-beta.2] 2022-01-05

idc-占位符注入

### [0.2.0-alpha.1] 2021-07-06

增加 setLanguage 和 init({ language }) 配置

### [0.2.0-alpha.0] 2021-07-06

弃用 passToken 接口

### [0.1.2] (2021-03-04)

增加新方法：

- `手机号+短信验证码+密码注册账号`
- `邮箱+邮箱验证码+密码注册账号`
- `邮箱+密码登录`
- `发送邮箱验证码`
- `通过邮箱重置密码`
- `校验密码强度`
- `检查手机号是否可用`
- `检查邮箱是否可用`

### [0.1.1] (2021-01-20)

增加新方法：

- `密码登录`
- `多帐户选择用户登录`
- `同步token`
- `通过短信验证码重置密码`
- `退出登录`

### [0.1.0] (2020-11-05)

初始化项目，增加新方法：

- `初始化`
- `发送短信验证码`
- `短信验证码登录`
