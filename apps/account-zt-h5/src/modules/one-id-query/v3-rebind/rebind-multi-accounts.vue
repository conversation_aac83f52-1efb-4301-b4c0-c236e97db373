<template>
  <div class="one-id-query-container">
      <MultiAccounts
        :multi-accounts="multiAccounts"
        @success="onLoginSuccess"
        @error="onLoginError"
      />
  </div>
</template>

<script lang="ts">
import MultiAccounts from '@/modules/one-id-query/components/login-multi-accounts/LoginMultiAccounts.vue';
import { MULTI_ACCOUNT }  from '@/modules/one-id-query/components/login-multi-accounts/types';
import { defineComponent, ref, getCurrentInstance } from '@vue/composition-api';
import { Toast } from '@ks/sharp-ui';

export default defineComponent({
  components: {
    MultiAccounts
  },

  setup() {
    const multiAccounts = ref<MULTI_ACCOUNT>({
      userInfos: [],
      multiUserToken: '',
      phone: '',
      countryCode: '',
    });

    try {
      multiAccounts.value = JSON.parse(sessionStorage.multiAccountsInfo);
    } catch (e) {
      console.log(e);
    }

    const vm = getCurrentInstance();
    const onLoginSuccess = (multiAccounts: any) => {
      (vm?.proxy as any).$router.push({
        path: '/one-id-query/v3/rebind/new-phone',
        query: { phone: multiAccounts.phone }
      });
    };

    const onLoginError = (err: any) => {
      console.log('onLoginError', err);
      Toast.error('选择账号失败，请检查网络，稍后再试！');
    };

    return {
      multiAccounts,
      onLoginSuccess,
      onLoginError,
    };
  }
});
</script>

<style scoped lang="less"></style>
