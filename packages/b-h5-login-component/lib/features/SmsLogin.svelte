<script lang="ts">
    import Input from "$lib/shared/forms/Input.svelte";
    import IconClear from "$lib/shared/icons/IconClear.svelte";
    import IconSwitch from "$lib/shared/icons/IconSwitch.svelte";
    import IconKuai<PERSON>ogo from "$lib/shared/icons/IconKuaiLogo.svelte";
    import Agreements from "$lib/features/Agreements.svelte";
    import Button from "$lib/shared/button/index.svelte";
    import ChooseUser from "$lib/features/components/ChooseUser.svelte";
    import { appendParams } from "$lib/utils/url";

    import {
        requestCodeByAccountV2 as requestMobileCodeB,
        smsCodeLoginV2 as smsLoginB,
    } from '@ks/general-sso';
    import {
        login as smsLoginC,
        requestMobileCode as requestMobileCodeC,
    } from '@ks/sso';

    import { createSmsCodeStore } from "$lib/store/smsStore";
    import { getContext, createEventDispatcher } from "svelte";
    import { CODE_MULTICHOOSE, CODE_CANCEL_CAPTCHA, EnumErrorType, EnumLoginType } from "$lib/shared/const";
    import { useToast } from "$lib/shared/toast/toastStore";
    import BottomAlert from "$lib/features/components/BottomAlert.svelte";
    import { loginMultiUserInfo } from "$lib/store/multiUserInfo";


    const toast = useToast();
    const store = createSmsCodeStore();
    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    const onLoginStart = getContext('onLoginStart');
    const configStore = getContext('loginPageConfig');
    const env = getContext('env');

    const currentMainState = $configStore.currentMainState;

    const dispatch = createEventDispatcher();
    let loginErrorMsg = '';

    let sendCodeClicked = false;
    async function sendCodeAction() {
        if (!store.reducers.validateSend()) {
            toast.show({
                message: $store.loginErrorMsg,
            });
            return;
        }
        if ($store.countDown > 0) {
            return;
        }
        try {
            currentMainState === 1 ?
                await requestMobileCodeC({
                    account: $store.phone,
                    isLogin: true,
                    type: 53,
                }) :
                await requestMobileCodeB({
                    account: $store.phone!,
                    countryCode: $store.countryCode,
                    type: 53,
                });
            sendCodeClicked = true;
            toast.show({
                message: '已发送验证码',
            });
            store.setValues({
                countDown: 60,
            });
            const interal = setInterval(() => {
                if ($store.countDown <= 0) {
                    clearInterval(interal);
                    return;
                }
                store.setValues({
                    countDown: $store.countDown - 1,
                });
            }, 1000);
        } catch (error) {
            console.log('error', error);
            loginErrorMsg = error.error_msg || '未知错误，请稍后重试';
            toast.show({
                message: loginErrorMsg,
            });
            onFail?.({
                loginType: EnumErrorType.SendCodeError,
                error,
            });
        }
    }

    let codeClass = '';
    $: {
        if ($store.phone?.length >= 8) {
            codeClass = 'activate';
        } else {
            codeClass = '';
        }
    }


    let buttondisabled = false;

    async function loginAction() {
        const loginType = currentMainState === 1 ? EnumLoginType.smsCodeB : EnumLoginType.smsCodeC;

        onLoginStart({
            name: loginType,
        });
        try {
            buttondisabled = true;
            const res = currentMainState === 1 ? await smsLoginC({
                account: $store.phone,
                smsCode: $store.smsCode,
            }) : await smsLoginB({
                account: $store.phone,
                smsCode: $store.smsCode,
                countryCode: $store.countryCode,
            });
            buttondisabled = false;
            onSuccess({
                loginType,
                result: res,
            });
        } catch (error) {
            buttondisabled = false;
            console.log('error', error);
            if (error?.result === CODE_MULTICHOOSE) {
                // 存在两个账号的情况，需要选择
                loginMultiUserInfo.set({
                    userInfos: error.userInfos,
                    multiUserToken: error.multiUserToken,
                    phone: $store.phone,
                    loginType: 'password',
                });
                store.setValues({
                    chooseUserOpen: true,
                });
                return;
            } else if (error?.result === CODE_CANCEL_CAPTCHA) {
                onFail({
                    loginType: loginType,
                    error,
                });
                return;
            }
            loginErrorMsg = error.error_msg || '未知错误，请稍后重试';
            toast.show({
                message: loginErrorMsg,
            });
            onFail({
                loginType,
                error,
            })
        }
    }

    async function onLogin() {
        if (!store.reducers.validateLogin()) {
            toast.show({
                message: $store.loginErrorMsg,
            });
            return;
        }
        if ($store.agreementChecked) {
            await loginAction();
        } else {
            store.setValues({
                bottomAlertOpen: true,
            });
        }
    }

    async function onAcceptLogin() {
        store.reducers.onBottomAccept();
        await loginAction();
    }

    function goToPasswordLogin() {
        dispatch('updateLoginMethod', 'password');
    }
    let buttonForbidden = false;
    store.subscribe((value) => {
        buttonForbidden = store.reducers.isButtonForbidden();
    });

    function closeChooseUser() {
        store.setValues({
            chooseUserOpen: false,
        });
    }

    const appIdMap = {
        staging: {
            appId: 'ks694117295173644432',
            baseUrl: 'https://open-platform.staging.kuaishou.com/fe/authorization/h5',
        },
        production: {
            appId: 'ks667377170011306237',
            baseUrl: 'https://open.kuaishou.com/fe/authorization/h5',
        }
    };

    function jumpToCLogin() {
        // console.log('jumpToCLogin');
        // 拼接 sid 和 redirect_url，跳转到C端页面
        const url = appendParams(appIdMap[env].baseUrl, {
            app_id: appIdMap[env].appId,
            scope: 'user_info',
            response_type: 'code',
            redirect_uri: appendParams(location.href, {
                loginType: 'KSAuthorize'
            }),
            auth_mode: '1,2,3',
        });
        location.href = url;
    }
</script>

<div>
    <div class="login-body">
        <Input
                placeholder="请输入手机号"
                type="tel"
                bind:value={$store.phone}
                onClear={() => {
                    store.setValues({
                        phone: ''
                    });
                }}
        >
        </Input>
        <Input
                placeholder="请输入验证码"
                bind:value={$store.smsCode}
                maxlength={6}
                type="text"
                inputmode="numeric"
                onClear={() => {
                    store.setValues({smsCode: ''});
                }}
        >
            <slot slot="right">
                <div class="sms-slot-right">
                    <div class="send-code"
                         on:click={sendCodeAction}
                    >
                        {#if $store.countDown}
                            <span class="text-[#C6C6C6ff]">{$store.countDown}s</span>
                        {:else}
                        <span class={codeClass}>
                            {#if sendCodeClicked}
                                重新发送
                            {:else}
                                获取验证码
                            {/if}
                        </span>
                        {/if}
                    </div>
                </div>
            </slot>
        </Input>
        <Agreements
                bind:checked={$store.agreementChecked}
        />
        <Button
                on:click={onLogin}
                disabled={buttondisabled}
                forbidden={buttonForbidden}
        >
            登录
        </Button>

        <div class="vice-operate">
            <div style="display: flex">
                <div class="switch-button"
                     on:click={goToPasswordLogin}
                >
                    <IconSwitch />
                    密码登录
                </div>
            </div>
            {#if [2, 3].includes(currentMainState)}
                <div on:click={jumpToCLogin}
                >
                    <IconKuaiLogo />
                </div>
            {/if}
        </div>
        <BottomAlert
                isOpen={$store.bottomAlertOpen}
                onAccept={onAcceptLogin}
                onReject={store.reducers.onBottomReject}
        />
        <ChooseUser
                isOpen={$store.chooseUserOpen}
                onClose={closeChooseUser}
        />
    </div>
</div>


<style>

    .title {
        color: #000000;
        font-size: 24px;
        font-weight: 500;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 28px;
        text-align: center;
        vertical-align: middle;
        margin-top: 64px;
    }
    .login-body {
        padding-left: 35px;
        padding-right: 35px;
        margin-top: 40px;
    }

    .common-login-btn {
        background: #326BFB;
        border-radius: 28px;
        opacity: 0.5;

        color: #FFFFFF;
        font-size: 16px;
        font-weight: 500;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 24px;
        text-align: center;
        vertical-align: top;

        padding: 16px 32px;
    }
    .switch-button {
        box-sizing: border-box;
        border-style: solid;
        border-width: 1px;
        border-color: #EAEAEA;
        border-radius: 100px;
        padding: 10px 16px;
        display: flex;
        gap: 8px;
        align-items: center;

        color: #222222;
        font-size: 14px;
        font-weight: 500;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 21px;
        text-align: center;
        vertical-align: top;
    }
    .send-code {
        white-space: nowrap;
        color: #C6C6C6ff;
        font-size: 16px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 22px;
        text-align: right;
        vertical-align: middle;
    }
    .activate {
        color: #326BFBff;
    }
    .vice-operate {
        margin-top: 24px;
        display: flex;
        gap: 10px;
        justify-content: center;
        align-items: center;
    }
    .sms-slot-right {
        /*margin-left: 12px;*/
        /*display: flex;*/
        /*white-space: nowrap;*/
        /*gap: 12px;*/
        /*align-items: center;*/
    }
</style>
