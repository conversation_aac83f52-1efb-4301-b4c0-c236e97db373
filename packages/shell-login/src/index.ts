// import QRCode from 'qrcode';
import qrcode from 'qrcode-terminal';
import { PREFIX, QRSurfix, UserSurfix, ResultSurfix, LoginTokenPath } from "./config"
import { AcceptResult, AuthTokenResponse, BaseUserInfo, QRCodeInfo, QRLoginInfo } from '@ks/sso';
import { ENV, LoginTokenResult } from './type';
import got, {CancelableRequest} from 'got'

export class ShellLogin {
    private sid: string
    private baseUrl: string;
    private qrLoginInfo: QRCodeInfo | undefined = undefined;
    private expireTime: number | undefined
    private pendingRequest: Record<string, {
        gotPromise: CancelableRequest,
        reject: (reason?: any) => void;
    }> = {}
    constructor(sid: string, env: ENV) {
        this.sid = sid;
        this.baseUrl = PREFIX[env]
    }

    private async getQRLoginInfo() {
        const url = this.baseUrl + QRSurfix;

        return got.post(url, {
            form: {
                sid: this.sid
            }
        })
        .json<QRLoginInfo & { qrUrl:string, expireTime:number }>()
    }

    async printQRCode(options:{small: boolean} = { small: true }): Promise<boolean> {
        const {
            qrUrl,
            qrLoginSignature,
            qrLoginToken,
            expireTime
        } = await this.getQRLoginInfo();

        this.expireTime = expireTime

        this.qrLoginInfo = {
            qrLoginSignature,
            qrLoginToken
        }
        const isSmall = process.platform === "win32" ? false : options.small

        qrcode.generate(qrUrl, { small: isSmall }, function (qrcode) {
            //print qrcode
            console.log("\n");
            console.log(qrcode)
        });

          return true
    }

    async getUserLoginInfo() {
        const targetUrl = this.baseUrl + UserSurfix;
        const qrLoginInfo = this.qrLoginInfo;
        if (!qrLoginInfo) {
            throw new Error("No QR info! Please call printQRCode first.")
        }

        const timeout = this.expireTime ? this.expireTime - Date.now() : 5000;

        return got.post(targetUrl, {
            form: this.qrLoginInfo,
            timeout
        }).json<{ user: BaseUserInfo }>().then(res => res.user)
    }


    async scanQRLoginResult(): Promise<LoginTokenResult> {
        const targetUrl = this.baseUrl + ResultSurfix;
        const qrLoginInfo = this.qrLoginInfo;

        if (!qrLoginInfo) {
            throw new Error("No QR info! Please call printQRCode first.")
        }

        const timeout = this.expireTime ? this.expireTime - Date.now() : 5000;

        return new Promise(async (resolve, reject) => {
            let gotPromise =  got.post(targetUrl, {
                form: { ...qrLoginInfo, sid: this.sid },
                timeout
            })

            this.pendingRequest[targetUrl] = {
                gotPromise,
                reject
            }

            let data = await gotPromise.json<AcceptResult>()

            resolve(this.getLoginToken(data.qrToken))
        })
    }

    async cancelQrLogin() {
        const url = this.baseUrl + ResultSurfix;
        const { reject, gotPromise } = this.pendingRequest[url];
        gotPromise.cancel("From Cancel Request")
        reject('From Cancel Request');
    }

    async getLoginToken(qrToken: string): Promise<LoginTokenResult> {
        const currentSid = this.sid;
        const url = this.baseUrl + LoginTokenPath;
        // const params = getQueryString({ qrToken, sid: this.sid })
        // 如果有业务账号域名的情况取token都在业务账号域名操作
        return got.post(url, {
            form: { qrToken, sid: this.sid },
        }).json<AuthTokenResponse>()
            .then(({
                [currentSid + '.at']: authToken,
                sid = currentSid,
                [currentSid + '_st']: serviceToken,
                userId,
                ssecurity,
                stsUrl,
                followUrl,
                isNewUser = false,
            }) => {
                return {
                    [currentSid + '.at']: authToken,
                    authToken,
                    sid,
                    ssecurity,
                    [currentSid + '_st']: serviceToken,
                    serviceToken,
                    userId,
                    stsUrl,
                    followUrl,
                    isNewUser,
                    loginType: "shell"
                };
            })
    }

}