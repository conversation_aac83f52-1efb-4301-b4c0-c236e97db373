<script lang="ts">
    import { twMerge } from 'tailwind-merge';
    import ToolbarButton from '$lib/shared/toolbar/ToolbarButton.svelte';
    import { getContext } from "svelte";
    import Tooltip from "$lib/shared/tooltip/Tooltip.svelte";

    const themeColor = getContext('themeColor');
    const configStore = getContext('loginPageConfig');

    let defaultColor = '#C6C6C6';
    export let customColor = defaultColor;
    export let name: string = 'IconPc';
    export let toolTip = '';

    function onHover() {
        // console.log('$p', $$props.class);
        if ($$props.class.includes('currentB')) {
            customColor = themeColor?.colorB || defaultColor;
        } else {
            customColor = themeColor?.colorC || defaultColor;
        }
    }
    function onOut() {
        customColor = defaultColor;
    }
</script>

<ToolbarButton on:click {name} {...$$restProps} class={twMerge('ms-auto', $$props.class)} let:svgSize
>
    {#if $configStore?.iconStyle === 1}
        <svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg"
             on:mouseover={onHover}
             on:mouseout={onOut}
        >
            <mask id="mask0_2261_45722" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="72" height="72">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H60C66.6274 0 72 5.37258 72 12V72L0 0Z" fill="white"/>
            </mask>
            <g mask="url(#mask0_2261_45722)">
                <path d="M56.3781 47.7722H58V54.3863H40.772V37.1582H47.386V44.7722C47.386 46.4291 48.7292 47.7722 50.386 47.7722H56.3781Z" stroke="#FFAA8A" stroke-width="4"/>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M35.8421 32.8421C36.3944 32.8421 36.8421 32.3944 36.8421 31.8421V13C36.8421 12.4477 36.3944 12 35.8421 12H17C16.4477 12 16 12.4477 16 13V31.8421C16 32.3944 16.4477 32.8421 17 32.8421H35.8421ZM19.0875 16.0879C19.0875 15.5356 19.5352 15.0879 20.0875 15.0879H32.7542C33.3064 15.0879 33.7542 15.5356 33.7542 16.0879V28.7546C33.7542 29.3068 33.3064 29.7546 32.7542 29.7546H20.0875C19.5352 29.7546 19.0875 29.3068 19.0875 28.7546V16.0879Z" fill="#FFAA8A"/>
                <path d="M24.175 24.667V20.1758H28.6663V24.667H24.175Z" fill="#D8D8D8" stroke="#FFAA8A" stroke-width="4"/>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M58.9996 12C59.5519 12 59.9996 12.4477 59.9996 13V31.8421C59.9996 32.3944 59.5519 32.8421 58.9996 32.8421H40.1575C39.6052 32.8421 39.1575 32.3944 39.1575 31.8421V13C39.1575 12.4477 39.6052 12 40.1575 12H58.9996ZM45.3325 19.1754C45.3325 18.6231 45.7802 18.1754 46.3325 18.1754H52.8237C53.376 18.1754 53.8237 18.6231 53.8237 19.1754V25.6666C53.8237 26.2189 53.376 26.6666 52.8237 26.6666H46.3325C45.7802 26.6666 45.3325 26.2189 45.3325 25.6666V19.1754ZM42.245 16.0879C42.245 15.5356 42.6927 15.0879 43.245 15.0879H55.9116C56.4639 15.0879 56.9116 15.5356 56.9116 16.0879V28.7546C56.9116 29.3068 56.4639 29.7546 55.9116 29.7546H43.245C42.6927 29.7546 42.245 29.3068 42.245 28.7546V16.0879Z" fill="#EAEAEA"/>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M31.3889 12C30.2843 12 29.3889 12.8954 29.3889 14L29.389 39.3189C29.389 40.4235 30.2844 41.3189 31.389 41.3189H43.2611V44.1113C43.2611 44.1119 43.2611 44.1126 43.2611 44.1133H36.4941C35.7466 44.1133 35.1406 44.7596 35.1406 45.5568C35.1406 46.354 35.7466 47.0003 36.4941 47.0003H55.4417C56.1892 47.0003 56.7951 46.354 56.7951 45.5568C56.7951 44.7596 56.1892 44.1133 55.4417 44.1133H48.6747C48.6747 44.1126 48.6747 44.1119 48.6747 44.1113V41.3189H60.1667C61.2713 41.3189 62.1667 40.4235 62.1667 39.3189V14C62.1667 12.8954 61.2713 12 60.1667 12L31.3889 12ZM59.7085 36C59.7085 37.1046 58.813 38 57.7085 38H33.8473C32.7428 38 31.8473 37.1046 31.8473 36V17C31.8473 15.8954 32.7428 15 33.8473 15H57.7084C58.813 15 59.7085 15.8954 59.7085 17V36ZM38.7639 20C37.6594 20 36.7639 20.8954 36.7639 22L36.7639 32C36.7639 33.1046 37.6593 34 38.7639 34H52.7917C53.8963 34 54.7917 33.1046 54.7917 32V22C54.7917 20.8954 53.8963 20 52.7917 20H38.7639Z" fill={customColor}/>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H70C71.1046 0 72 0.895431 72 2V72L0 0Z" fill="white"/>
            </g>
            <mask id="mask1_2261_45722" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="72" height="72">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H60C66.6274 0 72 5.37258 72 12V72L0 0Z" fill="white"/>
            </mask>
            <g mask="url(#mask1_2261_45722)">
                <rect x="20" y="14" width="38" height="26" rx="3" stroke={customColor} stroke-width="2"/>
                <rect x="25" y="45" width="28" height="3" rx="1.5" fill={customColor}/>
                <rect x="24" y="34" width="30" height="2" rx="1" fill={customColor}/>
                <rect x="37" y="42" width="4" height="3" fill={customColor} stroke={customColor} stroke-width="2"/>
            </g>
        </svg>

    {:else}
        <svg width="59" height="72" viewBox="0 0 59 72" fill="none" xmlns="http://www.w3.org/2000/svg"
             on:mouseover={onHover}
             on:mouseout={onOut}
        >
            <mask id="mask0_2261_45694" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="-13" y="0" width="72" height="72">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M-13 0H47C53.6274 0 59 5.37258 59 12V72L-13 0Z" fill="white"/>
            </mask>
            <g mask="url(#mask0_2261_45694)">
                <mask id="path-2-inside-1_2261_45694" fill="white">
                    <rect x="6" y="13" width="40" height="28" rx="1"/>
                </mask>
                <rect x="6" y="13" width="40" height="28" rx="1" stroke={customColor} stroke-width="4" mask="url(#path-2-inside-1_2261_45694)"/>
                <rect x="12" y="45" width="28" height="3" rx="1.5" fill={customColor}/>
                <rect x="11" y="34" width="30" height="2" rx="1" fill={customColor}/>
                <rect x="24" y="42" width="4" height="3" fill={customColor} stroke={customColor} stroke-width="2"/>
            </g>
        </svg>
    {/if}

    {#if toolTip}
        <Tooltip placement="left">{toolTip}</Tooltip>
    {/if}
</ToolbarButton>

<style>
    path.currentC {
        fill: var(--colorC);
    }
    path.currentB {
        fill: var(--colorB);
    }
    svg:focus {
        outline: none;
    }
</style>
