<style scoped lang="less" src="./verify.less" />
<script lang="ts" src="./verify.ts" />

<template>
    <div class="one-id-query-container">
        <div style="margin-top: 0.5625em; margin-left: 1em; margin-bottom: -0.1875em;">
            <span class="gray-text">请输入以下信息验证身份</span>
        </div>

        <div class="white-panel">
            <div style="margin-top: 0.25em;">
                <span class="black-text">上传手机号入网信息</span>
            </div>
            <div class="upload-component" :style="{backgroundImage: `url(${image.url})`}" @click="selectFile">
                <input
                    ref="selectFileInput"
                    type="file"
                    accept="image/*"
                    @input="onFileInput"
                    style="position: absolute; top: 0; left: 0; visibility: hidden; z-index: -1; opacity: 0;"
                />
                <template v-if="image.url">
                    <div class="clear-icon" @click.stop="image.url = ''; image.file = null;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="17" height="16" viewBox="0 0 17 16" fill="none">
                            <mask
                                id="mask0_122_274"
                                style="mask-type:alpha"
                                maskUnits="userSpaceOnUse"
                                x="0"
                                y="0"
                                width="17"
                                height="16"
                            >
                                <path d="M0.110962 0H16.111V16H0.110962V0Z" fill="white" />
                            </mask>
                            <g mask="url(#mask0_122_274)">
                                <path
                                    d="M4.38653 3.20122L12.8718 11.6865L11.929 12.6293L3.44373 4.14403L4.38653 3.20122Z"
                                    fill="white"
                                />
                                <path
                                    d="M11.8013 3.20122L3.31605 11.6865L4.25886 12.6293L12.7441 4.14403L11.8013 3.20122Z"
                                    fill="white"
                                />
                            </g>
                        </svg>
                    </div>
                </template>
                <template v-else>
                    <div class="upload-tip">
                        <svg
                            class="icon"
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="25"
                            viewBox="0 0 24 25"
                            fill="none"
                        >
                            <g clip-path="url(#clip0_122_486)">
                                <path
                                    fill-rule="evenodd"
                                    clip-rule="evenodd"
                                    d="M13 4.5V11.5H20V13.5H12.9995L13 20.5H11L10.9995 13.5H4V11.5H11V4.5H13Z"
                                    fill="#C6C6C6"
                                />
                            </g>
                            <defs>
                                <clipPath id="clip0_122_486">
                                    <rect width="24" height="24" fill="white" transform="translate(0 0.5)" />
                                </clipPath>
                            </defs>
                        </svg>
                        <div class="gray-text upload-text">上传图片</div>
                    </div>
                </template>
            </div>
            <div style="margin-bottom: 0.2192em;">
                <span class="gray-text">入网信息请联系运营商客服</span>
            </div>
        </div>

        <div class="white-panel">
            <div style="margin-bottom: 0.1875em; margin-top: 0.25em;">
                <span class="black-text">手机号码</span>
            </div>
            <div class="input-component phoneNumber">
                <div class="area-code">
                    <span> {{ phoneInfo.countryCode }}</span>
                </div>
                <input style="" v-model="phoneInfo.phone" placeholder="请输入手机号码" maxlength="11" />
                <div class="clear-icon">
                    <img
                        v-show="phoneInfo.phone"
                        @click.stop="phoneInfo.phone = ''"
                        class="close-icon"
                        src="@/assets/delete.png"
                    />
                </div>
            </div>
            <div style="margin-top: 0.875em; margin-bottom: 0.1875em;">
                <span class="black-text">短信验证码</span>
            </div>
            <div class="input-component captcha" style="margin-bottom: 0.625em;">
                <input v-model="phoneInfo.smsCode" placeholder="请输入验证码" maxlength="6" />
                <div class="captcha-btn">
                    <div style="position: relative;">
                        <template v-if="counting">
                            <span style="color: #9C9C9C; margin-right: 0.76923em;">{{`重新发送(${countDown})`}}</span>
                        </template>
                        <template v-else>
                            <span @click="sendSmsCode" style="margin-right: 0.461538em;">获取验证码</span>
                        </template>
                        <div class="clear-icon">
                            <img
                                v-show="phoneInfo.smsCode"
                                @click.stop="phoneInfo.smsCode = ''"
                                class="close-icon"
                                src="@/assets/delete.png"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 5em;">
            <div
                @click="onFaceBtnClick"
                class="red-btn"
                :style="{opacity: (phoneInfo.smsCode.length + phoneInfo.phone.length === 17 && image.file) ? 1 : 0.5}"
            >
                进入人脸验证
            </div>
        </div>
    </div>
</template>
