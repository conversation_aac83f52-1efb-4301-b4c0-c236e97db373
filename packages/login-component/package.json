{"name": "@ks/login-component", "version": "3.1.1-beta.3", "module": "./dist/index.esm.js", "main": "./dist/index.js", "files": ["dist", "README.md", "CHANGELOG.md"], "typings": "./dist/index.d.ts", "repository": "https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "maintainers": ["liuz<PERSON><EMAIL>"], "devDependencies": {"@babel/core": "^7.9.0", "@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@ks/eslint-config-game": "^1.1.9-alpha.3", "eslint": "^6.3.0", "eslint-plugin-svelte3": "^2.7.3", "husky": "^4.2.3", "npm-run-all": "^4.1.5", "rimraf": "^2.6.2", "rollup": "^1.31.1", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-livereload": "^1.0.4", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-svelte": "^6.1.1", "rollup-plugin-terser": "^5.2.0", "rollup-plugin-typescript2": "^0.26.0", "sirv-cli": "^0.4.4", "standard-version": "^7.1.0", "stylus": "^0.54.8", "svelte": "^3.19.1", "svelte-preprocess": "^3.0.2"}, "dependencies": {"@ks/sso": "workspace:*", "@ks/weblogger": "^3.9.46", "carbon-components": "^10.51.0", "ks-log": "^2.0.0-rc8"}, "scripts": {"dev": "run-p start:dev autobuild", "build": "rimraf dist && rollup -c", "publishBeta": "npm run build && pnpm publish --tag beta", "autobuild": "rollup -c -w", "lint": "echo 'lint skipped.'", "start": "sirv dist --single", "start:dev": "sirv dist --single --dev", "publishToNpm": "npm run build && if [[ -d ./dist ]]; then pnpm publish; else echo '请先编译出产物，再发包。'; fi"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "peerDependencies": {"@ks/weblogger": "^3.8.18"}}