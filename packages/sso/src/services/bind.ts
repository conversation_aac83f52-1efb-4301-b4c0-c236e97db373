import { request } from '@/core/request';
import { DEFAULT_COUNTRY_CODE } from '@/config/constants'
import { privateGetConfig } from '@/core/configInit';
import {  BaseRes } from '../types/services';

export interface bindPhoneOptions {
    phone: string;
    countryCode: string;
    newSmsCode: string | number;
    originalSmsCode?: string | number;
    originalEmailCode?: string | number;
}

export interface bindEmailOptions {
    email: string;
    newEmailCode: string | number;
    originalSmsCode?: string | number;
    originalEmailCode?: string | number;
}

export function bindPhone({phone, countryCode = DEFAULT_COUNTRY_CODE, newSmsCode, originalSmsCode, originalEmailCode}: bindPhoneOptions) {
    const config = privateGetConfig();
    return request<BaseRes>(config.baseUrl + '/pass/kuaishou/bind/phone', {
        sid: config.sid,
        phone,
        countryCode,
        newSmsCode,
        originalSmsCode,
        originalEmailCode,
    });
}

export function bindEmail({email, newEmailCode, originalSmsCode, originalEmailCode}: bindEmailOptions) {
    const config = privateGetConfig();
    return request<BaseRes>(config.baseUrl + '/pass/kuaishou/bind/email', {
        sid: config.sid,
        email,
        newEmailCode,
        originalSmsCode,
        originalEmailCode,
    });
}
