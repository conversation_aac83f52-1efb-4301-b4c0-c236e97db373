<script lang="ts">
import Alert from "$lib/shared/alert/ErrorAlert.svelte";
import { twMerge } from 'tailwind-merge';
import { getContext } from 'svelte';

const loginPageConfig = getContext('loginPageConfig');


export let loginErrorMsg: string | undefined;
</script>

<div class={twMerge("w-full global-error-msg-wrapper bg-[#FFF2ECff]", $$props.class, $loginPageConfig?.errorMsgClass)}>
    {#if loginErrorMsg}
        <Alert>
            <div style="margin-left: 8px"
                 class="font-[#1F1F1F] text"
            >
                {loginErrorMsg.slice(0, 25)}
            </div>
        </Alert>
    {/if}
</div>

<style>
    .global-error-msg-wrapper {
        border-radius: var(--buttonRadius);
    }
    .text {
        font-size: 12px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 22px;
        text-align: left;
        vertical-align: top;
    }
</style>
