import { pageReq } from '@/core/request';
import { createDevLog } from '@/utils/logDev';
import { privateGetConfig } from '@/core/configInit';
import { BizAccountParam } from '../types/services';
import { getLoginToken } from '../core/loginToken';

const devLog = createDevLog('sso:');

// 此接口为支持业务账号中心域名case使用
// 当业务账号中心域名拉不到登录态时，从快手中心账号域名拉取token
export function _pullLoginToken(param?: BizAccountParam) {
    const config = privateGetConfig();
    return pageReq(config.baseUrl + '/pass/kuaishou/login/pull', {
        sid: param?.sid ?? config.sid,
        followUrl: param?.followUrl || config.followUrl,
        loginUrl: param?.loginUrl || config.loginUrl,
    });
}

// @Deprecated
// 如果为string则为sid 如果为object则为
export function passToken(param?: string | BizAccountParam) {
    const config = privateGetConfig();
    devLog('Warning: `passToken` will be deprecated, please use `passTokenStsInIframe` instead');
    const sid = typeof param === 'string' ? param : param?.sid;
    return getLoginToken('/pass/kuaishou/login/passToken', {
        sid: sid ?? config.sid,
    })
    .catch((err) => {
        // 在没有登录的case下，并且使用了业务账号中心域名，取中心账号域名拉取passToken
        if (err && err.result === ********* && config.bizIdHost) {
            if (typeof param === 'object' && param.pullTokenFromCenter !== false || typeof param === 'undefined') {
                return _pullLoginToken(param);
            }
        }
        return err;
    });
}

