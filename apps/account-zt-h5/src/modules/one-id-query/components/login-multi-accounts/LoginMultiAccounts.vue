<template>
    <div class="multi-accounts">
        <div class="choose-account">
            <div class="tips">轻触头像选择账号</div>
            <div class="separator"></div>
            <div class="accounts">
                <div
                    class="account"
                    @click="onChooseLoginAccount(item)"
                    v-for="item in multiAccounts.userInfos"
                    :key="item.userId"
                >
                    <div class="avator" :style="{'background-image': `url(${item.headUrl})`}"></div>
                    <div class="username">
                        {{ item.name }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from '@vue/composition-api';
import { MULTI_ACCOUNT  } from './types';
import { chooseUser } from '@ks/sso';
import { setRootDomainToken } from '@/modules/one-id-query/common/utils';

export default defineComponent({
    props: {
        multiAccounts: {
            type: Object as PropType<MULTI_ACCOUNT>,
            required: true,
        }
    },
    setup({ multiAccounts }, { emit }) {
        const onChooseLoginAccount = (data: any) => {
            chooseUser({
                multiUserToken: multiAccounts.multiUserToken,
                userId: data.userId,
                phone: multiAccounts.phone,
                countryCode: multiAccounts.countryCode,
            }).then(res => {
                return setRootDomainToken(res.authToken, res.sid).then(res => {
                    emit('success', multiAccounts);
                });
            }).catch(err => {
                console.log('onChooseLoginAccount Error', err);
                emit('error', err);
            });
        }

        return {
            onBack: () => emit('back'),
            onChooseLoginAccount
        };
    },
});
</script>

<style lang="less" scoped>
@import "~@/assets/css/mixins.less";
.multi-accounts {
    .choose-account {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 0.48rem;
        .tips {
            font-size: 0.48rem;
            line-height: 0.68rem;
        }
        .separator {
            width: 2.4rem;
            height: 1px;
            margin-top: 1.28rem;
            background: #E6E6E6;
        }
        .accounts {
            display: flex;
            justify-content: center;
            margin-top: 1.28rem;
            .account {
                display: flex;
                flex-direction: column;
                align-items: center;
                width: 1.92rem;
                text-align: center;
                &:not(:first-of-type) {
                    margin-left: 1rem;
                }
                .avator {
                    width: 1.44rem;
                    height: 1.44rem;
                    border-radius: 50%;
                    background: center /100% 100% no-repeat;
                }
                .username {
                    width: 100%;
                    margin-top: 0.16rem;
                    font-size: 0.32rem;
                    line-height: 0.48rem;
                    .one-line-ellipsis();
                }
            }
        }
    }
}
</style>
