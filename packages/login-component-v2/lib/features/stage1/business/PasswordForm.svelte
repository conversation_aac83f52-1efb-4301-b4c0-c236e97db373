<script lang="ts">
    import { getContext, createEventDispatcher } from 'svelte';


    import Button from '$lib/shared/button/index.svelte';
    import { accountPasswordLoginV2 } from '@ks/general-sso';

    import Input from '$lib/shared/forms/input.svelte';
    import CloseButton from "$lib/utils/CloseButton.svelte";
    import SeePassword from "$lib/utils/SeePassword.svelte";
    import HidePassword from "$lib/utils/HidePassword.svelte";
    import DropDown from "$lib/shared/dropdown/DropDown.svelte";
    import ViceOperate from "$lib/features/components/viceOperate/ViceOperate.svelte";
    import BackChoose from "$lib/features/components/BackChoose.svelte";
    import OtherLogin from "$lib/features/components/OtherLogin.svelte";
    import { EnumLoginType } from "$lib/shared/const";
    import { filterInput } from "$lib/utils/validate";

    import { createPasswordStore } from '$lib/store/passwordStore';
    import RecommendLoginB from "$lib/features/components/RecommendLoginB.svelte";

    const configStore = getContext('loginPageConfig');
    const isMainAccountPage = getContext('isMainAccountPage');

    let passwordLoginTypes;
    let viceOperateConfig;
    let otherOperateConfig;
    let inputClass;
    if (isMainAccountPage) {
        passwordLoginTypes = $configStore.bAccountConfig?.passwordLoginTypes;
        viceOperateConfig = $configStore.bAccountConfig?.viceOperateConfig;
        otherOperateConfig = $configStore.bAccountConfig?.otherOperateConfig;
        inputClass = $configStore.bAccountConfig?.inputClass || '';
    } else {
        passwordLoginTypes = $configStore.bSubAccountConfig?.passwordLoginTypes;
        viceOperateConfig = $configStore.bSubAccountConfig?.viceOperateConfig;
        otherOperateConfig = $configStore.bSubAccountConfig?.otherOperateConfig;
        inputClass = $configStore.bSubAccountConfig?.inputClass || '';
    }

    const store = createPasswordStore(passwordLoginTypes);

    const dispatch = createEventDispatcher();


    let showPassword = false;


    let loginErrorMsg = '';


    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    const onLoginStart = getContext('onLoginStart');


    let buttondisabled = false;
    async function loginSubmit() {
        if (!store.reducers.validateLogin()) {
            return;
        }
        try {
            onLoginStart({
                name: EnumLoginType.passwordB,
            });
            buttondisabled = true;
            const res = await accountPasswordLoginV2({
                account: $store.account,
                password: $store.password,
            });
            buttondisabled = false;
            console.log('res is', res, onSuccess);
            onSuccess({
                loginType: EnumLoginType.passwordB,
                result: res,
            });
        } catch (error) {
            buttondisabled = false;
            console.log('error', error);
            loginErrorMsg = error.error_msg || '网络错误，请稍后重试';
            onFail({
                loginType: EnumLoginType.passwordB,
                error,
            });
        }
    }

    function updatePageState(s) {
        console.log('s is', s);
        dispatch('updatePageState', s.detail);
    }
</script>

<div class="w-full mt-[8px] password-form-wrapper">
    <div class="mb-[24px] relative">
        <Input placeholder={$store.placeholder}
               bind:value={$store.account}
               on:focus={() => {
                   store.setValues({accountErrorMsg: ''})
               }}
               on:input={store.reducers.updateInputValue}
               on:blur={store.reducers.onAccountBlur}
               on:keydown={store.reducers.handleKeydown}
               bind:ref={$store.emailInput}
               errorMsg={$store.accountErrorMsg}
               class={inputClass}
        >
            <slot slot="right">
                {#if $store.account}
                    <CloseButton on:click={store.reducers.clearInputValue}/>
                {/if}
            </slot>
        </Input><DropDown
            class="absolute z-30 w-full currentB"
            visible="{$store.emailDropdownVisible}"
            tooltips="请选择邮箱类型"
            current="{$store.emailHighlightIndex}"
            options="{$store.emailDropdownList}"
            onSelect="{store.reducers.handleEmailSelect}"
    />
    </div>

    <div class="mb-[44px]">
        <Input placeholder="请输入密码"
               class={inputClass}
               bind:value={$store.password}
               on:input={filterInput}
               on:focus={() => {
                   store.setValues({passwordErrorMsg: ''})
               }}
               on:blur={store.reducers.onPasswordBlur}
               type={showPassword ? 'text': 'password'}
               errorMsg={$store.passwordErrorMsg}
               globalErrorMsg={loginErrorMsg}
               maxlength={20}
        >
            <slot slot="right">
                {#if showPassword}
                    <SeePassword on:click={() => (showPassword = !showPassword)}/>
                {:else}
                    <HidePassword on:click={() => (showPassword = !showPassword)}/>
                {/if}
            </slot>
        </Input>
    </div>

    <Button class="currentB"
            on:click={loginSubmit}
            disabled={buttondisabled}
    >
        快手商家账号登录
    </Button>
    <ViceOperate
            currentType="passwordForm"
            on:updatePageState={updatePageState}
            viceOperateConfig={viceOperateConfig}
    />
    <BackChoose {otherOperateConfig} />
    <OtherLogin />
    <RecommendLoginB />
</div>

<style>
</style>
