<template>
    <div class="account-qrcode">
        <img
            v-if="showCloseIcon"
            class="close-icon"
            src="../../assets/icon-close.svg"
            @click="cancel"
        >
        <slot
            name="prefix"
        >
            <div class="prefix-tips">
                快手APP，扫码验证
            </div>
        </slot>

        <div class="account-qrcode-main">
            <div class="corner tl"></div>
            <div class="corner tr"></div>
            <div class="corner bl"></div>
            <div class="corner br"></div>
            <img
                v-if="imageUrl"
                class="account-qrcode-img"
                :src="imageUrl"
            >
            <img
                v-if="step === 'scanResult'"
                class="logo-icon"
                :src="logoURL"
            >
            <div
                v-else
                class="over-layer"
            >
                <template v-if="step === 'timeout'">
                    <div
                        class="icon icon-timeout"
                    ></div>

                    <div class="tips">
                        二维码已失效
                    </div>

                    <div
                        class="main-text text-strong"
                        @click="retry"
                    >
                        点击刷新
                    </div>
                </template>
                <template v-if="step === 'acceptResult'">
                    <div
                        class="icon icon-confirm"
                    ></div>
                    <div class="tips">
                        扫码成功
                    </div>
                    <div class="tips main-text">
                        请在手机上实名认证
                    </div>
                </template>
                <template v-if="step === 'succ'">
                    <div
                        class="icon icon-confirm"
                    ></div>
                    <div class="tips">
                        授权成功
                    </div>
                </template>
            </div>
        </div>

        <slot
            name="suffix"
        >
            <div class="suffix-tips">
                <div>
                    打开 <span class="text-strong">快手App</span> 扫一扫，开始验证
                </div>
                <div>
                    「首页左上角」-「更多」-「扫一扫」
                </div>
            </div>
        </slot>
    </div>
</template>

<script lang="ts">
import { ref, defineComponent } from '@vue/composition-api';
import { startIdentityQrcodeVerify, envType, QrcodeVerifyResult } from '@ks-passport/qrcode-core';

export default defineComponent({
    name: 'account-qrcode',
    props: {
        env: {
            type: String,
            default: 'production',
        },
        kpn: {
            type: String,
            default: 'KUAISHOU',
        },
        sid: {
            type: String,
            default: 'kuaishou.api',
        },
        type: {
            type: String,
            required: true,
        },
        url: {
            type: String,
            required: true,
        },
        logoURL: {
            type: String,
            default: 'https://var----h5----var.kskwai.com/udata/pkg/cloudcdn/i/logo-simple.f8bc2fc.svg',
        },
        showCloseIcon: {
            type: Boolean,
            default: false,
        },
    },
    setup(props, vm) {
        const {
            env,
            kpn,
            sid,
            type,
            url,
        } = props;
        /**
         * 二维码地址
         */
        const imageUrl = ref('');
        const step = ref('init');
        const identityQrcodeVerify = startIdentityQrcodeVerify({
            env: env as envType,
            kpn,
            sid,
            type,
            url,
        });
        identityQrcodeVerify.on('change-qrcode-image', (url: string) => {
            console.log('imageUrl', url);
            imageUrl.value = url;
        });
        identityQrcodeVerify.on('change-step', (nextStep: string, _currentStep: string) => {
            console.log('change-step', nextStep);
            step.value = nextStep;
        });
        identityQrcodeVerify.on('success', (res: QrcodeVerifyResult) => {
            console.log(res);
            setTimeout(() => {
                const { serviceOwnToken: token, serviceOwnType } = res;
                const data = {
                    result: 1,
                    token,
                    type: serviceOwnType,
                };
                vm.emit('sendMsg', data);
            }, 1000);
        });
        identityQrcodeVerify.start();
        function retry() {
            identityQrcodeVerify.start();
        }

        function cancel() {
            vm.emit('sendMsg', { result: -999 });
        }
        return {
            identityQrcodeVerify: identityQrcodeVerify,
            imageUrl,
            step,
            cancel,
            retry,
        };
    },
});
</script>

<style lang="stylus" scoped>
primary-color = #FE3666
size = 180px

.account-qrcode
    position relative
    margin 0 auto
    width 288px
    height 353px
    box-sizing border-box
    border-radius 8px
    padding 24px 40px
    text-align center
    background-color #fff

    &-main
        position relative
        margin 24px 0
        border solid 1px rgba(254,54,102,0.1)
        width size
        height size
        display inline-block
    &-img
        position relative
        width size
        height size
.corner
    content ""
    position absolute
    width 10px
    height 10px
    background primary-color
    &.tl
        top -1px
        left -1px
    &.tr
        top -1px
        right -1px
    &.bl
        bottom -1px
        left -1px
    &.br
        bottom -1px
        right -1px


.prefix-tips
    font-size: 18px;
    font-weight: 500;
    color: #222222;
    line-height: 28px;

.logo-icon
    position absolute
    top 50%
    left 50%
    transform translate(-50%, -50%)
    max-height 32px
    max-width @max-height
.over-layer
    position: absolute
    top: 0
    background: rgba(255,255,255,0.9);
    padding-top: 40px
    width size
    height size - @padding-top

.icon
    display inline-block
    width 40px
    height @width
    background no-repeat center / contain
    &-confirm
        background-image: url("https://var----h5----var.kskwai.com/s1/i/qr-confirm.f32a3c5.svg")
    &-timeout
        background-image: url("https://var----h5----var.kskwai.com/s1/i/qr-timeout.36ec907.svg")
.tips
    font-size 14px
    margin 10px
.suffix-tips
    font-size 12px
    color #222222
    line-height 22px
.text-strong
    color primary-color
.main-text
    font-weight bold

.close-icon
    position absolute
    right 10px
    top 10px
    width 15px
    height 15px
</style>