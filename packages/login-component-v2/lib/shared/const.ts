export const EMAIL_SOURCE = [
    {
        label: '163.com',
        value: '163.com',
        key: '163.com',
    },
    {
        label: '126.com',
        value: '126.com',
        key: '126.com',
    },
    {
        label: 'sina.com',
        value: 'sina.com',
        key: 'sina.com',
    },
    {
        label: 'gmail.com',
        value: 'gmail.com',
        key: 'gmail.com',
    },
    {
        label: 'qq.com',
        value: 'qq.com',
        key: 'qq.com',
    },
    {
        label: '139.com',
        value: '139.com',
        key: '139.com',
    },
]
export const KEYCODE = {
    BACKSPACE: 8,
    TAB: 9,
    ENTER: 13,
    SHIFT: 16,
    CTRL: 17,
    ALT: 18,
    ESC: 27,
    SPACE: 32,
    END: 35,
    HOME: 36,
    LEFT: 37,
    UP: 38,
    RIGHT: 39,
    DOWN: 40,
    PAGE_UP: 33,
    PAGE_DOWN: 34,
    '@': 50,
}

export const COUNTRY_CODE = [
    {
        chineseName: '中国大陆',
        englishName: 'China',
        countryCode: '+86',
    },
    {
        chineseName: '中国香港',
        englishName: 'Xiang Gang',
        countryCode: '+852',
    },
    {
        chineseName: '中国台湾',
        englishName: 'Tai Wan',
        countryCode: '+886',
    },
    {
        chineseName: '马来西亚',
        englishName: 'Malaysia',
        countryCode: '+60',
    },
    {
        chineseName: '新加坡',
        englishName: 'Singapore',
        countryCode: '+65',
    },
    {
        chineseName: '日本',
        englishName: 'Japan',
        countryCode: '+81',
    },
    {
        chineseName: '美国',
        englishName: 'American',
        countryCode: '+1',
    },
    {
        chineseName: '英国',
        englishName: 'British',
        countryCode: '+44',
    },
    {
        chineseName: '法国',
        englishName: 'French',
        countryCode: '+1264',
    },
]

export enum EnumPageState {
    form,
    qrcode,
    chooseUser,
    register,
    bindPhone,
    resetPassword,
}

export enum EnumLoginType {
    // C端扫码授权登录
    scanC = 'scanC',
    // B端扫码登录
    scanB = 'scanB',
    // C端短信验证码授权登录
    smsCodeC = 'smsCodeC',
    // B端短信验证码登录
    smsCodeB = 'smsCodeB',
    // C端密码授权登录
    passwordC = 'passwordC',
    // B端密码登录
    passwordB = 'passwordB',
    // C端账号绑定B端账号
    BindB = 'BindB',
    // C端账号快速绑定B端账号
    QuickBindB = 'QuickBindB',
    // C端账号暂时不绑定B端账号
    NotBindB = 'NotBindB',
    // C端账号换绑手机号
    RebindPhoneB = 'RebindPhoneB',
    // C端账号二选一登录
    ChooseC = 'ChooseC',
    // 开放平台授权跳回组件登录
    KSAuthorize = 'KSAuthorize',
    // B端注册
    RegisterB = 'RegisterB',
    // B端重置密码
    ResetPasswordB = 'ResetPasswordB',
    // C端重置密码
    ResetPasswordC = 'ResetPasswordC',
    // C端重置密码选择用户
    ResetPasswordChooseUser = 'ResetPasswordChooseUser',
}

export enum EnumErrorType {
    // 获取配置的接口失败
    GetConfigError = 'GetConfigError',
    // 发送验证码失败
    SendCodeError = 'SendCodeError'
}

export enum EnumInputLoginType {
    phone = 'phone',
    email = 'email',
    phoneAndEmail = 'phoneAndEmail',
}

export const CODE_NEED_BIND = 110300;
export const CODE_NEED_BIND_V2 = 110399;
export const CODE_MULTICHOOSE = 100110031;

export const CODE_BIND_CONFLICT = 110400;

export enum EnumComponentType {
    C,
    BMain,
    BSub,
}
