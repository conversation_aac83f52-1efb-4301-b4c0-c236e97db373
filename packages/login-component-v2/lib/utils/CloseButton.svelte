<script lang="ts">
    import { twMerge } from 'tailwind-merge';
    import ToolbarButton from '$lib/shared/toolbar/ToolbarButton.svelte';

    export let name: string = 'Close';
</script>

<ToolbarButton on:click {name} {...$$restProps} class={twMerge('ms-auto', $$props.class)} let:svgSize>
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16">
        <path fill="#8A9199" fill-rule="evenodd" d="M8 1.5a6.5 6.5 0 1 0 0 13 6.5 6.5 0 0 0 0-13ZM2.5 8a5.5 5.5 0 1 1 11 0 5.5 5.5 0 0 1-11 0Zm8.52 2.313L8.707 8l2.313-2.313-.707-.707L8 7.293 5.687 4.98l-.707.707L7.293 8 4.98 10.313l.707.707L8 8.707l2.313 2.313.707-.707Z" clip-rule="evenodd"/>
    </svg>
</ToolbarButton>
