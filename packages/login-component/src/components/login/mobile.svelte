<script>
    import PlInput from '../input/index.svelte';
    import PlButton from '../button/index.svelte';
    import { getContext, createEventDispatcher } from 'svelte';
    import { getCaptchaToken, login as ssoLogin, passwordLogin, requestMobileCode } from '@ks/sso';
    import { multiUserInfo } from './store';
    import { logger, sendTaskEvent } from '../../common/logger';
    import Tooltip from '../tooltip/index.svelte';
    const dispatch = createEventDispatcher();
    const isProduction = getContext('isProduction');
    const themeColor = getContext('themeColor');
    let openToolTip = false;
    /**
     * 验证方式 验证码 | 密码
     * @type {( 'verification' | 'password')}
     */
    export let phoneType = 'verification';
    // let pending = false;
    let phone = '';
    let password = '';
    let verification = '';
    let countdown = 0;
    let isLogining = false;
    let errorMsg = '';
    export let qrloginVersion;

    $: phoneValid = isProduction ? /^1\d{10}$/.test(phone) : /^1?\d{10}$/.test(phone);
    $: passwordValid = password.length >= 6 && password.length <= 20;
    $: verificationValid = /^\d{4,}$/.test(verification);
    $: canSubmit = phoneType === 'verification'
        ? phoneValid && verificationValid && !isLogining
        : phoneValid && passwordValid && !isLogining;
    function setErrorMsg(msg) {
        errorMsg = msg || '';
    }
    // function login(type) {
    //     cleanLoginInfo();
    // }
    // function cleanLoginInfo() {
    //     let phone = '';
    //     let password = '';
    //     let verification = '';
    //     let countdown = 0;
    // }
    async function getCaptcha(captchaInfo) {
        try {
            setErrorMsg('');
            const {captchaKey, captchaType, captchaUri} = captchaInfo;
            const captchaToken = await getCaptchaToken({
                key: captchaKey,
                type: captchaType,
                uri: captchaUri,
            });
            return captchaToken;
        } catch (e) {
            e.error_msg = e.error_msg || '未知错误';
            setErrorMsg(e.error_msg);
            throw e;
        }
    }

    async function loginSubmit(captchaInfo) {
        const baseTime = Date.now();
        const logTabName = phoneType === 'password' ? phoneType : 'identity_code'
        sendTaskEvent({
            action: 'USER_LOGIN',
            params: {
                tab_name: logTabName,
            },
            status: 'START'
        });
        if (!phoneValid) {
            errorMsg = '请输入有效的手机号';
            return;
        }

        if (phoneType !== 'verification' && !passwordValid) {
            errorMsg = '密码应不少于6位，不大于20位';
            return;
        }

        if (phoneType === 'verification') {
            if (!verification) {
                errorMsg = '请输入验证码';
                return;
            }
            if (!verificationValid) {
                errorMsg = '请输入有效的验证码';
                return;
            }
        }
        if (!canSubmit) {
            return;
        }
        isLogining = true;
        try {
            setErrorMsg('');
            const captchaToken = captchaInfo ? await getCaptcha(captchaInfo) : null;
            const loginPromise =
                phoneType === 'verification'
                    ? ssoLogin({
                        phone: phone,
                        smsCode: verification,
                        captchaToken,
                    })
                    : passwordLogin({
                        phone,
                        password,
                        captchaToken,
                    });
            const result = await loginPromise;
            sendTaskEvent(
                {
                    action: 'USER_LOGIN',
                    params: {
                        result_code: 1,
                        tab_name: logTabName,
                        cost_time: Date.now() - baseTime,
                    },
                    status: 'FINISH',
                }
            )
            isLogining = false;
            dispatch('login-success', {
                loginType: phoneType === 'verification' ? 'smsCode' : 'password',
                result,
            });
        } catch (e) {
            sendTaskEvent({
                action: 'USER_LOGIN',
                params: {
                    result_code: e.result,
                    tab_name: logTabName,
                    cost_time: Date.now() - baseTime,
                },
                status: 'FAIL',
            });
            isLogining = false;
            // 因关闭图形验证码 而导致获取captchaToken失败异常情况吞掉-KSLIVEWEB-1392
            // TODO 需要对error重新包装
            if (e.ret === 2) {
                return;
            }
            if (e.result === 100110013) {
                // 因为svelte的副作用并不是同步，所以需要依赖下一次时间片
                await new Promise(resolve => resolve());
                loginSubmit(e);
                return;
            }
            if (e.result === 100110031) {
                multiUserInfo.set({
                    phone: phone,
                    userInfos: e.userInfos,
                    multiUserToken: e.multiUserToken,
                    loginType: phoneType === 'verification' ? 'smsCode' : 'password',
                });
                return;
            }
            // 用户取消验证码时 result 为 -999, 不提示错误。
            if (e.result === -999) {
                setErrorMsg('');
            } else {
                setErrorMsg(e.error_msg || '未知错误');
            }
            dispatch('login-fail', e);
            throw e;
        }
    }

    async function getVerification() {
        logger.sendImmediately(
            'CLICK',
            {
                action: 'GET_IDENTITY_CODE'
            }
        );
        if (phone === '' && !phoneValid) {
            setErrorMsg('请输入有效的手机号');
            return;
        }
        if (countdown > 0) {
            return;
        }
        try {
            setErrorMsg('');
            await requestMobileCode({
                countryCode: phone.length === 10 ? '+1264' : '+86',
                phone: phone,
                isLogin: true,
            });
            countdown = 60;
        } catch (e) {
            setErrorMsg(e.error_msg);
        }
    }

    $: if (countdown > 0) {
        setTimeout(() => countdown--, 1000);
    }

    // watch phoneType change, clear errorMsg
    $: errorMsg = (phoneType, '');

    $: {
        logger.sendImmediately(
            'SHOW',
            {
                action: 'loginType',
                params: {
                    type: phoneType,
                },
            }
        );
    }

</script>

<div class="normal-login normal-login-{qrloginVersion}">
    {#if phoneType === "verification"}
        <div class="normal-login-item normal-login-item-{qrloginVersion}">
            <PlInput
                className="large"
                maxlength="11"
                placeholder="请输入手机号"
                bind:value={phone}
            />
        </div>
        <div
            class="normal-login-item normal-login-item-{qrloginVersion} verification-input"
        >
            <PlInput
                className="large"
                maxlength="11"
                placeholder="请输入验证码"
                bind:value={verification}
            >
                <span
                    slot="append"
                    class="get-verification {themeColor}"
                    class:disabled={countdown}
                    on:click={getVerification}
                >
                    {countdown || "获取验证码"}
                </span>
            </PlInput>
        </div>
        <div class="error-msg {themeColor}">{errorMsg}</div>
        <div class="padder padder-{qrloginVersion}" />
        {#if qrloginVersion === "v2"}
            <PlButton
                type="primary"
                size="large"
                className="pl-btn-large-padding"
                on:click={e => loginSubmit()}>登录</PlButton
            >
        {:else}
            <PlButton
                type="primary"
                size="large"
                radius
                on:click={e => loginSubmit()}>登录</PlButton
            >
        {/if}
    {:else}
        <div class="normal-login-item normal-login-item-{qrloginVersion}">
            <PlInput
                className="large"
                maxlength="11"
                placeholder="请输入手机号"
                bind:value={phone}
            />
        </div>
        <div
            class="normal-login-item normal-login-item-{qrloginVersion} verification-input"
        >
            <PlInput
                className="large pwd-input"
                maxlength="20"
                placeholder="请输入密码"
                bind:value={password}
                type="password"
                overflow="visible"
            >
                <div
                    class="forget-pwd-hint"
                    slot="append"
                    on:mouseenter={() => (openToolTip = true)}
                    on:mouseleave={() => (openToolTip = false)}
                >
                    忘记密码？
                    <div
                        class="forget-pwd-hit-tooltip"
                        class:v2={qrloginVersion === "v2"}
                    >
                        <Tooltip
                            bind:open={openToolTip}
                            direction="bottom"
                            hideIcon
                            align="end"
                        >
                            <div class="hit-content">
                                <div class="hit-title">已登录快手APP</div>
                                <div class="hit-desc">
                                    前往快手APP-设置-账号与安全-设置密码；按指引操作即可
                                </div>
                                <div style="margin-top: 8px;" />
                                <div class="hit-title">未登录快手APP</div>
                                <div class="hit-desc">
                                    前往快手APP-登录-帮助-找回账号；按指引操作即可
                                </div>
                            </div>
                        </Tooltip>
                    </div>
                </div>
            </PlInput>
        </div>

        <div class="error-msg {themeColor}">{errorMsg}</div>
        <div class="padder padder-{qrloginVersion}" />
        <div class="register-license">
            登录即代表同意
            <a
                    href="https://www.kuaishou.com/about/policy?tab=protocol"
                    target="_blank"
                    rel="noopener"
            >用户协议</a>
            和
            <a
                    href="https://www.kuaishou.com/about/policy?tab=privacy"
                    target="_blank"
                    rel="noopener"
            >隐私政策</a>
        </div>
        {#if qrloginVersion === "v2"}
            <PlButton
                type="primary"
                size="large"
                className="pl-btn-large-padding"
                on:click={e => loginSubmit()}>登录</PlButton
            >
        {:else}
            <PlButton
                type="primary"
                size="large"
                radius
                on:click={e => loginSubmit()}>登录</PlButton
            >
        {/if}
    {/if}
</div>

<style type="text/stylus">
@import '../../common/variables.styl';
.normal-login
    box-sizing border-box
    position relative
    margin-top 29px!important
    &-item
        margin-bottom 20px
        width 100%
        height 46px
        border-radius 4px
        // >>> .pl-input
        //    border-radius 4px
        // >>> .pl-input-text
        //    padding-left 16px
        //    font-size 15px
    &-item-v2
        margin-bottom 8px
.normal-login-v2
    margin-top 17px!important

// TODO这货可以封装一下其实……
.get-verification
    color brand-primary
    cursor pointer
    margin-right 10px
    font-size 15px
    display inline-block
    &.disabled
        cursor not-allowed
    &.blue
        color brand-primary-blue
.change-login
    margin-top -3px
    margin-bottom 17px
    color brand-primary
    cursor pointer
    font-size 14px
.error-msg
    font-size 14px
    padding-left 5px
    height 20px
    color font-color-error
    &.blue
        color font-color-error-blue
.padder
    height 40px
.padder-v2
    height 20px
.login-button
    font-size 14px
    &[disabled]
        background #FAFAFA
.normal-login-item
    position relative
    .forget-pwd-hint
        color #898a8c
        cursor default
        position relative
        .forget-pwd-hit-tooltip
            position absolute
            right 20px
            bottom 0
            width 0
            height 0
            :global(.bx--tooltip)
                max-width none
                background rgba(0, 0, 0, 0.8)
                padding 10px 12px
            :global(.bx--tooltip__caret)
                margin-right 40px !important
            &.v2
                right -45px
                :global(.bx--tooltip__caret)
                    margin-right 104px !important
.forget-pwd-hit-tooltip
    .hit-content
        width: 292px
        color: #ffffff;
        .hit-title
            font-family: PingFangSC, PingFangSC-Medium;
            height: 22px;
            line-height: 22px;
            font-size: 14px;
            font-weight: 500;
        .hit-desc
            font-family: PingFangSC, PingFangSC-Regular;
            opacity: 0.8;
            line-height: 20px;
            font-size: 12px;
            font-weight: 400;

.register
    &-license
        margin-bottom 10px
        text-align center
        font-size 12px
        color font-color-license
        > a
            color link-color-license;
            text-decoration none;
</style>
