<script context="module" lang="ts">
    // this part is shared between Radio and Checkbox
    import { twMerge } from 'tailwind-merge';

    const colorClasses: Record<FormColorType, string> = {
        primary: '',
        secondary: 'text-secondary-600 focus:ring-secondary-500 dark:focus:ring-secondary-600',
        red: 'text-red-600 focus:ring-red-500 dark:focus:ring-red-600',
        green: 'text-green-600 focus:ring-green-500 dark:focus:ring-green-600',
        purple: 'text-purple-600 focus:ring-purple-500 dark:focus:ring-purple-600',
        teal: 'text-teal-600 focus:ring-teal-500 dark:focus:ring-teal-600',
        yellow: 'text-yellow-400 focus:ring-yellow-500 dark:focus:ring-yellow-600',
        orange: 'text-orange-500 focus:ring-orange-500 dark:focus:ring-orange-600',
        blue: 'text-blue-600 focus:ring-blue-500 dark:focus:ring-blue-600'
    };

    export const labelClass = (inline: boolean, extraClass: string) => twMerge(inline ? 'inline-flex' : 'flex', 'items-center', extraClass);

    export const inputClass = (custom: boolean, color: FormColorType, rounded: boolean, tinted: boolean, spacing: string, extraClass: string) => {
        return twMerge('w-[14px] h-[14px] bg-[#ffffff]', spacing, colorClasses[color], extraClass)
    };
</script>

<script lang="ts">
    import { getContext } from 'svelte';
    import type { FormColorType } from '../types';
    import Label from './Label.svelte';

    export let color: FormColorType = 'primary';
    export let custom: boolean = false;
    export let inline: boolean = false;
    export let group: number | string | undefined = undefined;
    export let value: number | string = '';
    export let spacing: string = $$slots.default ? 'me-2' : '';

    // tinted if put in component having its own background
    let background: boolean = getContext('background');
</script>

<Label class={labelClass(inline, $$props.class)} show={$$slots.default}>
    <input type="radio" bind:group={group} on:change on:click on:keydown on:keypress on:keyup {value} {...$$restProps}
           class={inputClass(custom, color, false, background, spacing, $$props.class)} />
    <slot />
</Label>
