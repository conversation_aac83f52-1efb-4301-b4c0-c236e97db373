<template>
  <div class="one-id-query-container">
      <MultiAccounts
        :multi-accounts="multiAccounts"
        @success="onLoginSuccess"
        @error="onLoginError"
      />
  </div>
</template>

<script lang="ts">
import MultiAccounts from '@/modules/one-id-query/components/login-multi-accounts/LoginMultiAccounts.vue';
import { MULTI_ACCOUNT }  from '@/modules/one-id-query/components/login-multi-accounts/types';
import { defineComponent, ref } from '@vue/composition-api';
import { Toast } from '@ks/sharp-ui';
import { KUAISHOU_ACCOUNT_UNREGISTER } from '@/common/config/links';
import { getEnv } from '@/common/config/base';

export default defineComponent({
  components: {
    MultiAccounts
  },

  setup() {
    const multiAccounts = ref<MULTI_ACCOUNT>({
      userInfos: [],
      multiUserToken: '',
      phone: '',
      countryCode: '',
    });

    try {
      multiAccounts.value = JSON.parse(sessionStorage.multiAccountsInfo);
    } catch (e) {
      console.log(e);
    }

    const onLoginSuccess = () => {
      location.href = KUAISHOU_ACCOUNT_UNREGISTER[getEnv()];
    };

    const onLoginError = (err: any) => {
      Toast.error('选择账号失败，请检查网络，稍后再试！');
    };

    return {
      multiAccounts,
      onLoginSuccess,
      onLoginError,
    };
  }
});
</script>

<style scoped lang="less"></style>
