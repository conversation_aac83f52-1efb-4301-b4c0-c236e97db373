<style scoped lang="less" src="./first.less" />
<script lang="ts" src="./first.ts"></script>

<template>
    <div class="one-id-query-container">
        <div class="logo" style="margin-top: 4.5625em;">
            <img src="@/assets/kwai.png" class="icon" />
        </div>
        <div style="margin-top: 15.8125em;">
            <div style="line-height: 1; text-align: center;" v-if="!inApp">
                <div>
                    <span class="gray-text">为了您的账号安全</span>
                </div>
                <div>
                    <span class="gray-text"> 请在快手APP内完成手机号码解绑操作 </span>
                </div>
            </div>
            <div style="text-align: center; margin-top: 2.5em;">
                <div @click="onOpenAppToUnbindBtnClick" class="red-btn">
                    {{ inApp ? '手机号码解绑' : '打开快手app解绑' }}
                </div>
            </div>
        </div>
        <div style="text-align: center; margin-top: 5.25em">
            <span class="feedback" @click="onFeedBackBtnClick">在线投诉</span>
        </div>
    </div>
</template>
