<template>
    <div class="debug-page">
        调试页
        <dl>
            <dt>sid</dt>
            <dd>
                <input
                    type="text"
                    v-model="params.sid"
                >
            </dd>
            <dt>kpn</dt>
            <dd>
                <input
                    type="text"
                    v-model="params.kpn"
                >
            </dd>
            <dt>bizName</dt>
            <dd>
                <input
                    type="text"
                    v-model="params.bizName"
                >
            </dd>
        </dl>
        <div>
            {{accountVerify.url}}
        </div>
        <dl>
            <dt>idCardVerifyUrl</dt>
            <dd>{{accountVerify.idCardVerifyUrl}}</dd>
        </dl>
        <div>
            {{accountVerify.params}}
        </div>
        <div>
            <button @click="queryRealVerifyUrl">
                请求实名Qrcode地址
            </button>
        </div>
        <div>
            <a
                :href="qrcodeUrl"
                target="_blank"
            >核验页面</a>
        </div>
    </div>
</template>

<script lang="ts">
import { reactive, computed } from '@vue/composition-api';
export default {
    name: 'debug-page',
    setup() {
        const params = reactive({
            bizName: 'LIVE_STREAM_START',
            sid: 'kuaishou.api',
            kpn: 'KUAISHOU',
        });
        const accountVerify = reactive({
            url: '',
            params: {},
            idCardVerifyUrl: '',
        });
        function queryRealVerifyUrl() {
            const formData = new FormData();

            formData.append('bizNameForIdCard', params.bizName);
            formData.append('authType', '2');

            fetch('/rest/infra/id/card/qrToken/url/gen', { body: formData, method: 'POST' })
                .then(res => {
                    return res.json();
                })
                .then(res => {
                    const { url } = res;
                    accountVerify.url = url;
                    const match = url.match(/idCardVerifyStartUrl=([^&]+)/);
                    const idCardVerifyUrl = decodeURIComponent(match[1]);
                    const query = idCardVerifyUrl.split('?')[1];
                    accountVerify.idCardVerifyUrl = idCardVerifyUrl;
                    const queryParams = query.split('&')
                        .map(pair => pair.split('='))
                        .reduce((obj, [key, value]) => {
                            obj[key] = value;
                            return obj;
                        }, {} as Record<string, any>);
                    console.log(queryParams);
                    Object.assign(accountVerify.params, queryParams);
                });
        }
        const qrcodeUrl = computed(() =>
            `http://localhost:8080/account-qrcode/?sid=${params.sid}&kpn=${params.kpn}&type=9&&url=${encodeURIComponent(accountVerify.url)}`,
        );

        return {
            params,
            accountVerify,

            qrcodeUrl,
            queryRealVerifyUrl,
        };
    },
};
</script>

<style lang="stylus">
.debug-page
    text-align left;
</style>
