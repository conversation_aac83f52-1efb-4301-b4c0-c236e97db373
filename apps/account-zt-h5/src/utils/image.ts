function scaleSize(
    originalWidth: number,
    originalHeight: number,
    maxLength: number,
): { width: number; height: number } {
    // 要求 width > height
    if (originalWidth <= originalHeight) {
        const res = scaleSize(originalHeight, originalWidth, maxLength);
        return {
            width: res.height,
            height: res.width,
        };
    }
    let x = 1;
    if (originalWidth > maxLength) {
        x = originalWidth / maxLength;
    }
    return {
        width: originalWidth / x,
        height: originalHeight / x,
    };
}

// 处理图片文件
export async function transformImageFile(file: File, maxLength: number, type = 'image/png'): Promise<File> {
    try {
        const image = new Image();

        await new Promise((resolve, reject) => {
            image.onload = () => resolve();
            image.onerror = () => reject();
            image.src = URL.createObjectURL(file);
        });

        const { width: originalWidth, height: originalHeight } = image;

        // 计算压缩后的宽高
        const { width, height } = scaleSize(originalWidth, originalHeight, maxLength);

        const canvas = document.createElement('canvas');

        canvas.width = width;
        canvas.height = height;

        const context2d = canvas.getContext('2d');

        if (!context2d) throw new Error('context2d is ' + context2d);

        // 填充图片
        context2d.drawImage(image, 0, 0, width, height);

        const blob: Blob = await new Promise((resolve, reject) => {
            canvas.toBlob(
                function (blob) {
                    if (blob) {
                        resolve(blob);
                    } else {
                        reject('canvas BlobCallback result === null.');
                    }
                },
                type,
                1,
            );
        });

        return new File([blob], file.name, {
            lastModified: file.lastModified,
            type,
        });
    } catch (e) {
        console.error(e);
    }
    return file;
}
