# general-sso 接口

- 账号中心接口
  - /pass/${config.appName}/web/sms/code，请求短信验证码
  - /pass/${config.appName}/web/email/code，请求邮箱验证码
  - /pass/${config.appName}/web/login/mobileCode，短信登录
  - /pass/${config.appName}/web/login/mobilePassword，密码登录
  - /pass/${config.appName}/web/login/multiTokenLogin，多账号选择
  - /pass/${config.appName}/web/login/passToken，passToken （已标注即将废弃）
  - refreshLoginStatus 相关
  - /pass/${config.appName}/web/logout，登出
  - /pass/${config.appName}/web/register/mobilePassword，短信注册
  - /pass/${config.appName}/web/register/emailPassword，邮件注册
  - /pass/${config.appName}/web/login/emailPassword，邮件登录
  - /pass/${config.appName}/web/password/checkPassword，检查密码强度
  - /pass/${config.appName}/web/account/checkMobileExist，检查手机号是否可用
  - /pass/${config.appName}/web/account/checkEmailExist，检查邮箱是否可用
  - /pass/${config.appName}/web/password/reset，用短信验证码重置密码
  - /pass/${config.appName}/web/password/resetByEmail，用邮箱验证码重置密码

- 业务域名下的接口
  - /rest/infra/sts
