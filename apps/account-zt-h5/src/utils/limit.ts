// throttle 节流 限制 n 毫秒内只能执行一次
// debounce 防抖 推迟到 n 毫秒后再执行

// 节流
export function throttle(func: Function, duration: number = 1000, thisArg?: any) {
    let lastExecTime = 0;
    if (thisArg) {
        func = func.bind(thisArg);
    }
    return function (this: any) {
        const now = Date.now();
        const dis = now - lastExecTime;
        if (dis < duration!) {
            // 短期内已经执行过了, 现在不再执行了
            return;
        }
        lastExecTime = now;
        func.apply(this, arguments);
    };
}

// 防抖
export function debounce(func: Function, duration: number = 1000, thisArg?: any) {
    let tid: any;
    if (thisArg) {
        func = func.bind(thisArg);
    }
    return function (this: any) {
        if (tid) {
            // 如果有正在等着被执行的任务, 取消它
            clearTimeout(tid);
        }
        // 新开一个 n 毫秒后执行的任务
        tid = setTimeout(() => {
            func.apply(this, arguments);
        }, duration);
    };
}
