@startuml
'https://plantuml.com/sequence-diagram

autonumber

participant "调用方" as sso
participant "logger proxy" as proxy
participant "内置logger" as innerLogger
participant "远程logger" as remoteLogger

sso -> proxy: 调用report,上报埋点
proxy -> remoteLogger: 动态加载远程logger
proxy -> proxy: 等待远程logger加载结果
remoteLogger -> proxy: 远程logger完成加载
proxy -> innerLogger: 远程logger加载失败
proxy -> remoteLogger: 远程logger加载成功

@enduml
