export function parseUrl(url: string) {
    const index = url.indexOf('?');
    if (index < 0) {
        return {
            baseUrl: url,
            search: '',
            paramObj: {},
        };
    }
    const baseUrl = url.substr(0, index);
    const search = url.substr(index + 1);
    const paramObj = search.split('&')
        .map(x => x.split('='))
        .reduce((o, [key, value]) => {
            o[decodeURIComponent(key)] = decodeURIComponent(value);
            return o;
        }, {} as Record<string, any>);
    return {
        baseUrl,
        search,
        paramObj,
    };
}
