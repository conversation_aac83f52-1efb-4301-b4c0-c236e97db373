<template>
    <div class="faby-container" ref="fabyPage">
        <div class="logo">
            <img src="@/assets/kwai.png" class="icon">
        </div>
        <div class="input-area" v-if="!hasGetAccount">
            <div class="form-item phone">
                <div class="left">
                    <span class="country-code">+86</span>
                    <input
                        v-model="phoneInfo.phone"
                        @focus="changeFocus(inputType.phone)"
                        type="text"
                        class="phone-number"
                        placeholder="请输入手机号"
                        maxlength="11"
                    >
                </div>
                <img
                    v-show="phoneInfo.phone && phoneFocus"
                    @click.stop="phoneInfo.phone = ''"
                    class="close-icon"
                    src="@/assets/delete.png"
                >
            </div>
            <div class="form-item code" v-if="hasRunned">
                <input
                    v-model="phoneInfo.smsCode"
                    type="text"
                    class="phone-number"
                    @focus="changeFocus(inputType.smsCode)"
                    maxlength="6"
                    placeholder="请输入验证码"
                >
                <div class="right">
                    <template v-if="counting">
                        <span class="retry">重新发送({{countDown}})</span>
                    </template>
                    <template v-else>
                        <img
                            v-show="phoneInfo.smsCode && smsCodeFocus"
                            @click.stop="phoneInfo.smsCode = ''"
                            class="close-icon"
                            src="@/assets/delete.png"
                        >
                        <span
                            @click="sendSmsCode"
                            class="send-code"
                        >获取验证码</span>
                    </template>
                </div>
            </div>
            <sp-button
                v-if="!hasRunned"
                @click="sendSmsCode"
                :disabled="!phoneInfo.phone"
            >
                获取验证码
            </sp-button>
            <sp-button
                v-else
                @click="queryAccount"
                :disabled="phoneInfo.smsCode.length < 6"
            >
                下一步
            </sp-button>
        </div>
        <div class="account-area" v-else>
            <div class="phone-and-count">
                <div class="phone">
                    {{maskPhone}}
                </div>
                <div class="count">
                    共注册{{acounts.length}}个快手账号
                </div>
            </div>
            <div class="accounts" :class="{shadow: acounts.length >= 3}">
                <div class="account-wrapper">
                    <div
                        v-for="(account,index) in acounts"
                        :key="index"
                        class="account"
                    >
                        <img :src="account.userHead">
                        <span>{{account.userName}}</span>
                    </div>
                </div>
            </div>
            <div class="notice">
                请在应用市场下载快手APP，使用手机号登录账号，可以在快手APP上换绑或者注销不常用的账号。如果以上不是您注册的账号，可以拨打客服热线进行账号解绑。
            </div>
        </div>
        <footer
            class="footer"
        >
            <span>客服电话：400-126-0088</span>
            <span class="footer-link" v-if="!hasGetAccount">
                <span class="footer-split">|</span>
                <a :href="unbindDesUrl">
                    查询与解绑说明
                    <i class="footer-icon"></i>
                </a>
            </span>
        </footer>
    </div>
</template>

<script lang="ts">
import { ref, reactive, computed, defineComponent } from '@vue/composition-api';
import { Toast, Button } from '@ks/sharp-ui';
import { mask } from '@/utils/string';
import {
    sendSmsCode as sendSmsCodeApi,
    queryAccount as queryAccountApi,
    UserInfo,
    QueryAccountResult,
} from '@/api/query-account-by-phone';
import useCountDown from '@/common/hooks/useCountDown';
import {
    startIdentityVerify,
    getZtIdentityVerification,
    NEED_CAPTACHA_VERICATION,
} from '@/utils/identity-verification';
import { KUAISHOU_UNBIND_DES_URL } from '@/common/config/links';
import { COUNTRY_CODE } from '@/common/config/constants';
const sid = 'kuaishou.zhengtong';

export default defineComponent({
    components: {
        'sp-button': Button,
    },
    setup(props: any, vm: any) {
        const {
            start,
            stop,
            value: countDown,
            running: counting,
            hasRunned,
        } = useCountDown(60);

        const phoneInfo = reactive({
            countryCode: COUNTRY_CODE,
            phone: '',
            smsCode: '',
        });

        const hasGetAccount = ref(false);

        const acounts = ref<Array<UserInfo>>([]);
        enum inputType {
            phone = 1,
            smsCode = 2,
        }
        const phoneFocus = ref<Boolean>(false); // 手机号聚焦
        const smsCodeFocus = ref<Boolean>(false); // 验证码聚焦

        const maskPhone = computed(() => mask({ str: phoneInfo.phone, showStartLen: 3, showEndLen: 4 }));
        const changeFocus = (type: number) => {
            if (type === inputType.phone) {
                phoneFocus.value = true;
                smsCodeFocus.value = false;
            } else {
                phoneFocus.value = false;
                smsCodeFocus.value = true;
            }
        };

        async function sendSmsCode() {
            const { countryCode, phone } = phoneInfo;
            if (!/^1[3-9]\d{9}/.test(phone)) {
                return Toast.error('请输入正确的手机号');
            }

            const params = {
                sid,
                countryCode,
                phone,
                type: '53',
                ...getZtIdentityVerification(),
            };
            sendSmsCodeApi(params).then(
                res => {
                    start();
                },
                async err => {
                    if (err?.result === NEED_CAPTACHA_VERICATION && err?.url) {
                        const res = await startIdentityVerify(err.url);
                        res && sendSmsCode();
                    } else {
                        Toast.error(err?.error_msg || '发送失败');
                    }
                },
            );
        }

        async function queryAccount() {
            const { countryCode, phone, smsCode } = phoneInfo;
            if (!/^\d{6}/.test(smsCode)) {
                return Toast.error('验证码格式不正确');
            }
            const params = {
                sid,
                type: '53',
                ...phoneInfo,
            };
            queryAccountApi(params).then(
                res => {
                    acounts.value = (res as unknown as QueryAccountResult).userInfos || [];
                    hasGetAccount.value = true;
                },
                err => {
                    Toast.error(err?.error_msg || '网络异常');
                },
            );
        }

        return {
            phoneInfo,
            acounts,
            sendSmsCode,
            queryAccount,
            hasRunned,
            countDown,
            counting,
            hasGetAccount,
            maskPhone,
            inputType,
            phoneFocus,
            smsCodeFocus,
            changeFocus,
            unbindDesUrl: KUAISHOU_UNBIND_DES_URL,
        };
    },
});
</script>

<style lang="less" scoped>
input {
    background: transparent;
    -webkit-appearance: none;
    border-radius: 0;
    box-shadow: none;
    margin: 0;
    padding: 0;
    font-size: inherit;
    resize: none;
    border: none;
    outline: none;
}

input::-webkit-input-placeholder {
    color: #d1cece;
}

.faby-container{
    width: 100vw;
    height: 100vh;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 1.03rem;
    font-family: PingFangSC, PingFangSC-Regular;
    .logo{
        margin-top: 0.69rem;
        .icon{
            width: 2.64rem;
            height: 1.34rem;
            background: #ffffff;
        }
    }
    .input-area {
        margin-top: 0.56rem;
        font-size: 0.33rem;
        .form-item{
            width: 5.43rem;
            height: 1.03rem;
            border-bottom: 1px solid #eaeaea;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .left {
                overflow: auto;
                display: flex;
                flex: 1;
                align-items: center;
            }
            .country-code{
                width: .88rem;
                height: .45rem;
                display: block;
                line-height: 0.45rem;
                font-weight: bold;
                position: relative;
                &::after {
                    content: '';
                    display: inline-block;
                    height: .34rem;
                    border-right: 1px solid #eaeaea;
                    position: absolute;
                    z-index: 2;
                    right: 0;
                    bottom: .05rem;
                }
            }
            .phone-number{
                margin-left: 0.28rem;
                border: 0;
                color: #222;
                flex: 1;
                overflow: auto;
            }
            ::-webkit-input-placeholder {
                color: #9c9c9c
            }
            .close-icon{
                margin-right: 0.18rem;
                width: 0.29rem;
                height: 0.29rem;
            }
            .right{
                display: flex;
                align-items: center;
                font-size: 0.24rem;
                font-weight: 400;
                white-space: nowrap;
            }
            .send-code{
                margin-left: 0.18rem;
                color: #fe3666;
            }
            .retry{
                color: #9c9c9c;
            }
            &.code {
                .phone-number {
                    margin-left: 0;
                }
            }
        }

        button {
            margin-top: 0.51rem;
            height: 1.01rem;
            border-radius: .56rem;
            font-size: .29rem;
            font-weight: bold;
        }
        /deep/ .sp-button--disabled {
            .sp-button__text {
                opacity: 0.5;
            }
        }
    }
    .account-area{
        margin-top: 0.89rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .phone-and-count{
            width: 6.34rem;
            height: 1.32rem;
            text-align: center;
            .phone{
                height: .65rem;
                font-size: .47rem;
                color: #000000;
                line-height: .47rem;
                font-weight: bold;
            }
            .count {
                margin-top: 0.05rem;
                height: .39rem;
                font-size: .29rem;
                font-weight: 400;
                color: #9c9c9c;
                line-height: .29rem;
            }
        }

        .accounts {
            position: relative;
            margin-top: 0.18rem;
            .account-wrapper{
                max-height: 4.47rem;
                overflow: auto;
            }
            .account + .account {
                margin-top: 0.18rem;
            }
            .account {
                width: 6.34rem;
                height: 1.37rem;
                display: flex;
                align-items: center;
                background: #f8f8f8;
                border-radius: .21rem;
                img {
                    margin: 0 .29rem;
                    width: .8rem;
                    height: .8rem;
                    border-radius: 50%;
                }
                span{
                    font-size: .29rem;
                    font-weight: 400;
                    color: #000000;
                    line-height: .43;
                }
            }
        }
        .notice{
            margin-top: 0.29rem;
            font-size: .24rem;
            line-height: .34rem;
            font-weight: 400;
            color: #9c9c9c;
        }
    }
    .shadow{
        &::after{
            position: absolute;
            bottom: 0;
            left: 0;
            width: 6.34rem;
            height: 1.37rem;
            content: '';
            background: linear-gradient(180deg,rgba(255,255,255,0.00) 6%, #ffffff 100%);
            pointer-events: none;
        }
    }
    .footer{
        position: fixed;
        bottom: 1.72rem;
        height: .36rem;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: .25rem;
        font-weight: 400;
        color: #9c9c9c;
        a {
            color: #4C86FC;
            display: inline-flex;
            align-items: center;
        }
        .footer-split{
            padding: 0 0.2rem;
        }
        .footer-icon{
            display: inline-block;
            width: .3rem;
            height: .3rem;
            align-self: center;
            background-repeat: no-repeat;
            background-image: url(../../../assets/arrow-r.png);
            background-size: 100%;
        }
    }
}
</style>
