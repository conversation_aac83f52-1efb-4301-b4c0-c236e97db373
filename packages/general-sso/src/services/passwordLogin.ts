import { getConfig } from '@/core/configInit';
import { genEncryptReqParams } from '@/utils';
import { DEFAULT_COUNTRY_CODE } from '@/config';
import { getLoginToken } from '@/core/loginToken';

/**
 * 密码登录
 */
export interface PhonePasswordLogin {
    password: string;
    phone: string;
    countryCode?: string;
}
export function passwordLogin({ phone, countryCode = DEFAULT_COUNTRY_CODE, password }: PhonePasswordLogin) {
    const encryptReqParams = genEncryptReqParams({
        phone,
        password,
    });
    const config = getConfig();
    const params = {
        sid: config.sid,
        countryCode,
        ...encryptReqParams,
    };

    return getLoginToken(`/pass/${config.appName}/web/login/mobilePassword/v2`, {
        ...params,
    });
}

export interface AccountPasswordLoginParams {
    account: string;
    password: string;
}
export function accountPasswordLogin({ account, password }: AccountPasswordLoginParams) {
    const config = getConfig();
    const params = {
        sid: config.sid,
        account,
        password,
    };
    return getLoginToken(`/pass/${config.appName}/web/login/accountPassword/v2`, {
        ...params,
    });
}

export function accountPasswordLoginV2({ account, password }: AccountPasswordLoginParams) {
    const encryptReqParams = genEncryptReqParams({
        account,
        password,
    });
    const config = getConfig();
    const params = {
        sid: config.sid,
        ...encryptReqParams,
    };

    return getLoginToken(`/pass/${config.appName}/web/login/accountPassword/v2`, {
        ...params,
    });
}
