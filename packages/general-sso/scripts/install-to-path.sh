#!/bin/bash

<<作用

这个脚本的作用是把 @ks/general-sso 这个包直接应用到本地项目。
这样就不用每次修改 SDK 后都发一个包，再在本地项目安装最新版才能看到效果。

作用

# 请业务方将 to 变量设为本地项目中 general-sso 的绝对安装路径
# to="/Users/<USER>/Desktop/demo-project/node_modules/@ks/general-sso"
to="/Users/<USER>/Workspace/gitlabworks/user-center/account-zt-fe-demo/node_modules/@ks/general-sso"

############################# 一般不用编辑下面的脚本 #############################

# 当前脚本所在路径
DIR=$(cd -P `dirname $0` && pwd)

# @ks/general-sso 这个包所在的路径
from=$(dirname "$DIR")

# 切换到 sdk 根目录
cd "${DIR}/.."

# 格式化代码
npm run "sdk:lint-fix"

# 编译项目
npm run "sdk:build"

# 导入脚本函数
. "${DIR}/npmlink.sh"

# 调用 npm pack 本地打包 @ks/general-sso, 并解压至业务方本地项目
npmlink "$from" "$to"

