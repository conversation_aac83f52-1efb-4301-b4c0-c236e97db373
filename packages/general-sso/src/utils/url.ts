export function formatParams(url: string) {
    const urlObj = new URL(url, window.location.origin); // 创建 URL 对象
    const path = urlObj.pathname; // 获取路径部分，不包含域名
    // 将查询参数转换成对象格式
    const queryParams = new URLSearchParams(urlObj.search);
    const query: Record<string, string> = {};
    queryParams.forEach((value, key) => {
        query[key] = value;
    });
    return {
        path,
        query,
    }
}
