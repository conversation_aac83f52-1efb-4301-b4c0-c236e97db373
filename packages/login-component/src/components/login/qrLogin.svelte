<script>
    import { getContext } from 'svelte';
    import QrCode from './qrCode.svelte';
    import { sendCommonPV } from '../../common/logger';
    const themeColor = getContext('themeColor');

    sendCommonPV('SCAN_CODE_LOGIN');
</script>

<div class="qr-login">
    <QrCode
        width={162}
        height={162}
        on:login-success
        on:login-fail
    />
</div>

<p class="qr-login-download">
    打开
    <strong class="{themeColor}">快手APP</strong>
    扫一扫登录
</p>

<style type="text/stylus">
    @import '../../common/variables.styl';

.qr-login
    margin-top 64px
    height 180px
    text-align center
    &-download
        margin 45px 0 0px
        padding-bottom 31px
        font-size 13px
        text-align center;
        color font-color-primary
        strong
            color brand-primary
            text-decoration none;
            &.blue
                color brand-primary-blue
</style>
