<script lang="ts">
    import Popover from "$lib/shared/popover/Popover.svelte";
    import IconEllipse from "$lib/utils/IconEllipse.svelte";
    import IconError from "$lib/utils/IconError.svelte";
    import IconPass from "$lib/utils/IconPass.svelte";
    import { validateLowSafety, validateMiddleSafety } from "$lib/utils/validate";

    export let registerStore;
    let safety = currentSafety();
    function currentSafety() {
        if (!$registerStore.password1) {
            return 'default';
        } else if (validateLowSafety($registerStore.password1)) {
            return 'low';
        } else if (validateMiddleSafety($registerStore.password1)) {
            return 'middle';
        } else {
            return 'high';
        }
    }
    registerStore.subscribe(value => {
        safety = currentSafety();
    });
</script>

<Popover
        placement="right"
        trigger="focus"
        class="bg-white"
>
    <div class="password-tip-container">
        {#if safety === 'default'}
                <div class="password-tip-body">
                    <div class="flex items-center">
                        <IconEllipse />
                        请输入8~20个字符的密码
                    </div>
                    <div class="flex items-center">
                        <IconEllipse />
                        建议使用大小写和特殊字符
                    </div>
                </div>
        {:else}
            {#if safety === 'low'}
                <div class="password-tip-header low-safety">
                    安全系数：低
                </div>
                <div class="password-tip-divide">
                    <div class="low-safety-divide low-safety"></div>
                </div>
            {:else if safety === 'middle'}
                <div class="password-tip-header middle-safety">
                    安全系数：中
                </div>
                <div class="password-tip-divide">
                    <div class="middle-safety-divide"></div>
                </div>
            {:else if safety === 'high'}
                <div class="password-tip-header high-safety">
                    安全系数：高
                </div>
                <div class="password-tip-divide">
                    <div class="high-safety-divide"></div>
                </div>
            {/if}
            <div class="password-tip-body">
                <div class="flex items-center">
                    {#if $registerStore.password1?.length <= 20 && $registerStore.password1?.length >= 8}
                        <IconPass
                                width={16}
                                height={16}
                                class="p-[4px]"
                        />
                    {:else}
                        <IconError
                                width={16}
                                height={16}
                                class="p-[4px]"
                        />
                    {/if}
                    请输入8~20个字符的密码
                </div>
                <div class="flex items-center">
                    {#if safety === 'low'}
                        <IconEllipse />
                    {:else}
                        <IconPass
                                width={16}
                                height={16}
                                class="p-[4px]"
                        />
                    {/if}
                    建议使用大小写和特殊字符
                </div>
            </div>
        {/if}
    </div>
</Popover>
<style>
    .password-tip-container {
        white-space: nowrap;
        padding: 16px;
        width: 248px;
        border-radius: 4px;
        color: #434343;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 22px;
        text-align: left;
        vertical-align: top;
    }
    .password-tip-body {
        display: flex;
        flex-direction: column;
        gap: 1px;
    }
    .password-tip-header {
        font-size: 12px;
        font-weight: 500;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: left;
        vertical-align: top;
    }
    .password-tip-divide {
        height: 2px;
        background: #F5F7F9ff;
        margin-bottom: 8px;
    }
    .low-safety {
        color: #F93920;
    }
    .middle-safety {
        color: #D98900;
    }
    .high-safety {
        color: #078F59;
    }
    .low-safety-divide {
        width: 70px;
        height: 2px;
        background: #F93920ff;
    }
    .middle-safety-divide {
        width: 140px;
        height: 2px;
        background: #D98900ff;
    }
    .high-safety-divide {
        width: 100%;
        height: 2px;
        background: #078F59ff;
    }
</style>
