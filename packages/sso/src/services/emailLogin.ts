import { request } from '@/core/request';
import { genEncryptReqParams } from '@/utils/encrypt';
import { privateGetConfig } from '@/core/configInit';
import { AuthTokenResult, BaseRes, QuickLoginResult } from '../types/services';
import { getLoginToken } from '../core/loginToken';
import { wrapLoginRequest } from '../core/bidLoginAdapter';

export interface EmailRegisterInfo {
    email: string,
    password: string;
    emailCode: string | number;
    setCookie?: boolean;
}

export interface EmailLoginInfo {
    email: string;
    emailCode: string;
}

export interface requestEmailCodeDataWithEmail {
    type: number;
    email: string;
}

export interface requestEmailCodeDataOnLoggedIn {
    type: number;
}

export interface resetPasswordByEmailOptions {
    email: string;
    password: string;
    emailCode: string | number;
}


export interface validateEmailOptions {
    emailCode: string | number;
    type: number;
}

/**
 * 发送邮箱验证码
 */
export function requestEmailCode({type}: requestEmailCodeDataOnLoggedIn): Promise<BaseRes>;
export function requestEmailCode({email, type}: requestEmailCodeDataWithEmail): Promise<BaseRes>;
export function requestEmailCode({email, type}: any) {
    const config = privateGetConfig();
    let url = config.baseUrl + '/pass/kuaishou/email/requestEmailCode';
    if (!email) { // 已登录情况下，不需要传email
        url = config.baseUrl + '/pass/kuaishou/email/requestEmailCodeWithToken';
    }
    return request<BaseRes>(url, {
        sid: config.sid,
        type,
        email,
    });
}

/**
 * 邮箱 + 邮箱验证码 + 密码注册
 */
export function emailRegister({
    email,
    password,
    emailCode,
    setCookie = true
}: EmailRegisterInfo): Promise<AuthTokenResult | QuickLoginResult> {
    const config = privateGetConfig();
    const encryptReqParams = genEncryptReqParams({
        email,
        password,
    });
    const r = getLoginToken('/pass/kuaishou/email/ks/register/v2', {
        sid: config.sid,
        ...encryptReqParams,
        emailCode,
        setCookie,
    });
    return wrapLoginRequest(r);
}

/**
 * 邮箱验证码登录
 */
export function emailLogin({email, emailCode}: EmailLoginInfo): Promise<AuthTokenResult | QuickLoginResult> {
    const config = privateGetConfig();
    const r = getLoginToken('/pass/kuaishou/email/ks/loginByCode', {
        sid: config.sid,
        email,
        emailCode,
    });
    return wrapLoginRequest(r);
}

export function resetPasswordByEmail({email, emailCode, password}: resetPasswordByEmailOptions) {
    const config = privateGetConfig();
    const encryptReqParams = genEncryptReqParams({
        email,
        password,
    });
    return request<BaseRes>(config.baseUrl + '/pass/kuaishou/pwd/resetByEmail/v2', {
        sid: config.sid,
        ...encryptReqParams,
        emailCode,
    });
}

export interface ResetPasswordByEmailWithoutLoginOption {
    email: string;
    emailCode: string;
    password: string;
}

export function resetPasswordByEmailWithoutLogin({ email, emailCode, password }: ResetPasswordByEmailWithoutLoginOption) {
    const config = privateGetConfig();
    const encryptReqParams = genEncryptReqParams({
        email,
        password,
    });
    return request<BaseRes>(config.baseUrl + '/pass/kuaishou/pwd/resetByEmailWithoutLogin/v2', {
        sid: config.sid,
        ...encryptReqParams,
        emailCode,
    });
}

export function validateEmail({emailCode, type}: validateEmailOptions) {
    const config = privateGetConfig();
    return request<BaseRes>(config.baseUrl + '/pass/kuaishou/email/validateEmailCodeWithToken', {
        sid: config.sid,
        emailCode,
        type,
    });
}

