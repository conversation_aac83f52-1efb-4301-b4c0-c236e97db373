import Vue from 'vue';
import App from './App.vue';
import router, { routes } from './router/index';
import ksBridge from '@ks/ks-bridge';
import VueCompositionAPI from '@vue/composition-api';
import { validLoginPageQuery } from './utils/validQuery';

if (process.env.NODE_ENV !== 'production' || process.env.USE_LOCAL === 'true') {
    const Vconsole = require('vconsole/dist/vconsole.min.js');
    new Vconsole();
}

Vue.use(VueCompositionAPI);

Vue.prototype.$ksBridge = ksBridge;

Vue.config.productionTip = false;

window.onload = function () { // 页面加载时，对url中的query参数校验
    validLoginPageQuery();
};

ksBridge.isSupportAsync({
    api: 'setBounceStyle',
    complete: (isSupport: Boolean) => {
        setTimeout(() => {
            if (isSupport) {
                ksBridge.setBounceStyle({
                    enable: false,
                });
            } else {
                //
            }
        }, 0);
    },
});

async function dark() {
    return new Promise(resolve => {
        ksBridge.getDarkMode({
            success: (ret: any) => {
                if (ret.darkMode) {
                    document.documentElement.classList.add('dark');
                } else {
                    document.documentElement.classList.remove('dark');
                }
                resolve(true);
            },
            fail() {
                resolve(false);
            },
        });
    });
}

async function init() {
    const supportDark = await ksBridge.isSupportAsync({
        api: 'getDarkMode',
    });
    if (supportDark) {
        await dark();
    }
    const isDark = document.documentElement.classList.contains('dark');
    const getDarkOpt = (() => {
        const colorOpt = isDark
            ? {
                titleTextColor: '#9C9C9C',
                titleBackgroundColor: '#19191e',
            }
            : {};
        return (title: string) => {
            return {
                title: title,
                ...colorOpt,
            };
        };
    })();

    router.beforeEach((to, from, next) => {
        if (!routes.some(route => route.path === to.path)) {
            return next({
                name: 'fallback',
            });
        }
        if (to.path !== '/login') {
            document.title = to.meta.title;
            ksBridge.isSupportAsync({
                api: 'setPageTitle',
                complete: (isSupport: Boolean) => {
                    setTimeout(() => {
                        if (isSupport) {
                            ksBridge.setPageTitle(getDarkOpt(to.meta.title));
                        } else {
                            //
                        }
                    }, 0);
                },
            });
        }
        next();
    });

    new Vue({
        router,
        render: h => h(App),
    }).$mount('#app');
}

init();
