import { getDomainFormUrl } from "@/common/utils/common";

export function request<Response = unknown>(url: string, data: Record<string, any>, method = 'POST') {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open(method, url);
        xhr.setRequestHeader('Content-type', 'application/json');
        // 添加泳道的字段
        // xhr.setRequestHeader('trace-context', JSON.stringify({ laneId: 'STAGING.lwq_test' }));
        xhr.responseType = 'json';
        xhr.withCredentials = true;
        // 只有异步才能设置timeout
        xhr.onload = () => {
            let res = xhr.response;
            if (typeof res === 'string') {
                // ie 11 xhr.responseType = 'json'; 这个设置是无效的
                // https://github.com/naugtur/xhr/issues/123
                try {
                    res = JSON.parse(res);
                } catch(e) {
                    reject(e);
                }
            }
            const status = xhr.status;
            if (status < 200 && status >= 300 && status !== 304 || res.result !== 1) {
                reject({
                    ...res,
                    isXHRError: false,
                    errorReason: 'responseData'
                } as Response);
            } else {
                resolve(res);
            }
        };

        xhr.ontimeout = e => {
            reject({
                ...e,
                isXHRError: true,
                errorReason: 'timeout',
                error_msg: '请求超时',
            });
        };
        xhr.onerror = e => {
            reject({
                ...e,
                isXHRError: true,
                errorReason: 'error',
                error_msg: '网络错误',
                error_domain: getDomainFormUrl(url),
            })
        }
        xhr.send(JSON.stringify(data));
    }) as Promise<Response>;
}
