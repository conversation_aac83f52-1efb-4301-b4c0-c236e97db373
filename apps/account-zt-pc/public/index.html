<!DOCTYPE html>
<html
    data-building-date-time="<%= htmlWebpackPlugin.options.buildingDateTime %>"
    data-git-commit-log="<%= htmlWebpackPlugin.options.gitCommitLog %>"
    lang="zh-CN"
>

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport"
    content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no,viewport-fit=cover">
  <link rel="dns-prefetch" href="var----h5----var.kskwai.com">
  <link rel="shortcut icon" href="//var----h5----var.kskwai.com/udata/pkg/fe/favicon.70ff1fcc.ico" type="image/x-icon">
  <link rel="preload" href="//var----h5----var.kskwai.com/kos/nlav10761/account-zt/logo-default.svg" as="image">
  <link rel="preload" href="//var----h5----var.kskwai.com/kos/nlav10758/pc/login/img/banner.859d2e1ce811aca6.png"
    as="image">
  <title>
    <%= htmlWebpackPlugin.options.title %>
  </title>
  <script>
    // 原 /pc/account/passToken/result 页面的逻辑
    if (location.pathname === '/pc/account/passToken/result') {
      var query = location.search
        .substr(1)
        .split('&')
        .reduce((result, pair) => {
          var list = pair.split('=');
          var key = list[0];
          var value = list[1];
          result[decodeURIComponent(key)] = decodeURIComponent(value);
          return result;
        }, {});
      var data = {
        successful: query.successful,
        failReason: query.failReason,
        for: query.for || '',
        id: query.id,
      };
      window.parent.postMessage(JSON.stringify(data), '*');
    }
  </script>
</head>

<body>
  <noscript>
    <strong>抱歉，运行本网页需要您开启javascript</strong>
  </noscript>
  <script>
    var cdn_public_path = '//var----h5----var.kskwai.com';
  </script>
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>

</html>
