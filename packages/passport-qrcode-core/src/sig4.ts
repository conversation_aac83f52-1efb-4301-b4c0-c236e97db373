import type { Jose, SigConfig } from '@ks-cqc/h5-sig4-lite-obf';
import type { InternalAxiosRequestConfig } from 'axios';

import type { SignSuccess } from './utils';
import { formatParams, noopAdapter, now, parsePayload } from './utils';
import { sendEvent } from './radar';

// 签名方法调用：通过config注入签名项，suc回调打印的的signResult即为签名的加密字符串
async function getSig4(jose: typeof Jose, config: SigConfig): Promise<SignSuccess> {
    return new Promise((resolve, reject) => {
        jose.call('$encode', [
            config,
            {
                suc(signResult, signInput, report) {
                    // const debugFlag = import.meta.env.DEV;
                    // if (debugFlag) {
                    //     console.debug('[Gundam]', 'sig4加签结果', signResult);
                    //     console.debug('[Gundam]', 'sig4加签输入', signInput);
                    // }
                    sendEvent({
                        name: 'sig4 suc',
                        extra_info: {
                            signResult,
                            signInput,
                        }
                    });
                    resolve({
                        signResult,
                        signInput,
                        report,
                    });
                },
                err(error, report) {
                    sendEvent({
                        name: 'sig4 err',
                        extra_info: {
                            error,
                        }
                    });
                    // eslint-disable-next-line prefer-promise-reject-errors
                    reject({
                        signFailed: true,
                        error,
                        report,
                    });
                },
            },
        ]);
    });
}

// 获取签名版本号
function getSig4Ver(jose: typeof Jose) {
    return jose.call('$getCatVersion') || '';
}

export function sig4Adapter(jose: typeof Jose, defaultAdapter = noopAdapter) {
    return async function adapter(config: InternalAxiosRequestConfig, next = defaultAdapter) {
        const { path, query, data, contentType } = formatParams(config);
        const base = '/rest/c/infra/ks/new/qr';
        const signVersion = getSig4Ver(jose);
        try {
            const params = {
                url: base + path,
                query: {
                    caver: signVersion,
                    ...query,
                },
                form:
                    contentType === 'application/x-www-form-urlencoded' && data ? parsePayload(data, contentType) : {},
                requestBody:
                    contentType === 'application/json' && data ? parsePayload(data, contentType) : {},
            };
            const { signResult, report } = await getSig4(jose, params);
            config.params = Object.assign(config.params || {}, {
                __NS_hxfalcon: signResult,
                caver: signVersion,
            });
        } catch (error) {
            // 加签逻辑失败, 执行对应的hook
        }
        return next(config);
    };
}

export default sig4Adapter;
