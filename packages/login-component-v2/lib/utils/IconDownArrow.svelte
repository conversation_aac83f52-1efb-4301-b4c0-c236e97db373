<script lang="ts">
    import { twMerge } from 'tailwind-merge';
    import ToolbarButton from '$lib/shared/toolbar/ToolbarButton.svelte';
    export let name: string = 'DownArrow';
    export let width = 24;
    export let height = 24;
</script>

<ToolbarButton on:click {name} {...$$restProps} class={twMerge($$props.class)} let:svgSize
>
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16">
        <path fill="#333840" fill-rule="evenodd" d="M12.354 6.352 8 10.705 3.646 6.352l.708-.707L8 9.29l3.646-3.646.708.707Z" clip-rule="evenodd"/>
    </svg>
</ToolbarButton>

<style>
</style>
