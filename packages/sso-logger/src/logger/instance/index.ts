import { LoggerConfig, ReportParams } from '@/logger/types';
import { Tracker } from '@/logger/tracker';

export default class Logger {
    private config: LoggerConfig;
    private tracker: Tracker;
    constructor(config: LoggerConfig) {
        this.config = config;
        this.tracker = new Tracker(config);
    }
    public report(params: ReportParams) { // 上报日志
        return this.tracker.sendApiTrack(params);
    }
}
