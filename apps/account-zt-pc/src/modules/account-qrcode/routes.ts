import { RouteConfig } from 'vue-router';

const routes: Array<RouteConfig> = [
    {
        path: '/account-qrcode/',
        name: 'account-qrcode',
        component: () => import(/* webpackChunkName: "account-qrcode" */ './index.vue'),
    },
    {
        path: '/account-qrcode/debug',
        name: 'account-qrcode-debug',
        // route level code-splitting
        // this generates a separate chunk (about.[hash].js) for this route
        // which is lazy-loaded when the route is visited.
        component: () => import(/* webpackChunkName: "account-qrcode" */ './debug.vue'),
    },
];

export default routes;
