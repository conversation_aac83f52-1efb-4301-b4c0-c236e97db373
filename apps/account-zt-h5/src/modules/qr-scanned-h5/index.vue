<template>
    <div v-if="!isLoading" class="qr-scanned-page">
        <div class="header">
            <div
                v-if="hasError"
                class="qr-scanned-page-icon"
                :style="{
                    backgroundImage: `url(${ERROR_ICON_URL})`,
                }"
            ></div>
            <div
                v-else
                class="qr-scanned-page-icon"
                :style="{
                    backgroundImage: `url(${iconURL})`,
                }"
            ></div>
            <template v-if="hasError">
                <h3 class="qr-scanned-page-title error">
                    二维码已失效
                </h3>
                <p class="qr-scanned-page-desc">
                    请重新扫码
                </p>
            </template>

            <template v-else>
                <h3 class="qr-scanned-page-title">
                    {{title}}
                </h3>
                <p v-if="describe" class="qr-scanned-page-desc">
                    {{ describe }}
                </p>
            </template>
        </div>
        <div class="footer">
            <button class="button button--primary" @click="handleConfirmOrQuit">
                {{ hasError ? '重新扫码' : '确认登录' }}
            </button>
            <button class="button button--ghost" @click="cancelConfirm">
                取消登录
            </button>
        </div>
    </div>
    <div v-else class="loading-ctn">
        <Loading></Loading>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref } from '@vue/composition-api';
import { init, qrScanned, qrAccept, cancelQrScanLogin } from '@ks/sso';
import ksBridge from '@ks/ks-bridge';
import { stringify } from 'qs';
import { Loading } from '@ks/sharp-ui';
import { parseUrl } from '@/utils/url';
import { getInfo } from '@/api/qr-scanned-h5';
import { DEFAULT_ICON_URL, ERROR_ICON_URL, DEFAULT_TITLE } from './config';

const query = parseUrl(location.href).paramObj;

export default defineComponent({
    components: {
        Loading,
    },
    setup() {
        init({
            sid: query.sid || '',
            env: query.env || 'production',
            enableSig4: true,
        });

        const qrType = query.qrType;
        const isLoading = ref(true);
        const iconURL = ref(query.iconURL || DEFAULT_ICON_URL); // 图标地址
        const title = ref(query.title || DEFAULT_TITLE); // 页面标题
        const describe = ref(query.describe || '');
        const canConfirm = ref(false);
        const hasError = ref(false);

        const handleConfirmOrQuit = async () => {
            if (hasError.value) {
                ksBridge.exitWebView();
                return;
            }
            if (!canConfirm.value) {
                return;
            }
            try {
                const accpetRes = await qrAccept({
                    qrLoginToken: query.qrLoginToken,
                });
                if (accpetRes.hasUri) {
                    window.location.href = `${accpetRes.redirectUri}?${stringify(query)}`;
                } else {
                    ksBridge.exitWebView();
                }
            } catch (e) {
                hasError.value = true;
            }
        };

        const cancelConfirm = async () => {
            if (!hasError.value) {
                await cancelQrScanLogin({
                    qrLoginToken: query.qrLoginToken,
                });
            }
            ksBridge.exitWebView();
        };

        Promise.allSettled([
            qrType ? getInfo({
                qrType: query.qrType,
            }) : Promise.reject(),
            qrScanned({
                qrLoginToken: query.qrLoginToken,
            }),
        ]).then(([
            infoRes,
            qrScannedRes,
        ]) => {
            // icon 地址、文案
            if (infoRes.status === 'fulfilled') {
                if (infoRes.value.icon) {
                    iconURL.value = infoRes.value.icon;
                }
                if (infoRes.value.dis_msg) {
                    title.value = infoRes.value.dis_msg;
                }
            }

            if (qrScannedRes.status === 'fulfilled' && qrScannedRes.value.result === 1) {
                canConfirm.value = true;
            } else {
                hasError.value = true;
            }

            isLoading.value = false;
            ksBridge.setTopLeftBtn({
                show: true,
                // 以前一直使用 close，先保持原样吧
                // @ts-ignore
                icon: 'close',
                onClick: () => {
                    cancelConfirm();
                },
            });
            ksBridge.setSlideBack({
                enabled: false,
            });
            ksBridge.setPhysicalBackButton({
                onClick: () => {
                    cancelConfirm();
                },
            });
        });

        return {
            isLoading,
            ERROR_ICON_URL,
            iconURL,
            title,
            describe,
            canConfirm,
            hasError,
            handleConfirmOrQuit,
            cancelConfirm,
        };
    },
});
</script>

<style lang="stylus" scoped>
@import './style.styl'
</style>
