<script lang="ts">
    import Input from "$lib/shared/forms/Input.svelte";
    import IconHide from "$lib/shared/icons/IconHide.svelte";
    import IconSee from "$lib/shared/icons/IconSee.svelte";
    import Button from "$lib/shared/button/index.svelte";
    import ChooseReset from "$lib/features/components/ResetPassword/ChooseReset.svelte";

    import {
        requestCodeByAccountV2 as requestCodeB,
        resetPasswordByAccountV2 as resetPasswordB,
    } from '@ks/general-sso';

    import { isValidPhone, isValidEmail } from "$lib/utils/validate";
    import { useToast } from "$lib/shared/toast/toastStore";

    import { createResetStore } from "$lib/store/resetStore";
    import { getContext, createEventDispatcher } from "svelte";
    import {CODE_MULTICHOOSE, EnumErrorType, EnumLoginType} from "$lib/shared/const";
    import { resetMultiUserInfo } from "$lib/store/multiUserInfo";

    const store = createResetStore(["phone", "email"]);
    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    const onLoginStart = getContext('onLoginStart');
    const configStore = getContext('loginPageConfig');

    const currentMainState = $configStore.currentMainState;
    const currentIsC = currentMainState === 1;
    const dispatch = createEventDispatcher();

    const toast = useToast();


    let showPassword = false;
    let buttonForbidden = false;
    let buttondisabled = false;
    let buttonText = '重置';


    let sendCodeClicked = false;
    async function sendCodeAction() {
        if (!store.reducers.validateSend()) {
            toast.show({
                message: $store.loginErrorMsg,
            });
            return;
        }
        if ($store.countDown > 0) {
            return;
        }
        try {
            currentIsC ? await store.reducers.smsCodeSendC() : await requestCodeB({
                account: $store.account,
                type: isValidPhone($store.account) ? 1485 : 460,
            });
            sendCodeClicked = true;
            store.setValues({
                countDown: 60,
            });
            const interal = setInterval(() => {
                if ($store.countDown <= 0) {
                    clearInterval(interal);
                    return;
                }
                store.setValues({
                    countDown: $store.countDown - 1,
                });
            }, 1000);
        } catch (error) {
            console.log('error', error);
            store.setValues({
                loginErrorMsg: error?.error_msg || '未知错误，请稍后重试'
            });
            onFail({
                loginType: EnumErrorType.SendCodeError,
                error,
            });
        }
    }

    let codeClass = '';
    $: {
        if (isValidEmail($store.account) || ($store.account?.length <= 11 && $store.account?.length >= 8)) {
            codeClass = 'activate';
        } else {
            codeClass = '';
        }
    }

    async function onReset() {
        if (!store.reducers.validateLogin()) {
            return;
        }
        try {
            const loginType = currentIsC ? EnumLoginType.ResetPasswordC : EnumLoginType.ResetPasswordB;
            onLoginStart({
                name: loginType,
            });
            buttondisabled = true;
            const res = currentIsC ? await store.reducers.resetPhoneC() : await resetPasswordB({
                account: $store.account,
                code: $store.smsCode,
                password: $store.password1,
                countryCode: '+86',
            });
            buttondisabled = false;
            buttonText = '重置成功';
            toast.show({
                message: '重置成功',
            });
            onSuccess({
                loginType: loginType,
                result: res,
            });
        } catch (error) {
            buttondisabled = false;
            if (error?.result === CODE_MULTICHOOSE) {
                // 存在两个账号的情况，需要选择
                resetMultiUserInfo.set({
                    userInfos: error.userInfos,
                    multiUserToken: error.multiUserToken,
                    phone: $store.account,
                    smsCode: $store.smsCode,
                    password: $store.password1,
                });
                store.setValues({
                    chooseResetOpen: true,
                });
                return;
            }
            store.setValues({
                loginErrorMsg: error?.error_msg || '未知错误，请稍后重试',
            });
            onFail({
                loginType: loginType,
                error,
            });
        }
    }

    store.subscribe((value) => {
        buttonForbidden = store.reducers.isButtonForbidden();
    });
    function closeChooseReset() {
        store.setValues({
            chooseResetOpen: false,
        });
    }
</script>

<div>
    <div class="login-body">
        <Input
                placeholder="请输入邮箱/手机号"
                bind:value={$store.account}
                onBlur={store.reducers.onAccountBlur}
                onClear={() => {
                    store.setValues({
                        account: '',
                    });
                    setTimeout(() => {
                        store.setValues({
                            loginErrorMsg: '',
                        })
                    });
                }}
        >
        </Input>
        <Input
                placeholder="设置新登录密码（8-20个字符）"
                maxlength={20}
                type={showPassword ? 'text': 'password'}
                bind:value={$store.password1}
                onBlur={store.reducers.onPassword1Blur}
                onClear={() => {
                    store.setValues({
                        password1: '',
                    });
                    setTimeout(() => {
                        store.setValues({
                            loginErrorMsg: '',
                        })
                    });
                }}
        >
            <slot slot="right">
                <div class="password-slot-right">
                    {#if showPassword}
                        <IconSee
                                on:click={() => (showPassword = !showPassword)}
                        />
                    {:else}
                        <IconHide
                                on:click={() => (showPassword = !showPassword)}
                        />
                    {/if}
                </div>
            </slot>
        </Input>
        <Input
                placeholder="请确认密码"
                maxlength={20}
                bind:value={$store.password2}
                onBlur={store.reducers.onPassword2Blur}
                type={showPassword ? 'text': 'password'}
                onClear={() => {
                    store.setValues({
                        password2: '',
                    });
                    setTimeout(() => {
                        store.setValues({
                            loginErrorMsg: '',
                        })
                    });
                }}
        >
        </Input>
        <Input
                placeholder="请输入验证码"
                bind:value={$store.smsCode}
                errorMsg={$store.loginErrorMsg}
                onBlur={store.reducers.onSmsCodeBlur}
                onClear={() => {
                    store.setValues({
                        smsCode: '',
                    });
                    setTimeout(() => {
                        store.setValues({
                            loginErrorMsg: '',
                        })
                    });
                }}
        >
            <slot slot="right">
                <div class="send-code"
                     on:click={sendCodeAction}
                >
                    {#if $store.countDown}
                        <span class="text-[#C6C6C6ff]">{$store.countDown}s</span>
                    {:else}
                        <span class={codeClass}>
                            {#if sendCodeClicked}
                                重新发送
                            {:else}
                                获取验证码
                            {/if}
                        </span>
                    {/if}
                </div>
            </slot>
        </Input>
        <Button
                on:click={onReset}
                disabled={buttondisabled}
                forbidden={buttonForbidden}
                class="mt-[40px]"
        >
            重置
        </Button>
    </div>
    <ChooseReset
            isOpen={$store.chooseResetOpen}
            onClose={closeChooseReset}
    />
</div>


<style>
    .login-body {
        padding-left: 35px;
        padding-right: 35px;
        margin-top: 40px;
    }
    .activate {
        color: #326BFBff;
    }
    .send-code {
        white-space: nowrap;
        color: #C6C6C6ff;
        font-size: 16px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 22px;
        text-align: right;
        vertical-align: middle;
    }
</style>
