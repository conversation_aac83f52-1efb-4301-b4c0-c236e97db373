function stringifyPrimitive(v: string | boolean | number | undefined | null) {
    switch (typeof v) {
        case 'string':
            return v;
        case 'boolean':
            return v ? 'true' : 'false';
        case 'number':
            return isFinite(v) ? v : '';
        default:
            return '';
    }
}

export default function stringify(obj: any, sep = '&', eq = '=') {
    if (obj === null) {
        obj = null;
    }
    if (typeof obj === 'object') {
        return Object.keys(obj)
            .map(k => {
                const ks = encodeURIComponent(stringifyPrimitive(k)) + eq;
                if (Array.isArray(obj[k])) {
                    return obj[k]
                        .map((v: any) => {
                            return (
                                ks + encodeURIComponent(stringifyPrimitive(v))
                            );
                        })
                        .join(sep);
                }
                return ks + encodeURIComponent(stringifyPrimitive(obj[k]));
            })
            .join(sep);
    }
    return (
        encodeURIComponent(stringifyPrimitive(null))
        + eq
        + encodeURIComponent(stringifyPrimitive(obj))
    );
}
