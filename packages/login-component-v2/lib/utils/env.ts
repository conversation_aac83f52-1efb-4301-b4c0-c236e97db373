export const getServiceOwnParams = (env: 'staging' | 'production') => {
    const appIdMap = {
        staging: {
            appId: 'ks694117295173644432',
            redirect_uri: 'https://passport-bid.staging.kuaishou.com',
        },
        production: {
            appId: 'ks667377170011306237',
            redirect_uri: 'https://id.kuaishou.com',
        }
    };
    const config = appIdMap[env];
    const s = `{ "appId":"${config.appId}","scope":"user_info","redirect_uri":"${config.redirect_uri}"}`
    return s;
}
