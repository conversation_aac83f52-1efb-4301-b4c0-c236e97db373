import { request } from '@/core/request';
import { genEncryptReqParams } from '@/utils/encrypt';
import { DEFAULT_COUNTRY_CODE } from '@/config/constants'
import { privateGetConfig } from '@/core/configInit';
import { BaseRes } from '../types/services';

export interface resetPasswordByPhoneForPhoneOptions {
    countryCode: string;
    phone: string;
    password: string;
    smsCode: string | number;
    targetUserId: number | string;
    multiUserToken: string;
}

export interface resetPasswordByPhoneForAccountOptions {
    account: string;
    password: string;
    smsCode: string | number;
    targetUserId: number | string;
    multiUserToken: string;
}

export function resetPasswordByPhone({ account, smsCode, password, targetUserId, multiUserToken }: resetPasswordByPhoneForAccountOptions): Promise<BaseRes>;
export function resetPasswordByPhone({ countryCode, phone, smsCode, password, targetUserId, multiUserToken }: resetPasswordByPhoneForPhoneOptions): Promise<BaseRes>;
export function resetPasswordByPhone({ countryCode = DEFAULT_COUNTRY_CODE, phone, account, smsCode, password, targetUserId, multiUserToken }: any) {
    const config = privateGetConfig();
    const encryptReqParams = genEncryptReqParams({
        account,
        phone,
        password,
    });
    return request<BaseRes>(config.baseUrl + '/pass/kuaishou/pwd/resetByPhone/v2', {
        sid: config.sid,
        countryCode,
        ...encryptReqParams,
        smsCode,
        targetUserId,
        multiUserToken,
    });
}

export interface ResetPasswordByPhoneWithoutLoginOptions {
    countryCode?: string;
    phone?: string;
    account?: string;
    smsCode: string;
    targetUserId?: number;
    multiUserToken?: string;
    password: string;
}
export function resetPasswordByPhoneWithoutLogin({ countryCode = DEFAULT_COUNTRY_CODE, phone, account, smsCode, targetUserId, multiUserToken, password }: ResetPasswordByPhoneWithoutLoginOptions) {
    const config = privateGetConfig();
    const encryptReqParams = genEncryptReqParams({
        account,
        phone,
        password,
    });
    return request<BaseRes>(config.baseUrl + '/pass/kuaishou/pwd/resetByPhoneWithoutLogin/v2', {
        sid: config.sid,
        countryCode,
        ...encryptReqParams,
        smsCode,
        targetUserId,
        multiUserToken,
    });
}
