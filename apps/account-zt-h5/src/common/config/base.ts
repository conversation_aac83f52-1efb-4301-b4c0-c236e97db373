import { getBaseUrl } from '@ks-passport/url-kit';

const DOMAIN_NAME_PROD = 'passport.kuaishou.com';
const DOMAIN_NAME_PRT = ['zt-passport.prt.kuaishou.com', 'passport-prt.test.gifshow.com'];
// const DOMAIN_NAME_STAGING = 'ksid-staging.corp.kuaishou.com';

export function getEnv(): 'production' | 'prt' | 'staging' {
    if (DOMAIN_NAME_PROD === location.hostname) {
        return 'production';
    }

    if (DOMAIN_NAME_PRT.includes(location.hostname)) {
        return 'prt';
    }

    return 'staging'
}

export const isDev = () => getEnv() === 'staging';

// 账号后端 origin
export const baseApiUrls = {
    production: getBaseUrl('production'),
    staging: getBaseUrl('staging'),
    // prt: 'https://id-kuaishou.test.gifshow.com',
    prt: 'https://id-kuaishou.prt.kuaishou.com',
};

export const baseUrl = baseApiUrls[getEnv()];
// export const baseUrl = 'https://zhuzijian.staging.kuaishou.com';

