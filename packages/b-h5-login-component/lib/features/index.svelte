<script lang="ts">
    import SmsLogin from "$lib/features/SmsLogin.svelte";
    import PasswordLogin from "$lib/features/PasswordLogin.svelte";
    import ResetPassword from "$lib/features/components/ResetPassword/ResetPassword.svelte";
    import { getContext, onMount } from "svelte";
    import IconBack from "$lib/shared/icons/IconBack.svelte";
    import QuickBind from "$lib/features/bind/QuickBind.svelte";
    import { useURLSearch } from "$lib/utils/url";
    import { SNSLoginCode } from '@ks/sso';
    import { EnumLoginType } from "$lib/shared/const";
    import { CODE_NEED_BIND_V2 } from "$lib/shared/const";
    import { bindPhoneInfo } from "$lib/features/store";

    export let defaultLoginType: string = 'code';
    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    const onLoginStart = getContext('onLoginStart');

    const configStore = getContext('loginPageConfig');
    let currentLoginMainMethod = defaultLoginType;
    const query = useURLSearch();

    function removeSpecificParams() {
        const url = new URL(window.location.href);

        // 删除目标参数
        ['loginType', 'code', 'state'].forEach(param => {
            url.searchParams.delete(param);
        });

        // 构建新URL（保留路径、其他参数、hash）
        const newURL = url.pathname + url.search + url.hash;

        // 替换URL
        window.history.replaceState({}, document.title, newURL || url.pathname);
    }
    onMount(async () => {
        try {
            if (query?.loginType) {
                onLoginStart({
                    name: EnumLoginType.KSAuthorize,
                });
                const code = query.code;
                removeSpecificParams();
                const r = await SNSLoginCode(code as string);
                onSuccess({
                    loginType: EnumLoginType.KSAuthorize,
                    result: r,
                });
            }
        } catch (e) {
            if (e?.result === CODE_NEED_BIND_V2) {
                bindPhoneInfo.set({
                    phone: e.bindInfo.phone,
                    bindAuthToken: e.bindInfo.bindAuthToken,
                });
                currentLoginMainMethod = 'quickBind';
                return;
            }
            onFail({
                loginType: EnumLoginType.KSAuthorize,
                result: e,
            })
        }
    });

    function updateLoginMethod(event: CustomEvent<string>) {
        currentLoginMainMethod = event.detail;
    }

    let title = '';
    $: {
        if (['code', 'password'].includes(currentLoginMainMethod)) {
            if ($configStore?.currentMainState === 1) {
                title = '快手APP账号授权登录';
            } else {
                title = '快手商家账号登录';
            }
        } else {
            title = '密码重置';
        }
    }
</script>

<div>
{#if currentLoginMainMethod === 'reset'}
    <div class="icon-wrapper">
        <IconBack
                on:click={() => {
                    currentLoginMainMethod = 'password';
                }}
        />
    </div>
{/if}
{#if currentLoginMainMethod !== 'quickBind'}
    <div class="title">
        {title}
    </div>
{/if}
{#if currentLoginMainMethod === 'code'}
    <SmsLogin
            on:updateLoginMethod={updateLoginMethod}
    />
{:else if currentLoginMainMethod === 'password'}
    <PasswordLogin
            on:updateLoginMethod={updateLoginMethod}
    />
{:else if currentLoginMainMethod === 'reset'}
    <ResetPassword
            on:updateLoginMethod={updateLoginMethod}
    />
{:else}
    <QuickBind />
{/if}
</div>


<style>
    .title {
        color: #000000;
        font-size: 24px;
        font-weight: 500;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 28px;
        text-align: center;
        vertical-align: middle;
        margin-top: 64px;
    }
    .icon-wrapper {
        margin-top: 12px;
        margin-left: 12px;
    }
</style>
