<script lang="ts">
    import Button from '$lib/shared/button/index.svelte';
    import { notBindAndLogin, phoneRebindAndLogin } from "@ks/general-sso";
    import { createEventDispatcher, getContext } from "svelte";
    import { EnumLoginType } from "$lib/shared/const";
    import { bindConflictInfo, bindPhoneInfo } from "$lib/features/stage1/kuaishou/store";
    import IconLeftArrow from "$lib/utils/IconLeftArrow.svelte";
    import GlobalErrorMsg from "$lib/features/components/GlobalErrorMsg.svelte";

    const dispatch = createEventDispatcher();

    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    const onLoginStart = getContext('onLoginStart');
    const loginPageConfig = getContext('loginPageConfig');

    const currentStage = $loginPageConfig.currentMainState;
    let buttonText = '一键换绑并登录';
    let buttondisabled = false;
    let loginErrorMsg = '';

    async function changeBind() {
        try {
            loginErrorMsg = '';
            onLoginStart({
                name: EnumLoginType.RebindPhoneB,
            });
            buttondisabled = true;
            const res = await phoneRebindAndLogin($bindConflictInfo.authToken);
            buttondisabled = false;
            buttonText = '换绑成功，即将登录';
            setTimeout(() => {
                onSuccess({
                    loginType: EnumLoginType.RebindPhoneB,
                    result: res,
                });
            }, 1000);
        } catch (e) {
            buttondisabled = false;
            loginErrorMsg = e?.error_msg || '网络错误，请稍后重试';
            onFail({
                loginType: EnumLoginType.RebindPhoneB,
                error: e,
            });
        }
    }

    function backToLogin() {
        dispatch('backToLogin');
    }

    function bindOther() {
        dispatch('updateBindStatus', 'BindOther');
    }

    async function notBind() {
        try {
            onLoginStart({
                name: EnumLoginType.NotBindB,
            });
            buttondisabled = true;
            buttonText = '暂不绑定手机号，登录中';
            const res = await notBindAndLogin($bindPhoneInfo.bindAuthToken);
            buttondisabled = false;
            onSuccess({
                loginType: EnumLoginType.NotBindB,
                result: res,
            });
        } catch (e) {
            buttondisabled = false;
            buttonText = '一键换绑并登录';
            onFail({
                loginType: EnumLoginType.NotBindB,
                error: e,
            });
        }
    }

    let imgsrc = '';
    if ($bindConflictInfo.hintInfo.bidBindInfo.icons) {
        imgsrc = $bindConflictInfo.hintInfo.bidBindInfo.icons[0];
    }
</script>

<!-- 使用固定高度容器来保持组件高度一致 -->
<div class="conflict-container">
    <div class="bind-conflict-title">
        账号绑定冲突
    </div>
    <div class="bind-conflict-sub">当前手机号已绑定其他商家账号，且该账号已绑定另一个快手账号</div>
    <div class="bind-conflict-desc">使用商家账号“{$bindConflictInfo.hintInfo.bidBindInfo.nickName}”登录并更改绑定关系，新绑定快手号为“{$bindConflictInfo.hintInfo.newKsBindInfo.nickName}”</div>
    <div class="card">
        <div class="mr-[16px]">
            <img class="icon" src={imgsrc} alt="" />
        </div>
        <div>
            <div class="name">{$bindConflictInfo.hintInfo.bidBindInfo.nickName}</div>
            <div class="info">已绑定手机号：{$bindConflictInfo.hintInfo.bidBindInfo.blurryPhone}</div>
            <div class="info">原绑定快手号：{$bindConflictInfo.hintInfo.oldKsBindInfo.nickName}</div>
            <div class="info">新绑定快手号：{$bindConflictInfo.hintInfo.newKsBindInfo.nickName}</div>
        </div>
    </div>
    <!-- 错误信息容器，固定高度 -->
    <div class="error-container">
        {#if loginErrorMsg}
        <GlobalErrorMsg loginErrorMsg={loginErrorMsg}
            class=""
        />
        {/if}
    </div>
    <Button class="currentB my-[8px]"
            on:click={changeBind}
            disabled={buttondisabled}
    >
        {buttonText}
    </Button>
    <!-- 底部操作区域，动态调整 padding -->
    <div class="vice-operate" class:has-error={loginErrorMsg}>
        <div class="flex items-center gap-[4px] cursor-pointer"
             on:click={backToLogin}
        >
            <IconLeftArrow />
            {currentStage === 3 ? '返回登录' : '返回授权登录'}
        </div>
        <div class="flex items-center gap-[8px]">
                <span class="cursor-pointer"
                      on:click={bindOther}>
                    绑定其他手机号
                </span>
            <span class="divide border-l-[1px] h-[14px]"></span>
            <span class="cursor-pointer"
                  on:click={notBind}
            >
                暂不绑定
            </span>
        </div>
    </div>
</div>

<style>
    /* 主容器，匹配 QuickBindPhone 的内容区域高度 */
    .conflict-container {
        /* 不设置固定高度，让内容自然流动 */
        display: flex;
        flex-direction: column;
    }

    /* 错误信息容器，固定高度以避免布局跳动 */
    .error-container {
        min-height: 48px; /* 为错误信息预留固定空间 */
        margin-bottom: 0px; /* 移除底部边距，通过 padding 控制间距 */
    }

    .bind-conflict-title {
        color: #222222;
        font-size: 24px;
        font-weight: 600;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 32px;
        text-align: left;
        vertical-align: top;
        margin-bottom: 8px;
    }
    .bind-conflict-sub {
        color: #9C9C9C;
        font-size: 12px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: left;
        vertical-align: top;
        margin-bottom: 16px;
    }
    .bind-conflict-desc {
        color: #666666;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        text-align: left;
        vertical-align: top;
        margin-bottom: 12px;
    }
    .card {
        background: #F0F7FFff;
        border-radius: 8px;
        display: flex;
        padding: 16px;
        margin-bottom: 12px;
    }
    .icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
    }
    .name {
        color: #222222;
        font-size: 14px;
        font-weight: 600;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 22px;
        text-align: left;
        vertical-align: top;
    }
    .info {
        color: #666666;
        font-size: 12px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: left;
        vertical-align: top;
        margin-top: 4px;
    }
    .vice-operate {
        color: #9C9C9C;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: center;
        vertical-align: top;
        display: flex;
        justify-content: space-between;
        align-items: center;
        /* 调整 padding 以匹配 QuickBindPhone 的布局 */
        padding-top: 24px; /* 默认 padding，较小以适应内容 */
        transition: padding-top 0.2s ease; /* 平滑过渡 */
    }

    /* 当有错误信息时，减少 padding 以保持总高度不变 */
    .vice-operate.has-error {
        padding-top: 0px; /* 减少 padding 来补偿错误信息的高度 */
    }
</style>
