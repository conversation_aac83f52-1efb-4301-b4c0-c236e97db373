部署图

left to right direction

frame windows {
    card loggerReady
}

frame "sso sdk" {
    card "sso instance" as sso
    card asyncLoadLogger
}

frame "logger sdk"{
card "logger instance" as logger
card ready
card report
}

asyncLoadLogger --> logger: 异步加载
logger -> ready: 完成加载
ready --> loggerReady: 全局调用
loggerReady --> sso: 通知完成加载
sso --> report: 调用方法,上报日志


@startuml
'https://plantuml.com/sequence-diagram

autonumber

participant "logger sdk" as logger
participant "sso sdk" as sso
participant "window.loggerReady" as loggerReady

sso -> loggerReady: 全局注册回调方法,\n用于之后logger加载完的回调
sso -> logger: 异步加载sso
logger -> loggerReady: 完成加载后调用
loggerReady -> sso: 通知logger已加载完成
sso -> sso: 调用logger方法，上报日志


@enduml
