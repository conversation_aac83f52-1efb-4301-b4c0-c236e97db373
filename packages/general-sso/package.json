{"name": "@ks/general-sso", "version": "0.2.14-beta.9", "description": "通用sso（非快手账号体系）", "author": "kuaishou-fe", "main": "./dist/index.js", "module": "./dist/index.esm.js", "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./dist/esm": {"import": "./dist/esm/index.js", "require": "./dist/esm/index.js", "types": "./dist/esm/index.d.ts"}}, "sideEffect": false, "types": "./dist/index.d.ts", "files": ["dist", "README.md", "CHANGELOG.md"], "publishConfig": {"access": "public", "registry": "https://npm.corp.kuaishou.com"}, "repository": {"type": "git", "url": "https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general"}, "scripts": {"build": "npm run sdk:build", "sdk:dev": "rimraf ./dist && rollup -wc", "sdk:build": "rimraf ./dist && rollup -c && father build", "sdk:clean": "rimraf ./dist", "sdk:lint-check": "prettier --check .", "sdk:lint-fix": "prettier --write .", "sdk:publish-to-npm": "npm run 'sdk:build' && if [[ -d ./dist ]]; then npm publish; else echo '请先编译出产物，再发包。'; fi", "sdk:install-to-path": "bash scripts/install-to-path.sh", "demo": "npm run build && http-server ./", "lintXX": "lint-staged", "publish:beta": "npm run sdk:build && pnpm publish --no-git-checks --tag beta"}, "dependencies": {"@ks-cqc/h5-sig4-lite-obf": "3.0.1", "@ks-cqc/device-info": "2.0.1", "@ks/identity-verification": "^0.2.13", "@types/sm-crypto": "^0.3.4", "js-cookie": "^2.2.1", "jsencrypt": "^3.2.1", "@ks/weblogger": "^3.0.17", "@ks-radar/radar-core": "^1.2.4", "@ks-radar/radar-event-collect": "^1.2.4", "@ks-radar/radar-util": "1.2.15", "sm-crypto": "^0.3.13"}, "devDependencies": {"@rollup/plugin-replace": "^3.0.0", "@rollup/plugin-json": "^4.1.0", "@types/jest": "^23.3.7", "@types/js-cookie": "^2.2.6", "@types/node": "14.14.25", "@types/node-fetch": "^2.1.2", "@types/qs": "^6.5.1", "@typescript-eslint/eslint-plugin": "^4.15.2", "@typescript-eslint/parser": "^4.15.1", "eslint": "^7.20.0", "http-server": "^0.12.3", "jest": "^23.6.0", "lint-staged": "^9.5.0", "prettier": "^3.0.0", "rimraf": "^2.6.2", "rollup": "^1.20.3", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.0", "rollup-plugin-visualizer": "^2.5.4", "ts-jest": "^23.10.4", "tslib": "^2.8.1", "typescript": "~5.4.0", "father": "4.5.3"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "lint-staged": {"src/*.ts": ["eslint src --ext .ts"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 9"]}