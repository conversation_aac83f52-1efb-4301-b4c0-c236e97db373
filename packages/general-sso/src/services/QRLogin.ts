import { request, cancelRequest } from '@/core/request';
import { getConfig } from '@/core/configInit';
import { getLoginToken } from '@/core/loginToken';

export interface QRCodeResult {
    qrLoginSignature: string;
    status: string;
    result: number;
    next: string;
}
export interface QRCodeInfo {
    qrLoginToken: string;
    qrLoginSignature: string;
}

export interface QRLoginInfo extends QRCodeInfo {
    imageData: string;
    qrUrl: string;
}
export interface AcceptResult {
    callback: string;
    qrToken: string;
    result: number;
    sid: string;
    serviceOwnToken?: string;
}

/**
 * 获取二维码信息
 */
export function getQRLoginInfo() {
    const config = getConfig();
    const url = '/rest/c/infra/ks/new/qr/start';
    const param: Record<string, any> = {
        sid: config.sid,
        qrType: config.qrType,
        kpn: config.kpn,
    };
    if (config.serviceOwnParams) {
        param.serviceOwnParams = config.serviceOwnParams;
    }
    return request<QRLoginInfo>(url, param).then(({ qrLoginSignature, qrLoginToken, imageData, qrUrl }) => ({
        qrLoginSignature,
        qrLoginToken,
        imageData,
        qrUrl,
    }));
}
/**
 * 等待用户扫码，扫码完成后返回用户基本信息
 * @param QRCodeInfo
 * @returns QRCodeResult
 * @description 这里有两套扫码 老的扫码返回BaseUserInfo 新的扫码返回 QRCodeResult
 */
export function getUserLoginInfo({ qrLoginToken, qrLoginSignature }: QRCodeInfo) {
    const url = '/rest/c/infra/ks/new/qr/scanResult';
    cancelRequest(url);
    return request<QRCodeResult>(url, {
        qrLoginToken,
        qrLoginSignature,
    }).then((res) => {
        return res;
    });
}
/**
 * 等待用户在App 点击确认，点击确认后拿到 authToken 用于后端换取登录凭证
 */
export function scanQRLoginResult({ qrLoginToken, qrLoginSignature }: QRCodeInfo) {
    const config = getConfig();
    const url = '/rest/c/infra/ks/new/qr/acceptResult';
    return request<AcceptResult>(url, {
        qrLoginToken,
        qrLoginSignature,
        sid: config.sid,
    }).then((res) => {
        return getLoginToken(`/pass/${config.appName}/login/qrCodeCallback`, {
            authToken: res.serviceOwnToken,
            sid: config.sid,
        }).then((cbRes) => {
            // 这块的逻辑是给开放平台账号打通用的，这块透传serviceOwnToken
            cbRes.serviceOwnToken = res.serviceOwnToken;
            return cbRes;
        });
    });
}

export function scanQRLoginResultWeb({ qrLoginToken, qrLoginSignature }: QRCodeInfo) {
    const config = getConfig();
    const url = '/rest/c/infra/ks/new/qr/acceptResult';
    return request<AcceptResult>(url, {
        qrLoginToken,
        qrLoginSignature,
        sid: config.sid,
    }).then((res) => {
        return getLoginToken(`/pass/${config.appName}/web/login/qrCodeCallback`, {
            authToken: res.serviceOwnToken,
            sid: config.sid,
        }).then((cbRes) => {
            // 这块的逻辑是给开放平台账号打通用的，这块透传serviceOwnToken
            cbRes.serviceOwnToken = res.serviceOwnToken;
            return cbRes;
        });
    });
}
/**
 * 取消扫码登录
 */
export function cancelQrLogin() {
    // 因为扫码登录一定是串行的逻辑
    cancelRequest('/rest/c/infra/ks/new/qr/scanResult');
}

export interface scanTemplateResult {
    qrLoginToken: string;
    result: number;
    expireTime: number;
}
export function scanTemplate({qrToken}: {qrToken: string}) {
    const config = getConfig();
    const url = `/pass/${config.appName}/login/qrcode/scanTemplate`;
    return request<scanTemplateResult>(url, {
        qrToken,
    }).then((res) => {
        return res;
    });
}

export function scanTemplateWeb({qrToken}: {qrToken: string}) {
    const config = getConfig();
    const url = `/pass/${config.appName}/web/login/qrcode/scanTemplate`;
    return request<scanTemplateResult>(url, {
        qrToken,
    }).then((res) => {
        return res;
    });
}

export function acceptTemplate({qrToken}: {qrToken: string}) {
    const config = getConfig();
    const url = `/pass/${config.appName}/login/qrcode/acceptTemplate`;
    return request<{result: number; error_msg?: string}>(url, {
        qrToken,
    }).then((res) => {
        return res;
    });
}

export function acceptTemplateWeb({qrToken}: {qrToken: string}) {
    const config = getConfig();
    const url = `/pass/${config.appName}/web/login/qrcode/acceptTemplate`;
    return request<{result: number; error_msg?: string}>(url, {
        qrToken,
    }).then((res) => {
        return res;
    });
}

export function cancelTemplate({qrToken}: {qrToken: string}) {
    const config = getConfig();
    const url = `/pass/${config.appName}/login/qrcode/cancelTemplate`;
    return request<{result: number; error_msg?: string}>(url, {
        qrToken,
    }).then((res) => {
        return res;
    });
}

export function cancelTemplateWeb({qrToken}: {qrToken: string}) {
    const config = getConfig();
    const url = `/pass/${config.appName}/web/login/qrcode/cancelTemplate`;
    return request<{result: number; error_msg?: string}>(url, {
        qrToken,
    }).then((res) => {
        return res;
    });
}
