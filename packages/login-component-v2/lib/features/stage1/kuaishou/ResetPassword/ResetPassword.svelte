<script lang="ts">
    import Input from '$lib/shared/forms/input.svelte';
    import SeePassword from "$lib/utils/SeePassword.svelte";
    import HidePassword from "$lib/utils/HidePassword.svelte";
    import { resetPasswordByEmailWithoutLogin, resetPasswordByPhoneWithoutLogin, requestMobileCode, requestEmailCode, getKsAccountsWithoutLogin } from "@ks/sso";
    import Button from "$lib/shared/button/index.svelte";
    import { createEventDispatcher, getContext, onMount } from "svelte";
    import { CODE_MULTICHOOSE, EnumErrorType, EnumLoginType, EnumPageState } from "$lib/shared/const";
    import { createRegisterStore } from "$lib/store/registerStore";
    import { filterInput, isValidEmail, isValidPhone } from "$lib/utils/validate";
    import DropDown from "$lib/shared/dropdown/DropDown.svelte";
    import CloseButton from "$lib/utils/CloseButton.svelte";
    import { resetMultiUserInfo } from "$lib/features/stage1/kuaishou/ResetPassword/store";
    import ChooseReset from "$lib/features/stage1/kuaishou/ResetPassword/ChooseReset.svelte";

    const dispatch = createEventDispatcher();

    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    const onLoginStart = getContext('onLoginStart');

    const configStore = getContext('loginPageConfig');
    let passwordLoginTypes = $configStore.cAccountConfig.passwordLoginTypes;
    let inputClass = $configStore.cAccountConfig?.inputClass || '';
    const store = createRegisterStore(passwordLoginTypes);

    let currentState = 'reset'

    let showPassword = false;
    let codeClass = '';
    let loginErrorMsg = '';
    let buttonText = '重置';

    $: {
        if (isValidEmail($store.account) || ($store.account?.length <= 11 && $store.account?.length >= 8)) {
            codeClass = 'activate';
        } else {
            codeClass = '';
        }
    }

    let sendCodeClicked = false;
    async function sendCodeAction() {
        console.log('sendCodeAction');
        if (!store.reducers.validateSend()) {
            return;
        }
        if ($store.countDown > 0) {
            return;
        }
        try {
            if (isValidPhone($store.account)) {
                await requestMobileCode({
                    phone: $store.account,
                    countryCode: '+86',
                    type: 1485,
                });
            } else {
                await requestEmailCode({
                    email: $store.account,
                    type: 19,
                })
            }
            sendCodeClicked = true;
            store.setValues({
                countDown: 60,
            });
            const interal = setInterval(() => {
                if ($store.countDown <= 0) {
                    clearInterval(interal);
                    return;
                }
                store.setValues({
                    countDown: $store.countDown - 1,
                });
            }, 1000);
        } catch (error) {
            console.log('error', error);
            loginErrorMsg = error.error_msg || '网络错误，请稍后重试';
            onFail({
                loginType: EnumErrorType.SendCodeError,
                error,
            });
        }
    }

    function backToForm() {
        dispatch('updatePageState', EnumPageState.form);
    }


    let buttondisabled = false;

    async function onEmailReset() {
        try {
            onLoginStart({
                name: EnumLoginType.ResetPasswordC,
            });
            buttondisabled = true;
            const res = await resetPasswordByEmailWithoutLogin({
                email: $store.account,
                emailCode: $store.smsCode,
                password: $store.password1,
            });
            buttondisabled = false;
            buttonText = '重置成功';
            onSuccess({
                loginType: EnumLoginType.ResetPasswordC,
                result: res,
            });
        } catch (error) {
            buttondisabled = false;
            loginErrorMsg = error?.error_msg || '网络错误，请稍后重试';
            onFail({
                loginType: EnumLoginType.ResetPasswordC,
                error,
            });
        }
    }

    async function onPhoneReset() {
        onLoginStart({
            name: EnumLoginType.ResetPasswordC,
        });
        buttondisabled = true;
        try {
            await getKsAccountsWithoutLogin({
                phone: $store.account,
                countryCode: '+86',
                smsCode: $store.smsCode,
            });
        } catch (error) {
            buttondisabled = false;
            if (error?.result === CODE_MULTICHOOSE) {
                // 展示多选
                console.log('error', error);
                resetMultiUserInfo.set({
                    userInfos: error.userInfos,
                    multiUserToken: error.multiUserToken,
                    phone: $store.account,
                    smsCode: $store.smsCode,
                    password: $store.password1,
                });
                currentState = 'choose';
                return;
            }
            loginErrorMsg = error?.error_msg || '网络错误，请稍后重试';
            onFail({
                loginType: EnumLoginType.ResetPasswordC,
                error,
            });
            return;
        }
        try {
            const res = await resetPasswordByPhoneWithoutLogin({
                phone: $store.account,
                smsCode: $store.smsCode,
                password: $store.password1,
                targetUserId: 0,
                multiUserToken: '',
            });
            buttondisabled = false;
            buttonText = '重置成功';
            onSuccess({
                loginType: EnumLoginType.ResetPasswordC,
                result: res,
            });
        } catch (error) {
            buttondisabled = false;
            loginErrorMsg = error?.error_msg || '网络错误，请稍后重试';
            onFail({
                loginType: EnumLoginType.ResetPasswordC,
                error,
            });
        }
    }

    async function onReset() {
        if (!store.reducers.validateLogin()) {
            return;
        }
        if (isValidPhone($store.account)) {
            onPhoneReset();
        } else {
            onEmailReset();
        }
    }

    let container;
    let containerHeight = 0;
    onMount(() => {
        const rect = container.getBoundingClientRect();
        containerHeight = rect?.height;
    });
</script>

{#if currentState === 'reset'}
    <div class="pt-[48px] px-[48px] pb-[32px]"
         bind:this={container}
    >
        <div class="c-reset-title">快手APP账号重置密码</div>
        <div class="mt-[24px] relative">
            <Input placeholder={$store.placeholder}
                   bind:value={$store.account}
                   on:input={store.reducers.updateInputValue}
                   on:blur={store.reducers.onAccountBlur}
                   on:keydown={store.reducers.handleKeydown}
                   bind:ref={$store.emailInput}
                   errorMsg={$store.accountErrorMsg}
                   class={inputClass}
            >
                <slot slot="right">
                    {#if $store.account}
                        <CloseButton on:click={store.reducers.clearInputValue}/>
                    {/if}
                </slot>
            </Input><DropDown
                class="absolute z-30 w-full currentC"
                visible="{$store.emailDropdownVisible}"
                tooltips="请选择邮箱类型"
                current="{$store.emailHighlightIndex}"
                options="{$store.emailDropdownList}"
                onSelect="{store.reducers.handleEmailSelect}"
        />
        </div>
        <div class="mt-[24px]">
            <Input placeholder="请输入新密码"
                   bind:value={$store.password1}
                   type={showPassword ? 'text': 'password'}
                   errorMsg={$store.password1ErrorMsg}
                   maxlength={20}
                   on:input={filterInput}
                   on:blur={store.reducers.onPassword1Blur}
                   class={inputClass}
            >
                <slot slot="right">
                    {#if showPassword}
                        <SeePassword on:click={() => (showPassword = !showPassword)}/>
                    {:else}
                        <HidePassword on:click={() => (showPassword = !showPassword)}/>
                    {/if}
                </slot>
            </Input>
        </div>
        <div class="mt-[24px]">
            <Input placeholder="确认新密码"
                   bind:value={$store.password2}
                   type={showPassword ? 'text': 'password'}
                   errorMsg={$store.password2ErrorMsg}
                   maxlength={20}
                   on:input={filterInput}
                   on:blur={store.reducers.onPassword2Blur}
                   on:focus={() => {
                        store.setValues({
                            password2ErrorMsg: '',
                        })
                   }}
                   class={inputClass}
            >
            </Input>
        </div>
        <div class="mt-[24px]">
            <Input placeholder="请输入验证码"
                   bind:value={$store.smsCode}
                   maxlength={6}
                   errorMsg={$store.smsCodeErrorMsg}
                   on:input={store.reducers.filterSmsCode}
                   on:blur={store.reducers.onSmsCodeBlur}
                   globalErrorMsg={loginErrorMsg}
                   class={inputClass}
            >
                <slot slot="right">
                    <div class="hover:cursor-pointer send-code font-[400] text-[14px] leading-[22px] currentC"
                         on:click={sendCodeAction}
                    >
                        {#if $store.countDown}
                            <span class="text-[#C6C6C6ff]">{$store.countDown}s</span>
                        {:else}
                            <span class={codeClass}>
                                {#if sendCodeClicked}
                                    重新发送
                                {:else}
                                    发送验证码
                                {/if}
                            </span>
                        {/if}
                    </div>
                </slot>
            </Input>
        </div>
        <Button class="currentC mt-[44px]"
                on:click={onReset}
                disabled={buttondisabled}
        >
            {buttonText}
        </Button>
        <div class="vice-operate">
            已有账号？<span on:click={backToForm}>直接登录</span>
        </div>
    </div>
{:else}
    <ChooseReset
            height={containerHeight}
            on:updatePageState={() => (currentState = 'reset')}
    />
{/if}

<style>
    .currentC {
        color: var(--colorC);
    }
    .c-reset-title {
        color: #222222;
        font-size: 24px;
        font-weight: 600;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 32px;
        text-align: left;
        vertical-align: top;
    }
    .vice-operate {
        color: #9C9C9C;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        vertical-align: top;
        margin-top: 8px;
    }
    .vice-operate span {
        color: var(--colorC);
        cursor: pointer;
    }
    .send-code {
        color: #C6C6C6ff;
    }
    .activate {
        color: var(--colorC);
    }
</style>
