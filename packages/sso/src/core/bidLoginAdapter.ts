import { AuthTokenResult, QuickLoginResult } from '../types/services';
import { privateGetConfig } from './configInit';
import { identityVerificationInterceptor } from '@/core/verificationInterceptor';
import { generateCallback } from '@/utils/utils';

interface bidQuickLoginOptions {
    // 快手登录接口返回的 authToken
    quickLoginToken: string;
    isWeb?: boolean;
    // 是否自动注册
    registerOpen?: boolean;
    // 登录后跳转
    callBack?: string;
}

export async function bidQuickLogin({ quickLoginToken, registerOpen, callBack, isWeb=true }: bidQuickLoginOptions): Promise<QuickLoginResult> {
    const config = privateGetConfig();
    // console.log('bidQuickLogin 调用', isWeb, registerOpen, callBack);
    const url = config.baseUrl + '/pass/bid/web/sns/quickLoginByKsAuth';
    return identityVerificationInterceptor<QuickLoginResult>(url, {
        sid: config.sid,
        quickLoginToken,
        registerOpen,
        callBack,
        isWeb,
    });
}

export async function wrapLoginRequest(loginRes:  Promise<AuthTokenResult>): Promise<AuthTokenResult | QuickLoginResult> {
    const config = privateGetConfig();
    if (!config.kuaishouAuth || config.disableQuickLogin) {
        return loginRes;
    }

    const res = await loginRes;
    const authToken = res[`${config.sid}.at`];


    return bidQuickLogin({
        quickLoginToken: authToken!,
        registerOpen: true,
    }).then(res => {
        const currentSid = config.sid;
        const {
            [currentSid + '.at']: authToken,
        } = res;
        return {
            ...res,
            callback: generateCallback(config.callback, {
                authToken,
                sid: currentSid,
            }),
        }
    });
}
