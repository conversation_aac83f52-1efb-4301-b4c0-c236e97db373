<template>
    <div class="home">
        <div class="authorize-icon">
            <div class="ks-logo-box">
                <div
                    class="ks-logo-box-icon"
                    :style="{
                        backgroundImage: `url(${iconText.data.fromIcon})`,
                    }"
                ></div>
                <div class="ks-logo-box-text">
                    {{ iconText.data.fromName }}
                </div>
            </div>
            <div class="convert-icon"></div>
            <div class="from-box">
                <div
                    class="from-box-icon"
                    :style="{
                        backgroundImage: `url(${iconText.data.targetIcon})`,
                    }"
                ></div>
                <div class="from-box-text">
                    {{ iconText.data.targetName }}
                </div>
            </div>
        </div>
        <div class="info-box">
            <div class="user-info">
                <div
                    class="user-info-avator"
                    :style="{
                        backgroundImage: `url(${iconText.data.headUrl})`,
                    }"
                ></div>
                <div class="user-info-name">
                    {{ iconText.data.nickName }}
                </div>
            </div>
            <div class="authorize-info">
                <div class="authorize-info-title">
                    登录后应用将获得以下权限
                </div>
                <div class="authorize-info-list">
                    {{ iconText.data.text }}
                </div>
            </div>
        </div>
        <div class="authorizeAction" @click="authorizeAction">
            <span>确认登录</span>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive } from '@vue/composition-api';
import { Toast } from '@ks/sharp-ui';
import ksBridge from '@ks/ks-bridge';
import ksLog from '@/utils/kslog';
import webLog from '@/utils/weblog';
import { TYPE } from './fallback.vue';
import { getIconText, convertToken } from '@/api/authorize';
import { useSendFmp } from '@/common/hooks/useSendFmp';
import { parseUrl } from '@/utils/url';

const query = parseUrl(location.href).paramObj;
const followUrl = query.followUrl || '';
const targetSid = 'kuaishou.recharge.web'; // (站外)快币充值支付业务的sid标志
const fromSid = 'kuaishou.midground.api'; // 主站的sid标志，对应很多业务sid，在通过上面的一起来决定
// const fromSid = 'kuaishou.shop.im'; // 主站的sid标志，对应很多业务sid，在通过上面的一起来决定

/**
 * 把任意类型的错误转换成 JSON 对象
 */
export function error2JSON(e: any) {
	if (!e) return e; // 如果没有错误, 直接返回
	try {
		// 如果 e 是 Error 类型, 就直接用它, 否则用 e 创建一个新的 Error 对象
		const err = e instanceof Error ? e : new Error(e);
		// 返回一个包含错误属性的 JSON 对象
		return {
			name: String(err?.name),
			message: String(err?.message || '不存在 message'),
			stack: String(err?.stack ?? '不存在stack'),
			origin: e,
		};
	} catch {
		// 如果转换过程出现异常, 返回一个默认的 JSON 对象
		return {
			name: 'Error',
			message: String(e),
			stack: '转换过程出现异常',
			origin: e,
		};
	}
}

function getSid() {
    return new Promise(resolve => {
        ksBridge.isSupportAsync({
            api: 'getSidToken',
            complete: (isSupport: boolean) => {
                setTimeout(() => {
                    if (isSupport) {
                        ksBridge.getSidToken({
                            sid: fromSid,
                            forceRefresh: true,
                            success: (ret: any) => {
                                setTimeout(() => {
                                    resolve(ret);
                                }, 0);
                            },
                            fail: (err: any) => {
                                setTimeout(() => {
	                                webLog.plugins.radar.event({
		                                name: 'getSidToken fail',
		                                extra_info: JSON.stringify(error2JSON(err)),
	                                });
                                    resolve(err);
                                }, 0);
                            },
                        });
                    } else {
                        resolve(false);
                    }
                }, 0);
            },
        });
    });
}
export default defineComponent({
    setup(props: any, { root }: { root: any }) {
        useSendFmp();
        const { $router } = root;
        const api_st = ref('');
        const iconText = reactive({
            data: {},
        });

        async function authorizeAction() {
            try {
                webLog.sendImmediately('CLICK', {
                    action: 'LOGIN_BUTTON',
                    params: {},
                });
                const res: any = await convertToken({
                    sid: targetSid,
                    'kuaishou.midground.api_st': api_st.value,
                    followUrl,
                });
                ksBridge.isSupportAsync({
                    api: 'openBrowser',
                    complete: (isSupport: Boolean) => {
                        setTimeout(() => {
                            if (isSupport) {
                                ksBridge.openBrowser({
                                    url: res.loginUrl,
                                    success: (ret: any) => {},
                                    fail: () => {},
                                });
                                ksBridge.exitWebView();
                            } else {
                                Toast.error('请更新最新版快手App');
                            }
                        }, 0);
                    },
                });
            } catch (e) {
                ksLog(e.error_msg || e.errorMsg);
                Toast.error(e.error_msg || e.errorMsg);
            }
        }

        async function getIconTextFn() {
            try {
                return await getIconText({
                    fromSid,
                    sid: targetSid,
                    'kuaishou.midground.api_st': api_st.value,
                });
            } catch (e) {
                // 未登录
                if (e.result === 109) {
                    ksBridge.login({
                        checkFromServer: false,
                        success: (ret: any) => {
                            if (ret.result === 1) {
                                location.reload();
                            }
                        },
                        fail: () => {
                            Toast.error('登录失败，请重试');
                            setTimeout(() => {
                                ksBridge.exitWebView();
                            }, 1000);
                        },
                    });
                } else {
                    ksLog(e.error_msg || e.errorMsg);
                    Toast.error(e.error_msg || e.errorMsg);
                    $router.push({
                        name: 'fallback',
                        query: {
                            type: TYPE.networkError,
                        },
                    });
                }
                return {};
            }
        }
        webLog.sendImmediately('PV', {
            type: 'enter',
            page: 'DEPOSIT_LOGIN_PAGE',
            params: {
                followUrl,
            },
        });
        async function init() {
            const tokenRes: any = await getSid();
            if (!tokenRes) {
                $router.push({
                    name: 'fallback',
                    query: {
                        type: TYPE.networkError,
                    },
                });
                return;
            }
            api_st.value = tokenRes.token;
            iconText.data = await getIconTextFn();
        }
        init();
        // expose to template
        return {
            api_st,
            iconText,
            authorizeAction,
        };
    },
});
</script>

<style lang="less">
.dark {
    .home {
        background-color: #19191e;
    }
    .user-info-name,
    .authorize-info-title {
        color: #e6e6e6;
    }
    .authorize-info-list {
        color: #b5b5b6;
    }
    .from-box-text,
    .ks-logo-box-text {
        color: #b5b5b6;
    }
}
.info-box {
}
.user-info {
    margin-top: 0.72rem;
    margin-bottom: 0.56rem;
    display: flex;
    align-items: center;
    &-avator {
        width: 0.72rem;
        height: 0.72rem;
        background-size: contain;
        background-color: #f8f8f8;
        background-repeat: no-repeat;
        margin-right: 0.24rem;
        border-radius: 50%;
    }
    &-name {
        font-size: 0.28rem;
        font-weight: 400;
        color: #222222;
    }
}
.authorize-info {
    &-title {
        font-size: 0.32rem;
        font-weight: 500;
        color: #222222;
        margin-bottom: 0.16rem;
        line-height: 0.45rem;
    }
    &-list {
        font-size: 0.28rem;
        font-weight: 400;
        color: #666666;
        margin-left: 0.16rem;
        line-height: 0.4rem;
        position: relative;
        &:after {
            content: '';
            position: absolute;
            width: 0.08rem;
            height: 0.08rem;
            border-radius: 50%;
            background-color: #666666;
            top: 50%;
            transform: translateY(-50%);
            left: -0.16rem;
        }
    }
    &-list:not(:last-child) {
        margin-bottom: 0.16rem;
    }
}
.authorizeAction {
    margin-top: 0.56rem;
    width: 6.38rem;
    height: 0.88rem;
    background: #ff5000;
    border-radius: 0.44rem;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
        font-size: 0.32rem;
        font-weight: 500;
        color: #ffffff;
    }
}
.home {
    min-height: 100vh;
    box-sizing: border-box;
    padding: 0 0.54rem;
}
.authorize-icon {
    padding-bottom: 1.57rem;
    padding-top: 1.24rem;
    display: flex;
    justify-content: center;
    border-bottom: 0.01rem solid #eaeaea;
}
.convert-icon {
    margin-top: 0.45rem;
    width: 0.73rem;
    height: 0.38rem;
    background-size: contain;
    background-image: url(../../assets/authorize.png);
    background-repeat: no-repeat;
    margin-left: 0.56rem;
    margin-right: 0.6rem;
}
.ks-logo-box,
.from-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    &-icon {
        width: 1.28rem;
        height: 1.28rem;
        background-size: contain;
        background-repeat: no-repeat;
        background-color: #f8f8f8;
    }
    &-text {
        position: absolute;
        bottom: -0.6rem;
        font-size: 0.32rem;
        font-weight: 400;
        color: #222222;
        line-height: 0.45rem;
        white-space: nowrap;
    }
}
</style>
