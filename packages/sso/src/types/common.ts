export type Env = 'production' | 'staging';

export interface SSOConfig {
    env: Env;
    sid: string;
    // 快手中心账号域名 默认为 id.kuaihsou.com
    baseUrl: string;
    callback: string;
    useKsCaptcha?: boolean;
    // 业务账号中心域名 比如业务域名为A.com 那么业务账号中心域名一般为 id.A.com
    bizIdHost?: string;
    // 登出接口所在的host
    logoutHost?: string;
    // 登录成功之后的跳转页面
    followUrl?: string;
    // 未登录时的登录页面
    loginUrl?: string;
    // 在使用业务账号的时候是否自动同步登录态到中心账号域名
    syncToCenter?: boolean;
    // 新版验证码参数 如果传入这几个参数那么用新版的验证码接口逻辑
    qrType?: string;
    kpn?: string;
    serviceOwnParams?: any;
    channelType?: string;
    platform?: 'PC' | 'H5' | 'OUTSIDE_H5';
    collectConfig?: ICollectConfig;
    kuaishouAuth?: boolean;
    // C端授权页不能调 quickLogin，需要跳回组件才能调
    disableQuickLogin?: boolean;
    // 是否开启sig4加签
    enableSig4?: boolean;
    // 业务自定义 request header
    customRequestHeader?: Record<string, any>;
}


export interface ICollectConfig { // 风控采集参数
    sensor?: boolean; // 启用传感器数据采集, 默认false
    trace?: boolean; // 启用用户行为跟踪，默认false
    trojanScan?: boolean; // 启用木马扫描，默认false
    trojanApi?: boolean; // 上报安全组扫描设备
}
