# PC 端统一登录页

* [产品文档](https://wiki.corp.kuaishou.com/pages/viewpage.action?pageId=827833704)
* [UI](https://dp.corp.kuaishou.com/app/Wq8daKXfvm/design)
* [埋点文档](https://wiki.corp.kuaishou.com/pages/viewpage.action?pageId=*********)
* [TEAM](https://team.corp.kuaishou.com/task/T839604)

## 使用方式

- 方式一：详见[PC/H5 统一登录页](https://docs.corp.kuaishou.com/k/home/<USER>/fcACa6JjsRl46NZI0uhTKhITF)
- 方式二：业务方跳转到该页面，携带 redirectURL 参数

## 后端配置

- [各 sid 登录时发下的 stsUrl 配置](https://kconf.corp.kuaishou.com/#/infraService/passportKuaishouWebApi/stsUrls)
- [各 sid 登录时发下的 followUrl 配置](https://kconf.corp.kuaishou.com/#/infraService/passportKuaishouWebApi/followUrls)

根据线上历史数据：

* 绝对多数 sid，跳转统一登录页时，都传了 callback 参数，且 callback 参数里有 followUrl 参数

优先级从高到底：

1. /pc/account/login 页面参数上的 redirectURL（有效的话）
2. 登录接口返回的 followUrl（存在的话）
3. /pc/account/login 页面参数上的 callback 参数里的 followUrl 参数

温馨提示：这里的历史包袱比较重，不明白的可以找帐号中台后端 @江之鉴 @刘志健 咨询。

## 后端文档

- [新业务如何接入快手统一登录页](https://docs.corp.kuaishou.com/d/home/<USER>
`stsUrl` 不再由前端处理，账号后端统一处理
