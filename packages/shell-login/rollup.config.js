import { nodeResolve } from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import typescript from 'rollup-plugin-typescript2';
import { terser } from 'rollup-plugin-terser';

const commonPlugins = [
    nodeResolve(),
    commonjs(),
    typescript({
        clean: true,
        tsconfig: 'tsconfig.json',
        tsconfigOverride: {
            compilerOptions: {
                target: 'es5'
            }
        },
        rollupCommonJSResolveHack: false
    }),
    terser(),
];

export default [
    {
        input: 'src/index.ts',
        output: [{
            format: 'es',
            sourcemap: true,
            dir: 'dist/',
            entryFileNames: '[name].esm.js',
            exports: 'named',
        }, {
            format: 'cjs',
            sourcemap: true,
            dir: 'dist/',
            entryFileNames: '[name].js',
            exports: 'named',
        }],
        plugins: [
            ...commonPlugins,
        ],
        external: [
            '@ks/sso',
        ],
    },
];
