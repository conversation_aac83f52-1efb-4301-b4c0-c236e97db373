<script lang="ts">
    import { twMerge } from 'tailwind-merge';
    import ToolbarButton from '$lib/shared/toolbar/ToolbarButton.svelte';
    import { getContext } from "svelte";
    import Tooltip from "$lib/shared/tooltip/Tooltip.svelte";

    const themeColor = getContext('themeColor');
    const configStore = getContext('loginPageConfig');
    let defaultColor = '#C6C6C6';
    export let name: string = 'Qrcode';
    export let customColor = defaultColor;
    export let toolTip = '';
    function onHover() {
        // console.log('$p', $$props.class);
        if ($$props.class.includes('currentB')) {
            customColor = themeColor?.colorB || defaultColor;
        } else {
            customColor = themeColor?.colorC || defaultColor;
        }
    }
    function onOut() {
        customColor = defaultColor;
    }
</script>

<ToolbarButton on:click
               {name}
               {...$$restProps}
               class={twMerge('ms-auto', $$props.class)}
               let:svgSize
>
    {#if $configStore?.iconStyle === 1}
        <svg width="58" height="72" viewBox="0 0 58 72" fill="none" xmlns="http://www.w3.org/2000/svg"
             on:mouseover={onHover}
             on:mouseout={onOut}
        >
            <mask id="mask0_2261_45635" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="-14" y="0" width="72" height="72">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M-14 0H46C52.6274 0 58 5.37258 58 12V72L-14 0Z" fill="white"/>
            </mask>
            <g mask="url(#mask0_2261_45635)">
                <rect x="1" y="14" width="13" height="13" rx="3" stroke={customColor} stroke-width="2"/>
                <rect x="31" y="14" width="13" height="13" rx="3" stroke={customColor} stroke-width="2"/>
                <rect x="31" y="44" width="13" height="13" rx="3" stroke={customColor} stroke-width="2"/>
                <mask id="path-5-inside-1_2261_45635" fill="white">
                    <rect y="43" width="15" height="15" rx="1"/>
                </mask>
                <rect y="43" width="15" height="15" rx="1" stroke={customColor} stroke-width="4" mask="url(#path-5-inside-1_2261_45635)"/>
                <path d="M44 31V40" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
                <path d="M40 31H33C31.8954 31 31 31.8954 31 33V38C31 39.1046 30.1046 40 29 40H18" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
                <path d="M18 44V51C18 51.5523 18.4477 52 19 52H23.5" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
                <path d="M8.6 31H13C13.5523 31 14 31.4477 14 32V39C14 39.5523 13.5523 40 13 40H8" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
                <rect x="35" y="48" width="5" height="5" rx="1" fill={customColor}/>
                <rect x="35" y="18" width="5" height="5" rx="1" fill={customColor}/>
                <rect x="5" y="18" width="5" height="5" rx="1" fill={customColor}/>
                <rect x="5" y="48" width="5" height="5" rx="1" fill={customColor}/>
                <path d="M40 35V38C40 39.1046 39.1046 40 38 40H35" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
                <path d="M18 18V16C18 14.8954 18.8954 14 20 14H25C26.1046 14 27 14.8954 27 16V18" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
                <path d="M27 22V32C27 33.1046 26.1046 34 25 34H18" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
                <path d="M18 24H22" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
                <path d="M22 44H26C26.5523 44 27 44.4477 27 45V56C27 56.5523 26.5523 57 26 57H22" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
                <path d="M23 19V30" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
            </g>
        </svg>

    {:else}
        <svg width="59" height="72" viewBox="0 0 59 72" fill="none" xmlns="http://www.w3.org/2000/svg"
             on:mouseover={onHover}
             on:mouseout={onOut}
        >
            <mask id="mask0_2261_45772" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="-13" y="0" width="72" height="72">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M-13 0H47C53.6274 0 59 5.37258 59 12V72L-13 0Z" fill="white"/>
            </mask>
            <g mask="url(#mask0_2261_45772)">
                <mask id="path-3-inside-1_2261_45772" fill="white">
                    <rect x="1" y="13" width="15" height="15" rx="1"/>
                </mask>
                <rect x="1" y="13" width="15" height="15" rx="1" stroke={customColor} stroke-width="4" mask="url(#path-3-inside-1_2261_45772)"/>
                <mask id="path-4-inside-2_2261_45772" fill="white">
                    <rect x="31" y="13" width="15" height="15" rx="1"/>
                </mask>
                <rect x="31" y="13" width="15" height="15" rx="1" stroke={customColor} stroke-width="4" mask="url(#path-4-inside-2_2261_45772)"/>
                <mask id="path-5-inside-3_2261_45772" fill="white">
                    <rect x="31" y="43" width="15" height="15" rx="1"/>
                </mask>
                <rect x="31" y="43" width="15" height="15" rx="1" stroke={customColor} stroke-width="4" mask="url(#path-5-inside-3_2261_45772)"/>
                <mask id="path-6-inside-4_2261_45772" fill="white">
                    <rect x="1" y="43" width="15" height="15" rx="1"/>
                </mask>
                <rect x="1" y="43" width="15" height="15" rx="1" stroke={customColor} stroke-width="4" mask="url(#path-6-inside-4_2261_45772)"/>
                <path d="M45 31V40" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
                <path d="M41 31H32.2C32.0895 31 32 31.0895 32 31.2V39.8C32 39.9105 31.9105 40 31.8 40H19" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
                <path d="M19 44V51C19 51.5523 19.4477 52 20 52H24.5" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
                <path d="M9.6 31H14C14.5523 31 15 31.4477 15 32V39C15 39.5523 14.5523 40 14 40H9" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
                <rect x="36" y="48" width="5" height="5" rx="1" fill={customColor}/>
                <rect x="36" y="18" width="5" height="5" rx="1" fill={customColor}/>
                <rect x="6" y="18" width="5" height="5" rx="1" fill={customColor}/>
                <rect x="6" y="48" width="5" height="5" rx="1" fill={customColor}/>
                <path d="M41 35V39.8C41 39.9105 40.9105 40 40.8 40H36" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
                <path d="M19 18V14.2C19 14.0895 19.0895 14 19.2 14H27.8C27.9105 14 28 14.0895 28 14.2V18" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
                <path d="M28 22V33.8C28 33.9105 27.9105 34 27.8 34H19" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
                <path d="M19 24H23" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
                <path d="M23 44H27C27.5523 44 28 44.4477 28 45V56C28 56.5523 27.5523 57 27 57H23" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
                <path d="M24 19V30" stroke={customColor} stroke-width="2" stroke-linecap="round"/>
            </g>
        </svg>
    {/if}
    {#if toolTip}
        <Tooltip placement="left">{toolTip}</Tooltip>
    {/if}
</ToolbarButton>

<style>
    svg:focus {
        outline: none;
    }
</style>
