import typescript from 'rollup-plugin-typescript2';
import resolve from 'rollup-plugin-node-resolve';
import commonjs from 'rollup-plugin-commonjs';
import {terser} from 'rollup-plugin-terser';
import json from '@rollup/plugin-json';
import pkgJson from './package.json';
// import visualizer from 'rollup-plugin-visualizer';

export default {
    input: 'src/index.ts',
    output: [
        {
            file: 'dist/index.esm.js',
            format: 'es',
            name: '@ks/sso-logger',
            sourcemap: true,
        },
        {
            file: 'dist/index.js',
            format: 'cjs',
            name: '@ks/sso-logger',
            sourcemap: true,
        },
        {
            file: 'dist/index.umd.js',
            format: 'umd',
            name: '@ks/sso-logger',
            sourcemap: true,
        },
        {
            file: `dist/publish/ks-sso-logger@${pkgJson.version}.umd.js`,
            format: 'umd',
            name: '@ks/sso-logger',
            sourcemap: false,
        },
    ],
    plugins: [
        json(),
        resolve({browser: true}),
        commonjs(),
        typescript({
            clean: true,
            tsconfig: `tsconfig.json`,
            tsconfigOverride: {
                compilerOptions: {
                    target: 'es5'
                }
            },
            rollupCommonJSResolveHack: false
        }),
        terser(),
        // visualizer(),
    ]
};
