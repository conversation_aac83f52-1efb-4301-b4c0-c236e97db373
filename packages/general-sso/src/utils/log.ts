import { IS_PROD } from '@/config';

interface LogFunction {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (name: string, ...params: any[]): void;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    // sendTag: (...params: any[]) => void;
}

// 从 d3 扒的 颜色列表
const colorList = [
    '#1f77b4',
    '#ff7f0e',
    '#2ca02c',
    '#d62728',
    '#9467bd',
    '#8c564b',
    '#e377c2',
    '#7f7f7f',
    '#bcbd22',
    '#17becf',
];

let _colorIndex = 0;

/**
 * 从 colorList 2qu一个
 */
function getColor() {
    return colorList[_colorIndex++ % colorList.length];
}

export function createDevLog(prefix: string): LogFunction {
    let log!: LogFunction;
    if (IS_PROD) {
        log = function () {}; // eslint-disable-line @typescript-eslint/no-empty-function
    } else {
        const color = getColor();

        log = function (
            name: string,
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            ...params: any[]
        ) {
            console.info(`%c ${prefix}`, `color:${color};font-weight: bold`, name, ...params);
        };
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    // log.sendTag = function (...params: any[]) {
    //     sendTag(prefix, ...params);
    // }
    return log;
}
