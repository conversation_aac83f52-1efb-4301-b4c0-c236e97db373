import axios from 'axios';
import { getConfig, Config } from '@/env';
import sig4Adapter from "@/sig4";
import { Jose } from '@ks-cqc/h5-sig4-lite-obf';
import { addAdapter, adapter } from "@/gundamRequest";

const config = getConfig();
/**
 * 超时时间
 */
export const TIMEOUT = 5000;

/**
 * 长连接超时时间
 */
const LONG_PING_TIMEOUT = 600000;

/**
 * 二维码相关接口
 */
const client = axios.create({
    baseURL: `${config.API.ID_HOST}/rest/c/infra/ks/new/qr`,
    timeout: TIMEOUT,
    withCredentials: true,
    headers: {
        'content-type': 'application/x-www-form-urlencoded'
    },
    adapter,
});
addAdapter(sig4Adapter(Jose));


function setHost(host: string) {
    console.info('set api host', host);
    client.defaults.baseURL = `${host}/rest/c/infra/ks/new/qr`;
}

export function setHostFromConfig(config: Config) {
    setHost(config.API.ID_HOST);
}

export type AjaxResult<T> = {
    result: number;
} & T

export enum  ENUM_RESULT_CODE {
    // 已过期
    TIME_OUT = 100400002,
    // 用户取消
    USER_CANCEL = 100400004
}

/**
 * 开始二维码流程
 * @param kpn
 * @param qrType
 * @param sid
 * @param serviceOwnParams
 */
export async function getQrcodeInfo({kpn, qrType = 'identity-type', sid, serviceOwnParams}: {
    kpn: string;
    sid: string;
    qrType: string;
    serviceOwnParams: string;
}): Promise<AjaxResult<{
    expireTime: number;
    imageData: string;
    next: string; // "scanResult"
    qrLoginSignature: string;
    qrLoginToken: string;
}>> {
    return client.post(
        '/start',
        `serviceOwnParams=${encodeURIComponent(serviceOwnParams)}&sid=${encodeURIComponent(sid)}`
        + `&qrType=${encodeURIComponent(qrType)}&kpn=${encodeURIComponent(kpn)}&isWebSig4=true`,
        {
            headers: { 'content-type': 'application/x-www-form-urlencoded' }
        },
    )
        .then(res => res.data);
}

export async function getScaned(signature: string, token: string): Promise<AjaxResult<{
    "next": string;
    "qrLoginSignature": string;
    "status": string;
}>> {
    return client.post(
        '/scanResult',
        `qrLoginSignature=${encodeURIComponent(signature)}&qrLoginToken=${encodeURIComponent(token)}&isWebSig4=true`,
        {
            headers: { 'content-type': 'application/x-www-form-urlencoded' },
            timeout: LONG_PING_TIMEOUT,
        }
    )
        .then(res => res.data);
}

/**
 * 获取授权结果
 * @example {
	"result": 1,
	"next": "succ",
	"serviceOwnToken": "",
	"qrLoginSignature": "",
	"status": "ACCEPTED",
	"qrAuthToken": "",
}
 * @param signature
 * @param token
 */
export async function getAcceptResult(signature: string, token: string): Promise<AjaxResult<{
    next: string;
    serviceOwnToken: string;
    qrLoginSignature: string;
    status: string;
    qrAuthToken: string;
}>> {
    return client.post(
        '/acceptResult',
        `qrLoginSignature=${encodeURIComponent(signature)}&qrLoginToken=${encodeURIComponent(token)}&isWebSig4=true`,
        {
            headers: { 'content-type': 'application/x-www-form-urlencoded' },
            timeout: LONG_PING_TIMEOUT,
        }
    )
        .then(res => res.data);
}
