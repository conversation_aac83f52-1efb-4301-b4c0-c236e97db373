import { SCENE_CODE } from '../config/enums';
import { genEncryptReqParams } from '@/utils/encrypt';
import { DEFAULT_COUNTRY_CODE } from '@/config/constants'
import { privateGetConfig } from '@/core/configInit';
import { AuthTokenResult } from '../types/services';
import { getLoginToken } from '../core/loginToken';

export interface RegisterInfo {
    countryCode: string;
    phone: string;
    password: string;
    smsCode: string | number;
    captchaToken?: string;
}

/**
 * 手机号 + 短信验证码 + 密码注册
 */
export function register({
    countryCode = DEFAULT_COUNTRY_CODE,
    phone,
    password,
    smsCode,
    captchaToken
}: RegisterInfo): Promise<AuthTokenResult> {
    const config = privateGetConfig();
    const encryptReqParams = genEncryptReqParams({
        phone,
        password,
    });
    return getLoginToken('/pass/kuaishou/register/mobile/v2', {
        sid: config.sid,
        type: SCENE_CODE.REGISTER,
        countryCode,
        ...encryptReqParams,
        smsCode,
        captchaToken,
    });
}
