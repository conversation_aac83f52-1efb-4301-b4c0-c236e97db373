/**
 * @file 嗅探可用url
 */
import { cached } from './common';

const timeoutPromise = (timeout = 5 * 1000) => {
    return new Promise((_, _reject) => {
        setTimeout(() => {
            _reject(new Error('timeout'));
        }, timeout);
    });
};
const promiseWithTimeout = <T>(promise: Promise<T>) => {
    return Promise.race([promise, timeoutPromise()]);
}

function *fetchGenerator(urls: string[]) {
    for(const url of urls) {
        const fetchPromise = fetch(url, { method: 'GET', });
        // 超时情况的处理
        const promise = promiseWithTimeout(fetchPromise);
        yield promise.then(() => url, () => '');
    }
}

export async function sniffUrl(urls: string[]) {
    for await (const url of fetchGenerator(urls)) {
        if(url) return url;
    }
    return '';
}

export const sniffUrlWithCached = cached(sniffUrl);
