<script lang="ts">
    import SMSForm from "$lib/features/stage1/business/SMSForm.svelte";
    import PasswordForm from "$lib/features/stage1/business/PasswordForm.svelte";
    import QrCodeB from "$lib/features/stage1/business/QrCode.svelte";
    import { TabItem, Tabs } from '$lib/shared/switch-tab';
    import IconQrCode from "$lib/utils/IconQrCode.svelte";
    import IconPc from "$lib/utils/IconPc.svelte";
    import { CODE_NEED_BIND, CODE_NEED_BIND_V2, EnumLoginType, EnumPageState } from "$lib/shared/const";
    import { createEventDispatcher, getContext, onMount } from 'svelte';
    import { bindPhoneInfo, multiUserInfo } from "$lib/features/stage1/kuaishou/store";
    import ResetPassword from "$lib/features/stage1/business/ResetPassword.svelte";
    import Register from "$lib/features/stage1/business/Register.svelte";
    import BindPhone from "$lib/features/stage1/kuaishou/bind/QuickBindPhone.svelte";
    import IconQuestion from "$lib/utils/IconQuestion.svelte";
    import Tooltip from "$lib/shared/tooltip/Tooltip.svelte";
    import { useURLSearch } from '$lib/utils/url';
    import { SNSLoginCode } from '@ks/sso';
    import { tabBusinessMainStore, tabBusinessSubStore } from "$lib/store/tabStore";

    const dispatch = createEventDispatcher();
    const loginPageConfig = getContext('loginPageConfig');
    const isMainAccountPage = getContext('isMainAccountPage');
    const getCurrentHeight = getContext('getCurrentHeight');
    const onLoginStart = getContext('onLoginStart');
    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    const currentStage = $loginPageConfig.currentMainState;


    function removeSpecificParams() {
        const url = new URL(window.location.href);

        // 删除目标参数
        ['loginType', 'code', 'state'].forEach(param => {
            url.searchParams.delete(param);
        });

        // 构建新URL（保留路径、其他参数、hash）
        const newURL = url.pathname + url.search + url.hash;

        // 替换URL
        window.history.replaceState({}, document.title, newURL || url.pathname);
    }

    const query = useURLSearch();
    onMount(async () => {
        try {
            if (query?.loginType) {
                onLoginStart({
                    name: EnumLoginType.KSAuthorize,
                });
                let code = query.code;
                removeSpecificParams();
                const r = await SNSLoginCode(code as string);
                onSuccess({
                    loginType: EnumLoginType.KSAuthorize,
                    result: r,
                });
            }
        } catch (e) {
            if ([CODE_NEED_BIND, CODE_NEED_BIND_V2].includes(e?.result)) {
                console.log('need bind phone', e);
                bindPhoneInfo.set({
                    phone: e.bindInfo.phone,
                    bindAuthToken: e.bindInfo.bindAuthToken,
                });
                pageState = EnumPageState.bindPhone;
                return;
            }
            onFail({
                loginType: EnumLoginType.KSAuthorize,
                result: e,
            })
        }
    })


    let pageState = EnumPageState.form;
    $: {
        if ($multiUserInfo.userInfos.length) {
            pageState = EnumPageState.chooseUser;
        }
        if (pageState === EnumPageState.register) {
            dispatch('updateAgreementType', 'register');
        } else {
            dispatch('updateAgreementType', 'login');
        }
        if ([EnumPageState.qrcode, EnumPageState.chooseUser].includes(pageState)) {
            dispatch('updateShowFooter', false);
        } else {
            dispatch('updateShowFooter', true);
        }
    }

    let loginMainMethod;
    let mainTitle = '';
    let mainTitleTooltip = '';
    let qrCodeToolTip = '';
    let pcToolTip = '';
    loginPageConfig.subscribe(value => {
        let config;
        if (isMainAccountPage) {
            config = value.bAccountConfig;
        } else {
            config = value.bSubAccountConfig;
        }
        loginMainMethod = config.loginMainMethod;
        mainTitle = config?.mainTitle;
        mainTitleTooltip = config?.mainTitleTooltip;
        qrCodeToolTip = config?.qrCodeToolTip;
        pcToolTip = config?.pcToolTip;
    });

    let qrCodeAbsoluteHeight = 0;
    function switchCurrentState() {
        qrCodeAbsoluteHeight = getCurrentHeight();
        pageState = pageState === EnumPageState.qrcode ? EnumPageState.form : EnumPageState.qrcode;
        dispatch('toggleShowFooter');
    }



    function updatePageState(s) {
        pageState = s.detail;
        if (pageState === EnumPageState.resetPassword) {
            dispatch('updateShowFooter', false);
        } else {
            dispatch('updateShowFooter', true);
        }
    }

    export let tabItemActiveClasses = `currentB font-[600]`;
    export let tabItemInactiveClasses = "text-[#646B73]";
    let tabsClass = '';
    if (isMainAccountPage) {
        tabsClass = $loginPageConfig.bAccountConfig?.tabClass || '';
    } else {
        tabsClass = $loginPageConfig.bSubAccountConfig?.tabClass || '';
    }
    if (loginMainMethod?.length === 3 && loginMainMethod.every(e => e.title)) {
        tabsClass = 'justify-between';
    }

    function calculateHeight() {
        // 最外层 margin-top: 8
        // tab-tip: 20
        // 第一个input：48+24
        // 第二个input：48+44
        // Button：48
        // 副操作：28
        // 返回身份选择：44
        let config;
        if (isMainAccountPage) {
            config = $loginPageConfig.bAccountConfig;
        } else {
            config = $loginPageConfig.bSubAccountConfig;
        }
        const baseHeight = 8 + 20 + 48+24 + 48+44 + 48;
        let height = baseHeight;
        if (config?.viceOperateConfig) {
            // 副操作
            height += 28;
        }
        if (config?.otherOperateConfig) {
            // 返回身份选择
            height += 44;
        }
        // 二阶段切换登录方式 20+68
        if (currentStage === 2) {
            height += (20+68);
        }
        console.log('height is' ,height);
        return height;
    }
</script>

<div>
    {#if loginMainMethod[loginMainMethod.length-1].type === 'scan' && !loginMainMethod[loginMainMethod.length-1]?.title && [EnumPageState.form, EnumPageState.qrcode].includes(pageState)}
        <div class="absolute right-0 top-0">
            {#if pageState === EnumPageState.qrcode}
                <IconPc
                        class="currentB"
                        on:click={switchCurrentState}
                        toolTip={pcToolTip}
                />
            {:else}
                <IconQrCode
                        class="currentB"
                        on:click={switchCurrentState}
                        toolTip={qrCodeToolTip}
                />
            {/if}
        </div>
    {/if}
    {#if pageState === EnumPageState.qrcode}
        <QrCodeB
                height={qrCodeAbsoluteHeight}
        />
    {:else if pageState === EnumPageState.resetPassword}
        <ResetPassword on:updatePageState={updatePageState}
        />
    {:else if pageState === EnumPageState.register}
        <Register on:updatePageState={updatePageState} />
    {:else if pageState === EnumPageState.bindPhone}
        <BindPhone
                on:updatePageState={updatePageState}
        />
    {:else}
        <div class={`px-[48px] pt-[48px] currentStage-${currentStage}`}
        >
            {#if mainTitle}
                <div class="main-title">
                    {mainTitle}
                    {#if mainTitleTooltip}
                        <IconQuestion width={24} height={24} />
                        <Tooltip class="mb-[4px] opacity-[0.85] bg-[#000000]">{mainTitleTooltip}</Tooltip>
                    {/if}
                </div>
            {/if}
            {#if loginMainMethod?.length === 1}
                {#if loginMainMethod[0]?.tabTip}
                    <div class="tab-tip">{loginMainMethod[0].tabTip}</div>
                {/if}
                {#if loginMainMethod[0].type === 'password'}
                    <PasswordForm
                            on:updatePageState={updatePageState}
                            on:toggleShowFooter={() => dispatch('toggleShowFooter')}
                    ></PasswordForm>
                {:else if loginMainMethod[0].type === 'code'}
                    <SMSForm
                            on:updatePageState={updatePageState}
                            on:toggleShowFooter={() => dispatch('toggleShowFooter')}
                    ></SMSForm>
                {:else if loginMainMethod[0].type === 'scan'}
                    <QrCodeB height={calculateHeight()}
                             displayType="tab"
                    />
                {/if}
            {:else}
                <Tabs class={tabsClass}>
                    {#each loginMainMethod.filter(e => e.title) as method, index}
                        <TabItem title={method.title}
                                 activeClasses={tabItemActiveClasses}
                                 inactiveClasses={tabItemInactiveClasses}
                                 open={ isMainAccountPage ? $tabBusinessMainStore[index] : $tabBusinessSubStore[index] }
                                 tabTip={method.tabTip}
                                 showTabTip={method.type !== 'scan'}
                        >
                            {#if method.type === 'password'}
                                <PasswordForm
                                        on:updatePageState={updatePageState}
                                        on:toggleShowFooter={() => dispatch('toggleShowFooter')}
                                ></PasswordForm>
                            {:else if method.type === 'code'}
                                <SMSForm
                                        on:updatePageState={updatePageState}
                                        on:toggleShowFooter={() => dispatch('toggleShowFooter')}
                                ></SMSForm>
                            {:else if method.type === 'scan'}
                                <QrCodeB height={calculateHeight()}
                                         displayType="tab"
                                />
                            {/if}
                        </TabItem>
                    {/each}
                </Tabs>
            {/if}
        </div>
    {/if}
</div>

<style>
    .main-title {
        color: #222222;
        font-size: 24px;
        font-weight: 600;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 32px;
        text-align: left;
        vertical-align: top;
        margin-bottom: 24px;
        display: flex;
        gap: 4px;
        align-items: center;
    }
    .currentStage-1 {
        padding-bottom: 48px;
    }
    .currentStage-2 {
        padding-bottom: 32px;
    }
    .currentStage-3 {
        padding-bottom: 32px;
    }
    .tab-tip {
        color: #9C9C9C;
        font-size: 12px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        height: 20px;
        text-align: left;
        vertical-align: top;
    }
</style>
