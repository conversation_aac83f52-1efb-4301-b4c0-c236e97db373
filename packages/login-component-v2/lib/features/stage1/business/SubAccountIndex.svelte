<script lang="ts">
    import BusinessLoginForm from "$lib/features/stage1/business/index.svelte";
    import Agreements from "$lib/features/components/Agreements.svelte";
    import { createEventDispatcher, getContext } from "svelte";

    const configStore = getContext('loginPageConfig');
    const bSubAccountConfig = $configStore.bSubAccountConfig;
    let showFooterSection = true;
    function updateShowFooter(event) {
        showFooterSection = event.detail;
    }
    function toggleShowFooter() {
        showFooterSection = !showFooterSection;
    }
    let agreementType = 'login';
    function updateAgreementType(event) {
        agreementType = event.detail;
    }
    const configAgreements = bSubAccountConfig.agreements || [];
    const agreements = [
        {
            title: '用户协议',
            url: 'https://passport.kuaishou.com/b-account-h5/agreement',
            onClick: () => {
                bSubAccountConfig?.baseAgreementOnClick && bSubAccountConfig.baseAgreementOnClick();
            },
        },
        ...configAgreements,
    ]
</script>

<div>
    <BusinessLoginForm
            on:toggleShowFooter={toggleShowFooter}
            on:updateShowFooter={updateShowFooter}
            on:updateAgreementType={updateAgreementType}
    />
    <Agreements
            agreements={agreements}
            show={showFooterSection}
            {agreementType}
    />
</div>
