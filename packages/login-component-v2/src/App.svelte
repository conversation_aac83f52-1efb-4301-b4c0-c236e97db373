<script lang="ts">
    import { EnumLoginType, renderComponent } from '../lib';
    import { onMount } from 'svelte/internal';

    let com;
    onMount(() => {
        com = renderComponent({
            target: document.querySelector('.container1')!,
            env: 'staging',
            // sid: 'kuaishou.ad.uc',
            // sid: 'ksBid.kuaishou.shop.test2',
            sid: 'kuaishou.shop.test',
            // baseUrl: 'https://passport-bid.staging.kuaishou.com',
            baseUrl: 'https://ksid.staging.kuaishou.com',
            onSuccess(res) {
                console.log('success', res);
                if (['RegisterB', 'ResetPasswordC', 'ResetPasswordB', 'ResetPasswordChooseUser'].includes(res.loginType)) {
                    com.reRender();
                }
            },
            onFail(res) {
                console.log('fail', res);
            },
            onLoginStart() {
                console.log('start');
            },
            isMainAccountPage: true,
            enableSig4: true,
            borderRadiusConfig: {
                // 外部圆角
                containerRadius: 16,
                // 文本框圆角
                inputRadius: 8,
                // 按钮圆角
                buttonRadius: 8,
                // hover圆角
                toolTipRadius: 2,
            },
            loginPageConfig: {
                currentMainState: 2,
                errorMsgClass: 'text-[#E74933]',
                iconStyle: 1,
                "cAccountConfig": {
                    "mainTitle": "111",
                    "mainTitleTooltip": "dudu",
                    "qrCodeToolTip": "扫码登录更便捷",
                    "pcToolTip": "其他登录方式",
                    "loginMainMethod": [
                        {
                            "type": "password",
                            "title": "密码登录",
                            "tabTip": "未注册手机号登陆成功后将自动注册"
                        },
                        {
                            "type": "code",
                            "title": "短信登陆"
                        },
                        {
                            "type": "scan",
                            "title": "扫码登录"
                        },
                    ],
                    "passwordLoginTypes": [
                        "phone",
                        "email"
                    ],
                    "agreements": [
                        {
                            "title": "隐私条款",
                            "url": "https://ppg.viviv.com/doodle/qVXyKOXE.html?hyId=jimu_qVXyKOXE"
                        }
                    ],
                    "inputClass": "hover:border-[#EA582B] focus:border-[#EA582B] focus:shadow-[0_0_0_2px_rgba(234,88,43,0.2)] focus-within:border-[#EA582B] focus-within:shadow-[0_0_0_2px_rgba(234,88,43,0.2)]",
                    "tabsClass": "",
                    // "scanConfig": {
                    //     "rawDescHTML": "<div class='text-[18px] text-[#FF0000]'>测试2</div>"
                    // },
                },
                "bAccountConfig": {
                    "mainTitle": "商家账号登录",
                    "mainTitleTooltip": "曾通过快手APP账号授权登录，即拥有商家账号",
                    "qrCodeToolTip": "扫码登录更便捷",
                    "pcToolTip": "其他登录方式",
                    "loginMainMethod": [
                        {
                            "type": "password",
                            "title": "密码登录"
                        },
                        {
                            "type": "code",
                            "title": "验证码登录",
                            "tabTip": "未注册手机号登陆成功后将自动注册"
                        },
                        {
                            "type": "scan",
                            "title": "扫码"
                        }
                    ],
                    "passwordLoginTypes": [
                        "phone",
                        "email"
                    ],
                    "agreements": [
                    ],
                    baseAgreementOnClick: () => {
                        console.log('test');
                    },
                    "scanConfig": {
                        "appName": "快手小店商家版",
                        // "rawDescHTML": "<div class='text-[20px]'>测试</div>"
                    },
                    "oAuthTypes": [
                        {
                            "type": "ks",
                            "redirectUrl": "https://mfe-component-v2.led.staging.kuaishou.com"
                        }
                    ],
                    "viceOperateConfig": {
                        leftSection: [
                            {
                                type: 'register',
                                onClick: () => {
                                    console.log('register');
                                },
                            },
                            {
                                type: 'reset',
                            },
                        ],
                        rightSection: [
                            {
                                type: 'question',
                                onClick: () => {
                                    console.log('qu');
                                },
                            },
                        ],
                    },
                    "inputClass": "hover:border-[#EA582B] focus:border-[#EA582B] focus:shadow-[0_0_0_2px_rgba(234,88,43,0.2)] focus-within:border-[#EA582B] focus-within:shadow-[0_0_0_2px_rgba(234,88,43,0.2)]",
                    "tabsClass": "justify-evenly",
                },
                "bSubAccountConfig": {
                    "mainTitle": "快手商家账号登录",
                    "loginMainMethod": [
                        {
                            "type": "password",
                            "title": "密码登陆"
                        },
                        {
                            "type": "code",
                            "title": "短信登陆"
                        },
                        {
                            "type": "scan"
                        }
                    ],
                    "passwordLoginTypes": [
                        "phone"
                    ],
                    "agreements": [

                    ],
                    "scanConfig": {
                        "appName": "快手小店商家版APP"
                    },
                    "viceOperateConfig": {
                        leftSection: [
                            {
                                type: 'register',
                                onClick: () => {
                                    console.log('register');
                                },
                            },
                            {
                                type: 'reset',
                            },
                        ],
                        rightSection: [
                            {
                                type: 'question',
                                onClick: () => {
                                    console.log('qu');
                                },
                            },
                        ],
                    },
                },
            },
        });
    });

    function clickTag() {
        com.reRender();
    }
</script>
<div class="box w-screen h-screen flex items-center justify-center">
    <div class="tags">
        <div class="tag" on:click={clickTag}>tag1</div>
        <div class="tag" on:click={clickTag}>tag2</div>
    </div>
    <div class="container1">

    </div>
</div>

<style>
    .tags {
        display: flex;
    }
    .tag {
        border: 1px solid black;
        width: 100px;
    }
    .box {
        width: 100vw;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }
    .container1 {
        width: 456px;
        border: 1px solid #000;
        /*height: 500px;*/
    }
</style>
