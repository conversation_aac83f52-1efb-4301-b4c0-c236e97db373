// 微信 Oauth2 地址
export const WEIXIN_OAUTH_URL = 'https://open.weixin.qq.com/connect/oauth2/authorize';

// 快手APP统一下载页：端外分享业务和中台用的，作为ulink拉端的兜底页
export const KUAISHOU_APP_DOWNLOAD_ULINK = 'https://m.ssl.kuaishou.com/ulink/';

// 一证通查投诉页面
export const ONE_ID_QUERY_COMPLAINTS = 'https://collect.m.kuaishou.com/biz/complaints-from-yizhengtong';

// 快手解绑说明
export const KUAISHOU_UNBIND_DES_URL = 'https://ppg.viviv.com/doodle/nDwWHEcV.html?uni_src=ENEUKR&hyId=jimu_nDwWHEcV&layoutType=4&noBackNavi=true';

// 快手协议链接
export const KUAISHOU_PROTOCOL_POLICY = 'https://app.m.kuaishou.com/public/index.html#/protocol/privacy';
export const KUAISHOU_PROTOCOL_POLICY_SMALL = 'https://app.m.kuaishou.com/public/index.html#/protocol/privacy?smallWebview=true';
export const KUAISHOU_PROTOCOL_SERVICE = 'https://app.m.kuaishou.com/public/index.html#/protocol/service';
export const KUAISHOU_PROTOCOL_SERVICE_SMALL  = 'https://app.m.kuaishou.com/public/index.html#/protocol/service?smallWebview=true';

// 账号注销页面入口
export const KUAISHOU_ACCOUNT_UNREGISTER = {
    staging: 'https://h5-account-master.web-ops.staging.kuaishou.com/account/logout#/code',
    // prt: 'https://h5-account.test.gifshow.com/account/logout#/code',
    prt: 'https://h5-account.prt.kuaishou.com/account/logout#/code',
    production: 'https://app.m.kuaishou.com/account/logout#/code',
};
