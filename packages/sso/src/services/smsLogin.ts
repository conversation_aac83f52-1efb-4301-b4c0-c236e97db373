import { DEFAULT_COUNTRY_CODE } from '../config/constants';
import { SCENE_CODE } from '../config/enums';
import { AuthTokenResult, BaseRes, QuickLoginResult } from '../types/services';
import { getLoginToken } from '../core/loginToken';
import { privateGetConfig } from '../core/configInit';
import { startIdentityVerification } from '../utils/identityVerificationAdapter';
import { request } from '@/core/request';
import { wrapLoginRequest } from '../core/bidLoginAdapter';

export interface SMSLoginInfo {
    phone?: string;
    countryCode?: string;
    account?: string;
    smsCode: string | number;
    createId?: boolean;
    setCookie?: boolean;
}


/**
 * 手机号 + 短信验证码登录
 */
export function login({
    phone = '',
    countryCode = DEFAULT_COUNTRY_CODE,
    smsCode, createId = true,
    setCookie = true,
    account,
}: SMSLoginInfo): Promise<AuthTokenResult | QuickLoginResult> {
    const config = privateGetConfig();
    const r = getLoginToken('/pass/kuaishou/login/mobileCode', {
        countryCode,
        phone,
        account,
        sid: config.sid,
        createId,
        smsCode,
        setCookie,
        // kuaishouAuth: config.kuaishouAuth ? true: undefined,
    });
    return wrapLoginRequest(r);
}

/**
 * 发送短信验证码
 */
export interface requestMobileCodeResponse {
    result: number;
    error_msg?: string;
    jsSdkUrl?: string;
    url?: string;
    sid?: string;
}

export interface requestMobileCodeDataOnLoggedIn {
    type: number;
    ztIdentityVerificationType?: number; // 回放请求时需要
    ztIdentityVerificationCheckToken?: string; // 回放请求时需要
}

export interface requestMobileCodeDataWithTypeForPhone {
    countryCode?: string;
    phone?: string;
    account?: string;
    type: number;
    ztIdentityVerificationType?: number; // 回放请求时需要
    ztIdentityVerificationCheckToken?: string; // 回放请求时需要
}

export interface requestMobileCodeDataWithTypeForAccount {
    account: string;
    type: number;
    ztIdentityVerificationType?: number; // 回放请求时需要
    ztIdentityVerificationCheckToken?: string; // 回放请求时需要
}

export interface requestMobileCodeDataWithIsLogin {
    countryCode?: string;
    phone?: string;
    isLogin?: boolean;
    account?: string;
    type?: number;
    ztIdentityVerificationType?: number; // 回放请求时需要
    ztIdentityVerificationCheckToken?: string; // 回放请求时需要
}

export function requestMobileCode({type, ztIdentityVerificationType, ztIdentityVerificationCheckToken}: requestMobileCodeDataOnLoggedIn): Promise<requestMobileCodeResponse>;
export function requestMobileCode({account, type, ztIdentityVerificationType, ztIdentityVerificationCheckToken}: requestMobileCodeDataWithTypeForAccount): Promise<requestMobileCodeResponse>;
export function requestMobileCode({countryCode, phone, type, ztIdentityVerificationType, ztIdentityVerificationCheckToken}: requestMobileCodeDataWithTypeForPhone): Promise<requestMobileCodeResponse>;
export function requestMobileCode({countryCode, phone, isLogin, ztIdentityVerificationType, ztIdentityVerificationCheckToken}: requestMobileCodeDataWithIsLogin): Promise<requestMobileCodeResponse>;
export function requestMobileCode({countryCode = DEFAULT_COUNTRY_CODE, phone, account, isLogin, type, ztIdentityVerificationType, ztIdentityVerificationCheckToken}: any) {
    const config = privateGetConfig();
    let finalType;
    let url = config.baseUrl + '/pass/kuaishou/sms/requestMobileCode'; // 未登录时使用
    if (!phone && !account) {
        url = config.baseUrl + '/pass/kuaishou/sms/requestMobileCodeWithToken'; // 已登录时使用，不需要指定phone或account
    }
    if (type) {
        finalType = type;
    } else if (isLogin) {
        finalType = SCENE_CODE.LOGIN;
    } else {
        finalType = SCENE_CODE.REGISTER;
    }
    return request<requestMobileCodeResponse>(url, {
        sid: config.sid,
        type: finalType,
        countryCode,
        phone,
        account,
        ztIdentityVerificationType,
        ztIdentityVerificationCheckToken,
        // kuaishouAuth: config.kuaishouAuth ? true: undefined,
    }).then(res => res, res => {
        // 返回在400001-410999之间时，视为需要快手验证码，会使用统一身份认证SDK打开验证页面
        if (res.result >= 400001 && res.result <= 410999) {
            return startIdentityVerification({
                url: res.url,
                qrcodeEnv: config.env,
                platform: config.platform,
            }).then(res => {
                if(res.result === -999) {
                    // 行为验证码和端外短信验证出现取消，直接向下透传
                    return Promise.reject(res);
                }
                if (!phone) {
                    return requestMobileCode({
                        type,
                        ztIdentityVerificationType: res.type,
                        ztIdentityVerificationCheckToken: res.token,
                    });
                }
                if (type) {
                    return requestMobileCode({
                        countryCode,
                        phone,
                        type,
                        ztIdentityVerificationType: res.type,
                        ztIdentityVerificationCheckToken: res.token,
                    });
                }

                return requestMobileCode({
                    countryCode,
                    phone,
                    isLogin,
                    ztIdentityVerificationType: res.type,
                    ztIdentityVerificationCheckToken: res.token,
                });
            });
        }
        return Promise.reject(res);
    });
}

/**
 * 校验短信验证码
 */
export interface validatePhoneOptions {
    smsCode: string | number;
    type: number;
}

export function validatePhone({smsCode, type}: validatePhoneOptions) {
    const config = privateGetConfig();
    return request<BaseRes>(config.baseUrl + '/pass/kuaishou/sms/validateMobileCodeWithToken', {
        sid: config.sid,
        smsCode,
        type,
    });
}

export interface getCountryCodeListRes extends BaseRes {
    countryCodeList: Record<string, string | number>[];
}

export function getCountryCodeList() {
    const config = privateGetConfig();
    return request<getCountryCodeListRes>(config.baseUrl + '/pass/kuaishou/sms/countryCodeList', {
        sid: config.sid,
    });
}
