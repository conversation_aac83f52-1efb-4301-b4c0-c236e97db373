/**
 * @file 加密的相关处理
 */
import JSEncrypt from 'jsencrypt';
import { b64tohex } from './base64';

/**
 * 加密算法的配置参数
 */
export const EncryptConfig = {
    // 公钥
    publicKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjWfCb9IR5pMheXLUHCQko8VddGGDZ2jN0Edj/yQoXl91plE6r/muh1oKeuWtSpDwqDDAO5s3lHYfPFb45eWTky0a4ijOBlGbls5WJBQqoqD3gYTPcZyc1KFmn9wRTbNLMFhWN2kCHzo6YOO9kRcCQdAuXaj2sxFrirdglL8v7I0gp0n2ME+3V4Jwiv86cL24t6DfzxHqW/CO7Q/7P6bE5xVZHkuup7J1vXrjewN0r9nXovmahYlLIop4QuWC6zDVHDSTk/SXifHJBidOgEWHKgQSC5FS3xism5bth8XKWu4WX/z2pND4vA4STNE9LwULQPX2MJFjqUdYG7fBePZnIwIDAQAB',
    // 秘钥的版本
    keyVersion: 'version0',
}


/**
 * 获取Unix时间戳
 */
function getTimeStamp(): number {
    const timestamp=new Date().getTime();
    return timestamp;
}

/**
 * 生成随机字符串: nonce   (默认长度10位)
 * @param total
 */
function genRandomString(total: number): string {
    const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
    const str = new Array(total).fill(1).map(() => {
        return chars.charAt(Math.floor(Math.random() * chars.length));
    }).join('');
    return str;
}

function genNonce(): string {
    return genRandomString(10);
}

/**
 * 对应关系:
 * version: yzzh-vs
 * timestamp: yzzh-tsp
 * nonce: yzzh-nc
 */
interface EncryptHeadersParams {
    timestamp: number;
    nonce: string;
}
function genEncryptHeaders(params: EncryptHeadersParams) {
    const headers = {
        'yzzh-vs': EncryptConfig.keyVersion,
        'yzzh-tsp': params.timestamp,
        'yzzh-nc': params.nonce,
    };
    return headers;
}

interface MixContent {
    timestamp: number;
    nonce: string;
    mixedText: string;
}
interface MixOptions {
    timestamp?: number;
    nonce?: string;
}
/**
 * 生成待加密的混入文本
 * @param rawText
 */
function genMixContent(rawText: string, options?: MixOptions): MixContent {
    const timestamp = (options && options.timestamp) || getTimeStamp();
    const nonce = (options && options.nonce) || genNonce();
    const mixedText = [timestamp, nonce, rawText].join('_');
    return {
        timestamp,
        nonce,
        mixedText,
    }
}
type TextFormat = 'base64' | 'hex';
function formatText(base64Text: string, format: TextFormat) {
    switch (format) {
        case 'base64':
            return base64Text;
        case 'hex':
            return b64tohex(base64Text);
        default:
        {
            const _exhaustiveCheck: never = format;
            return _exhaustiveCheck;
        }
    }
}

type EncryptContent = {
    encryptText: string;
    ignoreEncrypt: boolean;
};
type EncryptOptions = MixOptions & {
    format?: TextFormat;
    isMix?: boolean;
};

export function encryptorCreator(publicKey: string) {
    const encryptor = new JSEncrypt();
    encryptor.setPublicKey(publicKey);
    return {
        encrypt(rawText: string, options?: EncryptOptions): EncryptContent {
            const format: TextFormat = (options && options.format) || 'hex';
            let contentText = rawText;
            if (options && options.isMix) {
                const mixContent = genMixContent(rawText, options);
                contentText = mixContent.mixedText;
            }
            const encryptResult = encryptor.encrypt(contentText);
            let encryptText = rawText;
            let ignoreEncrypt = true;
            if (encryptResult) {
                encryptText = formatText(encryptResult, format);
                ignoreEncrypt = false;
            }
            return {
                encryptText,
                ignoreEncrypt,
            }
        }
    }
}

export const encryptor = encryptorCreator(EncryptConfig.publicKey);

export function genEncryptCommonParams() {
    const timestamp = getTimeStamp();
    const nonce = genNonce();
    const headers = genEncryptHeaders({
        timestamp,
        nonce,
    });
    const options: EncryptOptions = {
        timestamp,
        nonce,
    };
    return {
        timestamp,
        nonce,
        headers,
        options,
    }
}

export function genEncryptReqParams(params: {
    account?: string;
    phone?: string;
    email?: string;
    password?: string;
}) {
    const {
        options: encryptOptions,
        headers: encryptHeaders,
    } = genEncryptCommonParams();
    let reqParams: Record<string, string|boolean|ReturnType<typeof genEncryptHeaders>> = {
        encryptHeaders,
    };
    if (params.password) {
        const encryptedPwd = encryptor.encrypt(params.password, {
            ...encryptOptions,
            isMix: true,
        });
        reqParams = {
            ...reqParams,
            password: encryptedPwd.encryptText,
            ignorePwd: encryptedPwd.ignoreEncrypt,
        };
    }
    if (params.account) {
        const encryptedAccount = encryptor.encrypt(params.account, encryptOptions);
        reqParams = {
            ...reqParams,
            account: encryptedAccount.encryptText,
            ignoreAccount: encryptedAccount.ignoreEncrypt,
        };
    }
    if (params.phone) {
        const encryptedPhone = encryptor.encrypt(params.phone, encryptOptions);
        reqParams = {
            ...reqParams,
            phone: encryptedPhone.encryptText,
            ignoreAccount: encryptedPhone.ignoreEncrypt,
        };
    }
    if (params.email) {
        const encryptedEmail = encryptor.encrypt(params.email, encryptOptions);
        reqParams = {
            ...reqParams,
            email: encryptedEmail.encryptText,
            ignoreAccount: encryptedEmail.ignoreEncrypt,
        };
    }
    return reqParams;
}

