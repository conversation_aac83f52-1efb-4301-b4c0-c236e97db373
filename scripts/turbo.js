/**
 * 用于解决以下问题：
 *  turbo [pipline] 只能在仓库根目录执行，在二级目录下无法执行，即在项目以及sdk目录下无法利用其优势
 * 思路：
 *  1、turbo [pipline] --filter=package-name 可以指定构建
 *  2、在根目录封装一个脚本，用于执行上述命令
 *  3、在项目以及sdk目录下使用 node 执行以上脚本
 */
const childProcess = require('child_process');
const path = require('path');
const yargs = require('yargs');

// monoropo 根目录
const root = path.join(__dirname, '..');
// 模式
const { pipeline } = yargs.argv;
// scope 指定构建的项目
const { npm_package_name } = process.env;

childProcess.execSync(`turbo ${pipeline} --filter=${npm_package_name}`, { stdio: 'inherit', cwd: root })