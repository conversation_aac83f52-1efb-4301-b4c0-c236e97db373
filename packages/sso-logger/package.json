{"name": "@ks/sso-logger", "version": "0.2.4", "license": "MIT", "description": "report log for @ks/sso", "author": "kuaishou-fe", "main": "./dist/index.js", "module": "./dist/index.esm.js", "types": "./dist/src/index.d.ts", "scripts": {"dev": "rollup -c -w", "build": "rimraf dist && rollup -c", "lint": "lint-staged", "publish": "npm run build && npm publish"}, "files": ["dist", "README.md", "CHANGELOG.md"], "repository": {"type": "git", "url": "https://git.corp.kuaishou.com/explore-frontend/account-zt-general/-/tree/master/packages/sso-logger"}, "keywords": ["log", "sso"], "devDependencies": {"@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.0.6", "@types/jest": "^23.3.7", "@types/uuid": "^8.3.3", "@typescript-eslint/eslint-plugin": "^5.2.0", "@typescript-eslint/parser": "^5.2.0", "commitizen": "^4.2.4", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.1.0", "jest": "^24.0.19", "lint-staged": "^9.5.0", "rimraf": "^2.6.2", "rollup": "^2.58.3", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.30.0", "rollup-plugin-visualizer": "^5.5.2", "standard-version": "^9.3.2", "ts-jest": "^24.1.0", "tslib": "^2.3.1", "typescript": "^3.9.7"}, "dependencies": {"uuid": "^8.3.2"}, "lint-staged": {"src/*.ts": ["eslint src --ext .ts"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}