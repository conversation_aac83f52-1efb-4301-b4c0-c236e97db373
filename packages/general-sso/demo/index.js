import {
    init,
    requestSmsCode,
    smsCodeLogin,
    passwordLogin,
    chooseUser,
    passToken,
    resetPasswordByPhone,
    logout,
    smsCodeRegister,
    emailRegister,
    emailPasswordLogin,
    requestEmailCode,
    resetPasswordByEmail,
    checkPassStrength,
    checkPhoneAvailability,
    checkEmailAvailability,
    getConfig,
} from '../dist/index.esm.js';

init({
    sid: 'orgKwaiPro.api',
    appName: 'orgKwaiPro',
    baseUrl: 'https://pptest.staging.kuaishou.com',
    callback: '',
    language: 'en',
});

window.requestSmsCode = async function (e) {
    const form = e.target;
    try {
        const res = await requestSmsCode({
            phone: form.phone.value,
            countryCode: form.countryCode.value,
            type: form.type.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.smsCodeLogin = async function (e) {
    const form = e.target;
    try {
        const res = await smsCodeLogin({
            phone: form.phone.value,
            countryCode: form.countryCode.value,
            smsCode: form.smsCode.value,
            registerOpen: form.registerOpen.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.passwordLogin = async function (e) {
    const form = e.target;
    try {
        const res = await passwordLogin({
            phone: form.phone.value,
            countryCode: form.countryCode.value,
            password: form.password.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.chooseUser = async function (e) {
    const form = e.target;
    try {
        const res = await chooseUser({
            phone: form.phone.value,
            countryCode: form.countryCode.value,
            userId: form.userId.value,
            multiUserToken: form.multiUserToken.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.passToken = async function (e) {
    const form = e.target;
    try {
        const res = await passToken(form.sid.value);
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.resetPasswordByPhone = async function (e) {
    const form = e.target;
    try {
        const res = await resetPasswordByPhone({
            phone: form.phone.value,
            countryCode: form.countryCode.value,
            smsCode: form.smsCode.value,
            password: form.password.value,
            setCookie: !!form.setCookie.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.logout = async function (e) {
    const form = e.target;
    try {
        const res = await logout();
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.smsCodeRegister = async function (e) {
    const form = e.target;
    try {
        const res = await smsCodeRegister({
            phone: form.phone.value,
            countryCode: form.countryCode.value,
            smsCode: form.smsCode.value,
            password: form.password.value,
            setCookie: !!form.setCookie.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.emailRegister = async function (e) {
    const form = e.target;
    try {
        const res = await emailRegister({
            email: form.email.value,
            password: form.password.value,
            emailCode: form.emailCode.value,
            setCookie: !!form.setCookie.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.emailPasswordLogin = async function (e) {
    const form = e.target;
    try {
        const res = await emailPasswordLogin({
            email: form.email.value,
            password: form.password.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.requestEmailCode = async function (e) {
    const form = e.target;
    try {
        const res = await requestEmailCode({
            email: form.email.value,
            type: form.type.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.resetPasswordByEmail = async function (e) {
    const form = e.target;
    try {
        const res = await resetPasswordByEmail({
            email: form.email.value,
            password: form.password.value,
            emailCode: form.emailCode.value,
            setCookie: !!form.setCookie.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.checkPassStrength = async function (e) {
    const form = e.target;
    try {
        const res = await checkPassStrength({
            password: form.password.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.checkPhoneAvailability = async function (e) {
    const form = e.target;
    try {
        const res = await checkPhoneAvailability({
            phone: form.phone.value,
            countryCode: form.countryCode.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.checkEmailAvailability = async function (e) {
    const form = e.target;
    try {
        const res = await checkEmailAvailability({
            email: form.email.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};
