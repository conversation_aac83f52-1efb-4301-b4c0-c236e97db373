<script>
    import {createEventDispatcher, getContext} from 'svelte';
    const dispatch = createEventDispatcher();
    export let maxlength;
    export let minlength;
    export let placeholder;
    export let disabled;
    export let readonly;
    export let radius;
    export let type;
    export let value;
    export let className = '';
    export let overflow = ''
    // 这个在 pl 里没有啊？
    export let height;
    const themeColor = getContext('themeColor');
    let isFocus;
    let focus = () => {
        isFocus = true;
        dispatch('focus', value);
    };
    let blur = () => {
        isFocus = false;
        dispatch('blur', value);
    };

    const eventHandler = () => ({});

    const onChange = e => (value = e.target.value);
</script>

<div class="pl-input-container {className} {themeColor}" class:focus={isFocus} class:disabled class:readonly class:radius>
    <div class="pl-input {overflow}">
        <!-- {prepend && prepend.length ? <div class="pl-input-prepend">{prepend}</div> : null} -->
        <input
            class="pl-input-text"
            {type}
            {readonly}
            {disabled}
            autocomplete="off"
            {placeholder}
            {value}
            {maxlength}
            {minlength}
            on:input={onChange}
            on:change={onChange}
            on:focus={focus}
            on:blur={blur}
            on-keydown={$event => eventHandler('keydown', $event.target.value)}
            on-keyup={$event => eventHandler('keyup', $event.target.value)} />
        <div class="pl-input-append">
            <slot name="append" />
        </div>
    </div>
</div>

<style type="text/stylus">
@import '../../common/variables.styl';
.pl-input
    display flex
    position relative
    height 100%
    width 100%
    overflow hidden
    &.visible
        overflow visible
    background-color transparent
    &-container
        box-sizing border-box
        border-bottom 1px solid input-border-color
        background-color input-bg-color
        transition border-color ease-in-out .15s
        color font-color-primary
        //border-radius 4px
        display inline-block
        position relative
        width 210px
        height 32px
        font-size 12px
        &.large
            width 100%
            height 46px
            //padding-left 16px
            font-size 15px
        &.radius
            border-radius 999px
            .pl-input
                border-radius 999px
            .pl-input-text
                border-radius 999px
        &:hover
            border-color darken(input-border-color, 10%)
        &.focus
            border-color brand-primary
            &.blue
                border-color brand-primary-blue
        &.readonly
            border-color input-border-color
        &.disabled
            cursor not-allowed
            background-color input-disabled-bg-color
    &-prepend
    &-append
        padding 0 5px
        display flex
        align-items center
        &:empty
            padding 0
    &-text
        outline none
        padding 0 5px
        border none
        box-sizing border-box
        vertical-align top
        background-color transparent
        width 100%
        flex 1
        &[disabled]
            cursor not-allowed
</style>
