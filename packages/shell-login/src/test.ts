import { ShellLogin } from "./index";

const SID = 'kuaishou.mp.tools';
process.env.NODE_TLS_REJECT_UNAUTHORIZED ="0";

async function test(){
    const sl = new ShellLogin(SID,"production")
    const res = await sl.printQRCode({ small: false })

    let userInfo = await sl.getUserLoginInfo();
    console.log("userInfo",userInfo)

    setTimeout(() => {
        sl.cancelQrLogin()
    }, 3000)

    let resultScan = await sl.scanQRLoginResult();
    console.log("resultScan",resultScan)
    
}

test()

