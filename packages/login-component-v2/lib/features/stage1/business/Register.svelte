<script lang="ts">
    import Input from '$lib/shared/forms/input.svelte';
    import SeePassword from "$lib/utils/SeePassword.svelte";
    import HidePassword from "$lib/utils/HidePassword.svelte";
    import { requestCodeByAccountV2, emailRegisterV2 } from '@ks/general-sso';
    import Button from "$lib/shared/button/index.svelte";
    import { createEventDispatcher, getContext } from "svelte";
    import { EnumErrorType, EnumInputLoginType, EnumLoginType, EnumPageState } from "$lib/shared/const";
    import CloseButton from "$lib/utils/CloseButton.svelte";
    import { createRegisterStore } from "$lib/store/registerStore";
    import { isValidEmail } from "$lib/utils/validate";
    import DropDown from "$lib/shared/dropdown/DropDown.svelte";
    import { filterInput } from "$lib/utils/validate";

    const store = createRegisterStore([EnumInputLoginType.email]);
    const dispatch = createEventDispatcher();
    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    const onLoginStart = getContext('onLoginStart');
    const isMainAccountPage = getContext('isMainAccountPage');
    const configStore = getContext('loginPageConfig');

    let loginErrorMsg = '';
    let showPassword = false;
    let codeClass = '';
    let buttonText = '注册';
    let inputClass;
    if (isMainAccountPage) {
        inputClass = $configStore.bAccountConfig?.inputClass || '';
    } else {
        inputClass = $configStore.bSubAccountConfig?.inputClass || '';
    }

    $: {
        if (isValidEmail($store.account)) {
            codeClass = 'activate';
        } else {
            codeClass = '';
        }
    }


    let sendCodeClicked = false;
    async function sendCodeAction() {
        if (!store.reducers.validateSend()) {
            return;
        }
        if ($store.countDown > 0) {
            return;
        }
        try {
            await requestCodeByAccountV2({
                account: $store.account,
                countryCode: '+86',
                type: 461,
            });
            sendCodeClicked = true;
            store.setValues({
                countDown: 60,
            });
            const interal = setInterval(() => {
                if ($store.countDown <= 0) {
                    clearInterval(interal);
                    return;
                }
                store.setValues({
                    countDown: $store.countDown - 1,
                });
            }, 1000);
        } catch (error) {
            console.log('error', error);
            loginErrorMsg = error.error_msg || '网络错误，请稍后重试';
            onFail({
                loginType: EnumErrorType.SendCodeError,
                error,
            });
        }
    }

    function backToForm() {
        dispatch('updatePageState', EnumPageState.form);
    }

    let buttondisabled = false;
    async function onRegister() {
        if (!store.reducers.validateLogin()) {
            return;
        }
        try {
            onLoginStart({
                name: EnumLoginType.RegisterB,
            });
            buttondisabled = true;
            console.log('$store.account', $store.account);
            const res = await emailRegisterV2({
                email: $store.account,
                emailCode: $store.smsCode,
                password: $store.password1,
            });
            buttondisabled = false;
            buttonText = '注册成功';
            onSuccess({
                loginType: EnumLoginType.RegisterB,
                result: res,
            });
        } catch (error) {
            buttondisabled = false;
            loginErrorMsg = error?.error_msg || '网络错误，请稍后重试';
            console.log('loginErrorMsg', loginErrorMsg);
            onFail({
                loginType: EnumLoginType.RegisterB,
                error,
            });
        }
    }
</script>

<div class="pt-[48px] px-[48px] pb-[32px]">
    <div class="b-register-title">快手商家账号注册</div>
    <div class="mt-[24px] relative">
        <Input placeholder={$store.placeholder}
               bind:value={$store.account}
               on:input={store.reducers.updateInputValue}
               on:focus={() => {
                   store.setValues({
                       accountErrorMsg: '',
                   })
               }}
               on:blur={store.reducers.onAccountBlur}
               on:keydown={store.reducers.handleKeydown}
               bind:ref={$store.emailInput}
               errorMsg={$store.accountErrorMsg}
               class={inputClass}
        >
            <slot slot="right">
                {#if $store.account}
                    <CloseButton on:click={store.reducers.clearInputValue}/>
                {/if}
            </slot>
        </Input><DropDown
            class="absolute z-30 w-full currentB"
            visible="{$store.emailDropdownVisible}"
            tooltips="请选择邮箱类型"
            current="{$store.emailHighlightIndex}"
            options="{$store.emailDropdownList}"
            onSelect="{store.reducers.handleEmailSelect}"
        />
    </div>
    <div class="mt-[24px]">
        <Input placeholder="设置登录密码（8-20个字符）"
               bind:value={$store.password1}
               type={showPassword ? 'text': 'password'}
               errorMsg={$store.password1ErrorMsg}
               maxlength={20}
               on:input={filterInput}
               on:focus={() => {
                   store.setValues({
                       password1ErrorMsg: '',
                   })
               }}
               on:blur={store.reducers.onPassword1Blur}
               class={inputClass}
        >
            <slot slot="right">
                {#if showPassword}
                    <SeePassword on:click={() => (showPassword = !showPassword)}/>
                {:else}
                    <HidePassword on:click={() => (showPassword = !showPassword)}/>
                {/if}
            </slot>
        </Input>
    </div>
    <div class="mt-[24px]">
        <Input placeholder="请确认密码"
               bind:value={$store.password2}
               type={showPassword ? 'text': 'password'}
               errorMsg={$store.password2ErrorMsg}
               maxlength={20}
               on:input={filterInput}
               on:focus={() => {
                   store.setValues({
                       password2ErrorMsg: '',
                   })
               }}
               on:blur={store.reducers.onPassword2Blur}
               class={inputClass}
               on:focus={() => {
                    store.setValues({
                        password2ErrorMsg: '',
                    })
               }}
        >
        </Input>
    </div>
    <div class="mt-[24px]">
        <Input placeholder="请输入验证码"
               bind:value={$store.smsCode}
               maxlength={6}
               errorMsg={$store.smsCodeErrorMsg}
               on:focus={() => {
                   store.setValues({
                       smsCodeErrorMsg: '',
                   })
               }}
               on:input={store.reducers.filterSmsCode}
               on:blur={store.reducers.onSmsCodeBlur}
               globalErrorMsg={loginErrorMsg}
               class={inputClass}
        >
            <slot slot="right">
                <div class="hover:cursor-pointer send-code font-[400] text-[14px] leading-[22px] currentC"
                     on:click={sendCodeAction}
                >
                    {#if $store.countDown}
                        <span class="text-[#C6C6C6ff]">{$store.countDown}s</span>
                    {:else}
                        <span class={codeClass}>
                            {#if sendCodeClicked}
                                重新发送
                            {:else}
                                发送邮箱验证码
                            {/if}
                        </span>
                    {/if}
                </div>
            </slot>
        </Input>
    </div>
    <Button class="currentB mt-[44px]"
            on:click={onRegister}
            disabled={buttondisabled}
    >
        {buttonText}
    </Button>
    <div class="vice-operate">
        已有账号？<span on:click={backToForm}>直接登录</span>
    </div>
</div>

<style>
    .currentB {
        color: var(--colorB);
    }
    .b-register-title {
        color: #222222;
        font-size: 24px;
        font-weight: 600;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 32px;
        text-align: left;
        vertical-align: top;
    }
    .vice-operate {
        color: #9C9C9C;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        vertical-align: top;
        margin-top: 8px;
    }
    .vice-operate span {
        color: var(--colorB);
        cursor: pointer;
    }
    .send-code {
        color: #C6C6C6ff;
    }
    .activate {
        color: var(--colorB);
    }
</style>
