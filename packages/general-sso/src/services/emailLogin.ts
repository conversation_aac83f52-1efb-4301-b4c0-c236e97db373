import { getConfig } from '@/core/configInit';
import { getLoginToken } from '@/core/loginToken';
import { identityVerificationInterceptor } from '@/core/verificationInterceptor';
import { BaseRes } from '../types';
import { genEncryptReqParams } from '@/utils';

/**
 * 邮箱+密码登录
 */
export interface emailPasswordLoginData {
    email: string;
    password: string;
}
export function emailPasswordLogin({ email, password }: emailPasswordLoginData) {
    const config = getConfig();
    return getLoginToken(`/pass/${config.appName}/web/login/emailPassword`, {
        sid: config.sid,
        email,
        password,
    });
}

export function emailPasswordLoginV2({ email, password }: emailPasswordLoginData) {
    const encryptReqParams = genEncryptReqParams({
        email,
        password,
    });
    const config = getConfig();
    const params = {
        sid: config.sid,
        ...encryptReqParams,
    };

    return getLoginToken(`/pass/${config.appName}/web/login/emailPassword/v2`, {
        ...params,
    });
}

/**
 * 发送邮箱验证码
 */
export interface requestEmailCodeDataWithEmail {
    email: string;
    type: number;
}
export function requestEmailCode({ email, type }: requestEmailCodeDataWithEmail): Promise<BaseRes> {
    const config = getConfig();
    return identityVerificationInterceptor(`/pass/${config.appName}/web/email/code`, {
        sid: config.sid,
        email,
        type,
    });
}

/**
 * 通过邮箱重置密码
 */
export interface resetPasswordByEmailData {
    email: string;
    password: string;
    emailCode: string | number;
    setCookie: boolean;
}
export function resetPasswordByEmail({ email, password, emailCode, setCookie }: resetPasswordByEmailData) {
    const config = getConfig();
    return getLoginToken(`/pass/${config.appName}/web/password/resetByEmail`, {
        sid: config.sid,
        email,
        password,
        emailCode,
        setCookie,
    });
}
