
declare module '@/common/logger' {
    import WebLog, {TaskOptions} from "@ks/weblogger";

    /**
     * 只使用 @ks/weblogger sendImmediately 接口
     * weblog 由 外部传入
     */
    export interface WebLogBasic {
        sendImmediately: WebLog['sendImmediately'];
    }

    /**
     * 设置新Logger;
     * @param logger 日志对象
     */
    export function setLogger(logger: WebLogBasic): void;

    /**
     * 给Logger增加风控插件;
     * @param config
     */
    export function addRiskPlugin(config: {
        env?: string;
        sid: string;
    }): void

    /**
     * 导出的默认logger
     * 非 prod 环境会直接输出到console
     */
    export const mockLogger: WebLogBasic;


    /**
     * 按生命周期 发送 PV Enter 和 PV Leave
     * @param pageName 页面名称
     * @param params
     */
    export function sendPVEnterLeave(pageName: string, params: object): void;

    /**
     * 按生命周期 发送 PV Enter 和 PV Leave
     * 补充 document.referrer || '' 作为 params.source_url
     * @param pageName 页面名称
     * @param params
     */
    export function sendCommonPV(pageName: string, params: object): void;

    /**
     * 发送 TaskEvent 事件
     * 兼容v2 v3 格式
     * v2 格式时，使用 option.action 作为 sendImmediately 第一个参数
     * v3 格式时，使用 'CLICK' 作为 sendImmediately 第一个参数
     *
     * @note 格式判断依赖于 WebLogger.logConfig.proto 变量
     * @param option Task参数
     */
    export function sendTaskEvent(option: TaskOptions): void;
}
