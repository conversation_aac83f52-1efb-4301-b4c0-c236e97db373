import { parseUrl } from '@/utils/url';
import { init, qrScanned, qrAccept, cancelQrScanLogin } from '@ks/sso';
import ksBridge from '@ks/ks-bridge';
import { stringify } from 'qs';
import './style.styl';

const query = parseUrl(location.href).paramObj;

let canConfirm = false;
let hasError = false;

function changeErrorState() {
    hasError = true;
    (document.getElementById('confirm-btn') as HTMLElement).innerHTML = '重新扫码';
    (document.getElementById('hasError') as HTMLElement).style.display = 'block';
    (document.getElementById('noError') as HTMLElement).style.display = 'none';
    // (document.getElementsByClassName('qr-scanned-error-icon')[0] as HTMLElement).style.display = 'block';
    // (document.getElementsByClassName('qr-scanned-page-icon')[0] as HTMLElement).style.display = 'none';
    // (document.getElementsByClassName('qr-scanned-page-info error')[0] as HTMLElement).style.display = 'block';
    // (document.getElementsByClassName('qr-scanned-page-info normal')[0] as HTMLElement).style.display = 'none';
}

async function initPage() {
    // H5 打开做scanned 确认
    try {
        const qrScannedRes = await qrScanned({
            qrLoginToken: query.qrLoginToken,
        });
        if (qrScannedRes.result === 1) {
            canConfirm = true;
        }
    } catch (e) {
        changeErrorState();
    }
}

async function main() {
    init({
        sid: query.sid || '',
        env: query.env || 'production',
    });

    ksBridge.setTopLeftBtn({
        show: true,
        icon: 'close',
        onClick: async () => {
            if (!hasError) {
                await cancelQrScanLogin({
                    qrLoginToken: query.qrLoginToken,
                });
            }
            ksBridge.exitWebView();
        },
    });

    ksBridge.setSlideBack({
        enabled: false,
    });

    ksBridge.setPhysicalBackButton({
        onClick: async () => {
            if (!hasError) {
                await cancelQrScanLogin({
                    qrLoginToken: query.qrLoginToken,
                });
            }
            ksBridge.exitWebView();
        },
    });

    const confirmBtnEle = document.getElementById('confirm-btn') as HTMLElement;
    const cancelBtnEle = document.getElementById('qr-scanned-page-cancel') as HTMLElement;

    confirmBtnEle.addEventListener('click', async function () {
        if (hasError) {
            ksBridge.exitWebView();
            return;
        }
        if (!canConfirm) {
            return;
        }
        try {
            const accpetRes = await qrAccept({
                qrLoginToken: query.qrLoginToken,
            });
            if (accpetRes.hasUri) {
                window.location.href = `${accpetRes.redirectUri}?${stringify(query)}`;
            } else {
                ksBridge.exitWebView();
            }
        } catch (e) {
            changeErrorState();
        }
    }, false);

    cancelBtnEle.addEventListener('click', async function () {
        if (!hasError) {
            await cancelQrScanLogin({
                qrLoginToken: query.qrLoginToken,
            });
        }
        ksBridge.exitWebView();
    }, false);

    initPage();
}

main();
