<!DOCTYPE html>
<html
    data-git-commit-log="********/周二"
    lang="zh-CN"
>
<head>
    <meta charset="UTF-8">
    <title>passToken result page</title>
    <link rel="icon" href="data:;base64,=">
</head>
<body>
<script>
    // 原 /pc/account/passToken/result 页面的逻辑
    if (location.pathname === '/pc/account/passToken/result') {
        var query = location.search
            .substr(1)
            .split('&')
            .reduce((result, pair) => {
                var list = pair.split('=');
                var key = list[0];
                var value = list[1];
                result[decodeURIComponent(key)] = decodeURIComponent(value);
                return result;
            }, {});
        var data = {
            successful: query.successful,
            failReason: query.failReason,
            for: query.for || '',
            id: query.id,
        };
        window.parent.postMessage(JSON.stringify(data), '*');
    }
</script>
</body>
</html>
