<script lang="ts">
    import Input from "$lib/shared/forms/Input.svelte";
    import { createSmsCodeStore } from "$lib/store/smsStore";
    import Agreements from "$lib/features/Agreements.svelte";
    import Button from "$lib/shared/button/index.svelte";
    import { dialog } from "$lib/shared/popup/dialog";
    import { useToast } from "$lib/shared/toast/toastStore";
    import { EnumErrorType, EnumLoginType } from "$lib/shared/const";
    import { bindOtherPhoneAndLogin, notBindAndLogin, requestCodeByAccountV2 } from "@ks/general-sso";
    import { bindPhoneInfo } from "$lib/features/store";
    import { getContext } from "svelte";



    const onLoginStart = getContext('onLoginStart');
    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    const store = createSmsCodeStore();
    const toast = useToast();
    let loginErrorMsg = '';


    let buttonForbidden = false;
    store.subscribe(() => {
        buttonForbidden = store.reducers.isButtonForbidden();
    });


    async function sendCodeAction() {
        if (!store.reducers.validateSend()) {
            toast.show({
                message: $store.loginErrorMsg,
            });
            return;
        }
        if ($store.countDown > 0) {
            return;
        }
        try {
            await requestCodeByAccountV2({
                type: 1483,
                account: $store.phone,
                countryCode: $store.countryCode,
            });
            sendCodeClicked = true;
            toast.show({
                message: '已发送验证码',
            });
            store.setValues({
                countDown: 60,
            });
            const interal = setInterval(() => {
                if ($store.countDown <= 0) {
                    clearInterval(interal);
                    return;
                }
                store.setValues({
                    countDown: $store.countDown - 1,
                });
            }, 1000);
        } catch (error) {
            loginErrorMsg = error.error_msg || '未知错误，请稍后重试';
            toast.show({
                message: loginErrorMsg,
            });
            onFail?.({
                loginType: EnumErrorType.SendCodeError,
                error,
            });
        }
    }

    let codeClass = '';
    $: {
        if ($store.phone?.length >= 8) {
            codeClass = 'activate';
        } else {
            codeClass = '';
        }
    }

    let sendCodeClicked = false;
    let buttondisabled = false;
    let buttonText = '绑定手机号';

    async function onBind() {
        if (!$store.agreementChecked) {
            toast.show({
                message: '请先同意协议',
            });
            return;
        }
        try {
            onLoginStart({
                name: EnumLoginType.BindB,
            });
            buttondisabled = true;
            const res = await bindOtherPhoneAndLogin({
                phone: $store.phone,
                smsCode: $store.smsCode,
                countryCode: $store.countryCode,
                authToken: $bindPhoneInfo.bindAuthToken,
            });
            buttondisabled = false;
            onSuccess({
                loginType: EnumLoginType.BindB,
                result: res,
            })
        } catch (e) {
            buttondisabled = false;
            loginErrorMsg = e?.error_msg || '未知错误，请稍后重试';
            toast.show({
                message: loginErrorMsg,
            });
            onFail({
                loginType: EnumLoginType.BindB,
                error: e,
            });
        }
    }

    async function notBind() {
        const confirmed = await dialog.confirm({
            title: '是否确认暂不绑定',
            message: '将无法使用手机号登录商家账号',
            cancelText: '取消',
            confirmText: '暂不绑定'
        });

        if (!confirmed) {
            return;
        }
        try {
            onLoginStart({
                name: EnumLoginType.NotBindB,
            });
            buttondisabled = true;
            buttonText = '暂不绑定手机号，登录中';
            const res = await notBindAndLogin($bindPhoneInfo.bindAuthToken);
            buttondisabled = false;
            onSuccess({
                loginType: EnumLoginType.NotBindB,
                result: res,
            });
        } catch (e) {
            buttondisabled = false;
            buttonText = `使用 ${$bindPhoneInfo.phone} 一键绑定`;
            onFail({
                loginType: EnumLoginType.NotBindB,
                error: e,
            });
        }
    }

</script>

<div class="login-body">
    <Input
            placeholder="请输入手机号"
            type="tel"
            bind:value={$store.phone}
            onClear={() => {
                    store.setValues({
                        phone: ''
                    });
                }}
    >
    </Input>
    <Input
            placeholder="请输入验证码"
            bind:value={$store.smsCode}
            maxlength={6}
            type="text"
            inputmode="numeric"
            onClear={() => {
                    store.setValues({smsCode: ''});
                }}
    >
        <slot slot="right">
            <div class="sms-slot-right">
                <div class="send-code"
                     on:click={sendCodeAction}
                >
                    {#if $store.countDown}
                        <span class="text-[#C6C6C6ff]">{$store.countDown}s</span>
                    {:else}
                        <span class={codeClass}>
                            {#if sendCodeClicked}
                                重新发送
                            {:else}
                                获取验证码
                            {/if}
                        </span>
                    {/if}
                </div>
            </div>
        </slot>
    </Input>
    <Agreements
            bind:checked={$store.agreementChecked}
    />
    <Button
            on:click={onBind}
            disabled={buttondisabled}
            forbidden={buttonForbidden || !$store.agreementChecked}
    >
        {buttonText}
    </Button>
    <div class="not-bind"
         on:click={notBind}
    >
        暂不绑定
    </div>
</div>


<style>
    .login-body {
        padding-left: 57px;
        padding-right: 57px;
        width: 100%;
    }
    .send-code {
        white-space: nowrap;
        color: #C6C6C6ff;
        font-size: 13px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 18px;
        text-align: right;
        vertical-align: middle;
    }
    .not-bind {
        color: #9C9C9C;
        font-size: 13px;
        font-weight: 500;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: center;
        vertical-align: top;
        margin-top: 24px;
    }
    .activate {
        color: #326BFBff;
    }
</style>
