{"name": "@ks/sso", "version": "2.10.2-beta.2", "description": "kuaishou-frontend-sso", "author": "kuaishou-fe", "main": "./dist/index.js", "module": "./dist/index.esm.js", "types": "./dist/index.d.ts", "sideEffect": false, "files": ["dist", "README.md", "CHANGELOG.md"], "repository": {"type": "git", "url": "https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general"}, "scripts": {"sdk:dev": "rimraf ./dist && rollup -wc", "sdk:build": "rimraf ./dist && rollup -c && father build", "sdk:clean": "rimraf ./dist", "sdk:publish-to-npm": "npm run 'sdk:build' && if [[ -d ./dist ]]; then pnpm publish; else echo '请先编译出产物，再发包。'; fi", "sdk:install-to-path": "bash scripts/install-to-path.sh", "demo": "npm run build && http-server ./", "lint": "eslint src --ext .ts", "test": "jest", "postinstall": "patch-package", "dev": "npm run sdk:dev", "build": "npm run sdk:build", "publish:beta": "npm run sdk:build && pnpm publish --tag beta", "publish:alpha": "npm run sdk:build && pnpm publish --tag alpha", "publish:latest": "npm run sdk:publish-to-npm"}, "devDependencies": {"@rollup/plugin-json": "^4.1.0", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^23.3.7", "@types/node": "20.5.0", "@types/node-fetch": "^2.1.2", "@types/qs": "^6.5.1", "@typescript-eslint/eslint-plugin": "^4.15.2", "@typescript-eslint/parser": "^4.15.1", "eslint": "^7.20.0", "http-server": "^0.12.3", "jest": "^24.0.19", "rimraf": "^2.6.2", "rollup": "^1.20.3", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.0", "rollup-plugin-visualizer": "^2.5.4", "standard-version": "^7.1.0", "ts-jest": "^24.1.0", "tslib": "^2.8.1", "typescript": "~5.4.0", "father": "4.5.3"}, "dependencies": {"@ks-cqc/device-info": "^1.0.4", "@ks-cqc/h5-sig4-lite-obf": "2.0.5", "@ks-passport/url-kit": "1.0.2-beta.0", "@ks-radar/radar-core": "^1.2.4", "@ks-radar/radar-event-collect": "^1.2.4", "@ks-radar/radar-util": "1.2.15", "@ks/identity-verification": "0.2.17-beta.0", "@ks/weblogger": "^3.0.17", "jsencrypt": "3.2.1", "patch-package": "^8.0.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 9"]}