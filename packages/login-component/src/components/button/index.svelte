<script>
    import {getContext} from "svelte";
    const themeColor = getContext('themeColor');
    export let type = 'default';
    export let size = 'medium';
    export let nativeType = 'button';
    export let radius = false;
    export let className = '';
    export let disabled;
</script>

<button
    type={nativeType}
    on:click
    class="pl-btn pl-btn-{type} pl-btn-{size}
    {className} {themeColor}"
    class:pl-btn-radius={radius}
    {disabled}>
    <slot />
</button>

<style type="text/stylus">
@import '../../common/variables.styl'

btn-font-weight = normal

// button大小控制
btn-small-padding-horizontal = 15px
btn-small-padding-vertical = 7px

btn-medium-padding-horizontal = 25px
btn-medium-padding-vertical = 10px

btn-large-padding-horizontal = 35px
btn-large-padding-vertical = 13px

btn-default-color = font-color-primary
btn-default-bg = #fff
btn-default-border = #e5e5e5

btn-primary-color = #fff
btn-primary-bg = brand-primary
btn-primary-border = darken(btn-primary-bg, 0)

btn-primary-bg-blue = brand-primary-blue
btn-primary-border-blue = darken(btn-primary-bg-blue, 0)

btn-secondary-color = #fff
btn-secondary-bg = brand-secondary
btn-secondary-border = darken(btn-secondary-bg, 0)

btn-success-color = #fff
btn-success-bg = brand-success
btn-success-border = darken(btn-success-bg, 0)

btn-info-color = #fff
btn-info-bg = brand-info
btn-info-border = darken(btn-info-bg, 0)

btn-warning-color = #fff
btn-warning-bg = brand-warning
btn-warning-border = darken(btn-warning-bg, 0)

btn-danger-color = #fff
btn-danger-bg = brand-danger
btn-danger-border = darken(btn-danger-bg, 0)

font-size-large = 14px
font-size-small = 12px
// button mixin
// Button颜色
button-variant(color, background, border)
    color color
    background-color background
    border-color border
    transition all .15s ease-in-out
    &[disabled]
        cursor not-allowed
        background-color rgba(background, .2)
        border-color transparent
        &:hover
            background-color rgba(background, .2)
            border-color transparent

// Button大小
button-size(padding-vertical, padding-horizontal, font-size)
    padding padding-vertical padding-horizontal
    font-size font-size
    line-height 1


.pl-btn
    box-sizing border-box
    display inline-block
    margin-bottom 0
    font-weight btn-font-weight
    text-align center
    vertical-align middle
    cursor pointer
    background-image none
    border 1px solid transparent
    white-space nowrap

    button-size(btn-medium-padding-vertical, btn-medium-padding-horizontal, font-size-small)
    &.pl-btn-radius
        border-radius 9999px

    &:hover
        text-decoration none

    &:hover
    &:focus
        outline none


    // Active state
    &.active,
    &:active
        box-shadow inset 0 2px 4px rgba(0, 0, 0, .15), 0 1px 2px rgba(0, 0, 0, .05)



.pl-btn-primary
    button-variant(btn-primary-color, btn-primary-bg, btn-primary-border)
    &:hover
        background lighten(btn-primary-bg, 10%)
        border-color lighten(btn-primary-border, 10%)
    &.blue
        button-variant(btn-primary-color, btn-primary-bg-blue, btn-primary-border-blue)
    &.blue:hover
        background lighten(btn-primary-bg-blue, 10%)
        border-color lighten(btn-primary-border-blue, 10%)

.pl-btn-secondary
    button-variant(btn-secondary-color, btn-secondary-bg, btn-secondary-border)
    &:hover
        background lighten(btn-secondary-bg, 10%)
        border-color lighten(btn-secondary-border, 10%)

.pl-btn-default
    button-variant(btn-default-color, btn-default-bg, btn-default-border)
    &:hover
        color brand-primary
        border-color brand-primary

.pl-btn-success
    button-variant(btn-success-color, btn-success-bg, btn-success-border)
    &:hover
        background lighten(btn-success-bg, 10%)
        border-color lighten(btn-success-border, 10%)

.pl-btn-info
    button-variant(btn-info-color, btn-info-bg, btn-info-border)
    &:hover
        background lighten(btn-info-bg, 10%)
        border-color lighten(btn-info-border, 10%)

.pl-btn-warning
    button-variant(btn-warning-color, btn-warning-bg, btn-warning-border)
    &:hover
        background lighten(btn-warning-bg, 10%)
        border-color lighten(btn-warning-border, 10%)

.pl-btn-danger
    button-variant(btn-danger-color, btn-danger-bg, btn-danger-border)
    &:hover
        background lighten(btn-danger-bg, 10%)
        border-color lighten(btn-danger-border, 10%)

.pl-btn-large
    button-size(btn-large-padding-vertical, btn-large-padding-horizontal, font-size-large)
    width 100%

.pl-btn-small
    button-size(btn-small-padding-vertical, btn-small-padding-horizontal, font-size-small)

.pl-btn-large-padding      
    padding 10px 35px

.pl-btn-block
    display block
    width 100%
.pl-btn + .pl-btn
    margin-left 20px

.pl-btn-block + .pl-btn-block
    margin-top 10px
</style>
