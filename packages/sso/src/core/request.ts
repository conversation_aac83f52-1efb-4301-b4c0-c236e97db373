import { stringify, getDomainFormUrl } from '@/utils/utils';
import { privateGetConfig } from './configInit';
import { sendEvent } from '@/core/radar';
import { getSig4Url } from './sig4';

const pendingRequest: Record<string, {
    reject: (reason?: any) => void;
    xhr: XMLHttpRequest;
}> = {};

export function cancelRequest(url: string) {
    Object.keys(pendingRequest).forEach(o => {
        // 因为 sig4 会改变 query，所以这里要用startsWith来判断，而不能用等于判断了
        if (o.startsWith(url)) {
            const { reject, xhr } = pendingRequest[o];
            xhr.abort();
            reject('From Cancel Request');
        }
    });
    Object.keys(pendingRequest).forEach(o => {
        if (o.startsWith(url)) {
            delete pendingRequest[o];
        }
    });
}


export function cancelAllRequests (){
    Object.keys(pendingRequest).forEach(url => {
        cancelRequest(url);
    });
}

function addOfflineListener(listener: any) {
    window.addEventListener('offline', listener);
}
function removeOfflineListener(listener: any) {
    window.removeEventListener('offline', listener);
}

/**
 * request请求的回调方法
 */
function requestHookCreator() {
    const reportData: Record<string, any> = {};
    type ResultType = 'SUCC' | 'EXCEPTION' | 'FAILED';
    function getResult(status: 'resolved'| 'rejected', response: any): ResultType {
        // 100110031: 对应多账号的情况的登录
        if (status === 'resolved' || response?.result === 100110031) {
            return 'SUCC';
        }
        if (response?.isXHRError) {
            return 'EXCEPTION';
        }
        return 'FAILED';
    }
    return {
        before(url: string, data: Record<string, any>) {
            Object.assign(reportData, {
                startTime: new Date().getTime(),
                request: data,
                apiUrl: url,
            });
        },
        after(status: 'resolved'| 'rejected', reportType: 'service' | 'catch', response: any) {
            const result: ResultType = getResult(status, response);
            Object.assign(reportData, {
                endTime: new Date().getTime(),
                result,
                response,
                reportType,
            });
            sendEvent({
                name: 'HTTP API',
                message: reportData.apiUrl,
                extra_info: {
                    ...reportData,
                },
            }, {
                duration: reportData.endTime - reportData.startTime,
            });
        }
    }
}

export function request<Response = unknown>(url: string, data: Record<string, any>, method = 'POST') {
    const requestHook = requestHookCreator();
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
        requestHook.before(url, data);
        data.channelType = privateGetConfig()?.channelType;
        if (url.includes('/pass/kuaishou')) {
            data.kuaishouAuth = privateGetConfig()?.kuaishouAuth || undefined;
        }
        if (privateGetConfig()?.enableSig4) {
            data.isWebSig4 = true;
        }
        url = await getSig4Url(url, data);
        const xhr = new XMLHttpRequest();
        xhr.open(method, url);
        xhr.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');
        const customRequestHeader = privateGetConfig().customRequestHeader;
        if (customRequestHeader) {
            Object.entries<string>(customRequestHeader).forEach(function ([key, value]) {
                xhr.setRequestHeader(key, value);
            });
        }
        if (data.encryptHeaders) { // 加密文本的请求头
            Object.entries<string>(data.encryptHeaders).forEach(function ([key, value]) {
                xhr.setRequestHeader(key, value);
            });
        }
        xhr.responseType = 'json';
        xhr.withCredentials = true;
        // 只有异步才能设置timeout
        xhr.onload = () => {
            removeOfflineListener(offlineListener);
            delete pendingRequest[url];
            let res = xhr.response;
            if (typeof res === 'string') {
                // ie 11 xhr.responseType = 'json'; 这个设置是无效的
                // https://github.com/naugtur/xhr/issues/123
                try {
                    res = JSON.parse(res);
                } catch(e) {
                    requestHook.after('rejected', 'catch', e);
                    reject(e);
                }
            }
            const status = xhr.status;
            if (status < 200 && status >= 300 && status !== 304 || res?.result !== 1) {
                const rejectData = {
                    ...res,
                    isXHRError: false,
                    errorReason: 'responseData'
                } as Response;
                requestHook.after('rejected', 'service', rejectData);
                reject(rejectData);
            } else {
                requestHook.after('resolved', 'service', res);
                resolve(res);
            }
        };

        xhr.ontimeout = e => {
            removeOfflineListener(offlineListener);
            const rejectData = {
                ...e,
                isXHRError: true,
                errorReason: 'timeout',
                error_msg: '请求超时',
            };
            requestHook.after('rejected', 'service', rejectData);
            reject(rejectData);
        };
        xhr.onerror = e => {
            removeOfflineListener(offlineListener);
            const rejectData = {
                ...e,
                isXHRError: true,
                errorReason: 'error',
                error_msg: '网络错误',
                error_domain: getDomainFormUrl(url),
            };
            requestHook.after('rejected', 'service', rejectData);
            reject(rejectData);
        }
        function offlineListenerCreator() {
            return () => {
                const rejectData = {
                    isXHRError: true,
                    errorReason: 'error',
                    error_msg: '网络错误',
                    error_domain: getDomainFormUrl(url),
                };
                requestHook.after('rejected', 'service', rejectData);
                reject(rejectData);
            }
        }
        const offlineListener = offlineListenerCreator();
        addOfflineListener(offlineListener);
        pendingRequest[url] = {
            reject,
            xhr,
        };
        const body = {
            ...data,
        };
        const filteredObject = Object.fromEntries(
            Object.entries(body).filter(([key, value]) => key !== 'encryptHeaders' && value !== undefined && value !== null)
        );
        console.log('xhr send', filteredObject);
        xhr.send(stringify(filteredObject));
    }) as Promise<Response>;
}

/**
 * 这块主要处理302相关的req
 */
 export function pageReq(url: string, params: Record<string, any>) {
    if (typeof window !== 'undefined') {
        const targetUrl = `${url}?${stringify(params)}`;
        localStorage.setItem('SSO_PAGE_REQ', targetUrl);
        window.location.href = targetUrl;
    }
}
