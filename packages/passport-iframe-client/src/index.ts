import { stringify } from 'qs';

export interface ConfigParams {
    sid: string;
    kpn?: string;
    env?: string;
    qrType?: string;
    // sso 的baseUrl
    baseUrl?: string;
    /**
     * 默认展示的页面
     */
    indexPage?:
        'login-qrcode' | 'login-password' | 'login-verify-sms'
        | 'register'
        | 'register-qrcode' | 'login-password-qrcode' | 'login-verify-sms-qrcode';
    serviceOwnParams?: Record<string, unknown>;
    handleRegisterByEvent?: boolean;
    /**
     * 显示第三方登录
     */
    showThirdPartLogin?: boolean;
    thirdPartLoginFollowUrl?: string;
    /**
     * 显示登录操作
     */
    showRegister?: boolean;
    /**
     * 右上角显示二维码/输入切换
     */
    showPlatformSwitch?: boolean;
    /**
     * 是否显示右上角关闭
     */
    showCloseIcon?: boolean;
    /**
     * 自定义反馈相关
     */
    appealCustomTitle?: string, // 反馈btn文案
    appealCustomHref?: string, // 反馈btn的跳转链接
    /**
     * 主题色
     */
    themeColor?: 'default' | 'blue',
}

// iframe的id
const IFRAME_CONTAINER_ID = 'passport-login-iframe-container';
// passport-login-iframe的消息type的前缀
const MSG_PREFIX = 'passport-login-iframe-msg-';

// 用户的msg回调函数
// eslint-disable-next-line @typescript-eslint/no-unused-vars
let userCallback = (data: Record<string, unknown>) => {
    console.info('user callback not inited');
};

function handleMessageCallback(event: MessageEvent) {
    const data = event.data;
    if (typeof data?.type?.indexOf === 'function' && data?.type?.indexOf(MSG_PREFIX) !== -1) {
        userCallback(data);
        if (data.type === `${MSG_PREFIX}close`) {
            removePassportLoginIframe();
        }
    }
}

export function injectPassportLoginIframe(url: string, callback: (data: Record<string, unknown>) => unknown, config: ConfigParams):void {
    if (!url) {
        throw 'injectPassportLoginIframe has an invalid login page url';
    }
    if (typeof callback !== 'function') {
        throw 'injectPassportLoginIframe callback must be a function';
    }
    // init userCallback
    userCallback = callback;

    // add message eventlistener
    window.addEventListener('message', handleMessageCallback, false);

    const configObj = {
        sid: config.sid,
        kpn: config.kpn ?? '',
        env: config.env ?? '',
        baseUrl: config.baseUrl ? config.baseUrl : '',
        indexPage: config.indexPage ?? 'login-qrcode',
        qrType: config.qrType ?? '',
        serviceOwnParams: config.serviceOwnParams ? JSON.stringify(config.serviceOwnParams) : '',
        handleRegisterByEvent: config.handleRegisterByEvent ?? '',
        showThirdPartLogin: config.showThirdPartLogin ?? false,
        thirdPartLoginFollowUrl: config.thirdPartLoginFollowUrl ?? '',
        showRegister: config.showRegister ?? true,
        showPlatformSwitch: config.showPlatformSwitch ?? true,
        showCloseIcon: config.showCloseIcon ?? true,
        appealCustomTitle: config.appealCustomTitle,
        appealCustomHref: config.appealCustomHref && encodeURIComponent(config.appealCustomHref),
        themeColor: config.themeColor,
    };
    const targetUrl = `${url}?${stringify(configObj)}`;

    // create container
    const container = document.createElement('div');
    container.id = IFRAME_CONTAINER_ID;
    container.style.position = 'fixed';
    container.style.top = '0';
    container.style.left = '0';
    container.style.right = '0';
    container.style.bottom = '0';
    container.style.zIndex = '1000';
    container.style.backgroundColor = 'rgba(0, 0, 0, 0.6)'

    // create iframe
    const iframe = document.createElement('iframe');
    iframe.src = targetUrl;
    iframe.style.width = '100%';
    iframe.style.height = '100%';
    iframe.style.border = '0';

    // inject to html
    container.appendChild(iframe);
    document.body.appendChild(container);
}

export function removePassportLoginIframe():void {
    const container = document.getElementById(IFRAME_CONTAINER_ID);
    container?.parentNode?.removeChild(container);
    // remove message eventlistener
    window.removeEventListener('message', handleMessageCallback, false);
}
