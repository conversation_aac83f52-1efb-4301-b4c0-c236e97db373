import { request } from '@/core/request';
import { getConfig } from '@/core/configInit';
import { startIdentityVerification } from '@ks/identity-verification';
import { BaseRes } from '../types';

/**
 * 身份认证统一拦截器
 */
export function identityVerificationInterceptor(url: string, params: any): Promise<BaseRes> {
    return request<BaseRes>(url, params).catch((res) => {
        // 返回在400001-410999之间时，视为需要快手验证码，会使用统一身份认证SDK打开验证页面
        if (res.result >= 400001 && res.result <= 410999) {
            return (
                startIdentityVerification({
                    url: res.url!,
                    language: getConfig().language as Parameters<typeof startIdentityVerification>[0]['language'],
                    qrcodeEnv: getConfig().env,
                    platform: getConfig().platform,
                })
                    // 滑块验证通过
                    .then((res) => {
                        if (res.result === -999) {
                            // 行为验证码和端外短信验证出现取消，直接向下透传
                            return Promise.reject(res);
                        }
                        return identityVerificationInterceptor(url, {
                            ...params,
                            ztIdentityVerificationType: res.type,
                            ztIdentityVerificationCheckToken: res.token,
                        });
                    })
                    // 滑块验证失败
                    .catch((res) => {
                        // result=2表示captchaSession 过期，需要重新获取 captchaSession
                        if (res.result === 2) {
                            // 重新获取captchaSession
                            return identityVerificationInterceptor(url, params);
                        }
                        // 其他错误result视为非常规错误，直接抛出由业务方处理
                        return Promise.reject(res);
                    })
            );
        }
        return Promise.reject(res);
    });
}
