<template>
    <Dialog
        popup-class="agreement-popup"
        v-model="isShow"
        :btns="btns"
        transfer-dom
    >
        <div class="content-container">
            <div class="top-title">
                请阅读并同意以下条款
            </div>
            <div class="content">
                <span>为保障您的个人信息安全，请您阅读并同意</span>
                <a :href="servicePageUrl" class="link"> 用户协议 </a>
                <span>和</span>
                <a :href="privacyPageUrl" class="link"> 隐私政策 </a>
            </div>
        </div>
    </Dialog>
</template>

<script lang="ts">
import { Dialog } from '@ks/sharp-ui';
import { defineComponent, computed, SetupContext } from '@vue/composition-api';

interface IProps {
    show: boolean;
}
export default defineComponent({
    name: 'AgreementPopup',
    components: {
        Dialog,
    },
    props: {
        show: Boolean,
        servicePageUrl: {
            type: String,
            required: true,
        },
        privacyPageUrl: {
            type: String,
            required: true,
        },
    },
    setup(props: IProps, vm: SetupContext) {
        const isShow = computed<boolean>({
            get() {
                return props.show;
            },
            set(val) {
                vm.emit('update:show', val);
            },
        });
        const btns = [
            {
                text: '取消',
                handler() {
                    isShow.value = false;
                },
            },
            {
                text: '同意',
                handler() {
                    isShow.value = false;
                    vm.emit('agree');
                },
            },
        ];
        return {
            isShow,
            btns,
        };
    },
});
</script>

<style lang="less" scoped>
.content-container {
    background: #ffffff;
    border-radius: .24rem;
    padding: .48rem .48rem .48rem .48rem;
    display: flex;
    flex-direction: column;
    .top-title {
        font-size: .36rem;
        line-height: .52rem;
        font-weight: 500;
        text-align: center;
        color: #222222;
        margin-bottom: .46rem;
    }
    .content {
        font-size: 0.32rem;
        line-height: 0.48rem;
        .link {
            color: #385080;
        }
    }
}
</style>

<style lang="less">
.agreement-popup {
    .sp-dialog__content {
        width: 5.8rem;
        .sp-dialog__title {
            height: 0;
            padding: 0;
        }
        .sp-dialog__body {
            padding: 0;
        }
    }
}
</style>
