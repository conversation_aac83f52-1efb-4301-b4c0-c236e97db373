<template>
    <Dialog
        popup-class="forget-pwd-dialog-root-container"
        v-model="isShow"
        :btns="btns"
        transfer-dom
    >
        <div class="content-container">
            <div class="top-title">
                忘记密码
            </div>
            <div
                v-for="contentItem in contentList"
                class="content-item"
            >
                <div class="content-title">
                    {{contentItem.title}}
                </div>
                <div class="content-desc">
                    {{contentItem.desc}}
                </div>
            </div>
        </div>
    </Dialog>
</template>

<script lang="ts">
import { Dialog } from '@ks/sharp-ui';
import { defineComponent, computed, SetupContext } from '@vue/composition-api';

interface IProps {
    show: boolean;
}
export default defineComponent({
    name: 'forget-pwd-dialog',
    components: {
        Dialog,
    },
    props: {
        show: Boolean,
    },
    setup(props: IProps, vm: SetupContext) {
        const isShow = computed<boolean>({
            get() {
                return props.show;
            },
            set(val) {
                vm.emit('update:show', val);
            },
        });
        const btns = [
            {
                text: '确定',
                handler() {
                    isShow.value = false;
                },
            },
        ];
        const contentList = [
            {
                title: '已登录快手APP',
                desc: '前往快手APP-设置-账号与安全-设置密码；按指引操作即可',
            },
            {
                title: '未登录快手APP',
                desc: '前往快手APP-登录-帮助-找回账号；按指引操作即可',
            },
        ];
        return {
            isShow,
            btns,
            contentList,
        };
    },
});
</script>

<style scoped lang="less">
.content-container {
    background: #ffffff;
    border-radius: .24rem;
    padding: .48rem .48rem .48rem .48rem;
    display: flex;
    flex-direction: column;
    .top-title {
        height: .44rem;
        line-height: .44rem;
        font-size: .36rem;
        font-family: PingFangSC, PingFangSC-Medium;
        font-weight: 500;
        text-align: center;
        color: #222222;
        margin-bottom: .32rem;
    }
    .content-item {
        & + .content-item {
            margin-top: .32rem;
        }
        .content-title {
            height: .48rem;
            line-height: .48rem;
            font-size: .32rem;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            text-align: left;
            color: #222222;
        }
        .content-desc {
            margin-top: .12rem;
            line-height: .48rem;
            font-size: .32rem;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            text-align: left;
            color: #666666;
        }
    }
}
</style>

<style lang="less">
.forget-pwd-dialog-root-container {
    .sp-dialog__content {
        width: 5.6rem;
        .sp-dialog__title {
            height: 0;
            padding: 0;
        }
        .sp-dialog__body {
            padding: 0;
        }
    }
}
</style>
