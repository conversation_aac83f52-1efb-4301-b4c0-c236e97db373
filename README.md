# account-zt-general

账号中台前端 monorepo

## SDK 流水线发布

| 包名                       | 流水线地址                                                      |
| -------------------------- | --------------------------------------------------------------- |
| @ks/general-sso            | https://halo.corp.kuaishou.com/devcloud/pipeline/history/465310 |
| @ks/identity-verification  | https://halo.corp.kuaishou.com/devcloud/pipeline/history/465373 |
| @ks/login-component        | https://halo.corp.kuaishou.com/devcloud/pipeline/history/465560 |
| @ks/passport-iframe-client | https://halo.corp.kuaishou.com/devcloud/pipeline/history/465562 |
| @mfe/sms-verification      | https://halo.corp.kuaishou.com/devcloud/pipeline/history/465568 |
| @ks/sso                    | https://halo.corp.kuaishou.com/devcloud/pipeline/history/465363 |
| @ks/sso-logger             | https://halo.corp.kuaishou.com/devcloud/pipeline/history/465569 |


## 项目目录结构
- apps monorepo里面各个服务的app，理论上一个服务只对应其中的一个
    - account-zt-pc 账号 pc 版本页面
    - account-pc-server 账号 pc 版本 node 服务，`account-zt-pc` 会 打包到这个服务里
    - account-zt-h5 账号 h5 版本页面
    - account-unified-h5 校验中台页面
        - [X] 语音短信账号页面
        - [ ] 实名认证 WIP
        - [ ] 单人脸核验 WIP
        - [ ] 青少年模式

- packages 公用的一些包和项目，是apps依赖的
    - general-sso 新版通用sso登录
    - identity-verification 用户认证
    - login-component 登录组件
    - sso sso登录

## 本地开发流程
1. 在根目录执行yarn
2. 执行 yarn livepc:dependencies:build，这步是编译packages里面的依赖
3. cd到app目录进行启动，或者配置script在根目录启动(需要node版本 v16.)

## 服务编译配置
在https://halo.corp.kuaishou.com/devcloud/pipeline/mine配置相关构建时，触发设置选择手动，同时需要删除自动里面的相关配置内容，后续上线时进行手动触发项目构建


目前出现了构建缓慢的现象，由于依赖比较多，目前方案优化了一下构建脚本，增加了$2参数（目前看只有live-next需要$2 依赖了tube和mp）
build.sh app dep1_dep2_dep3...
构建之前先把其他不涉及的app删除掉
理论上一个服务只是一个apps里面的项目，目前live-next服务会依赖apps里面的其他app，先临时通过这个方案解决

## 各业务测试入口梳理

[文档](https://docs.corp.kuaishou.com/d/home/<USER>
