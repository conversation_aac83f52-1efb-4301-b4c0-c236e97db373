<template>
    <div></div>
</template>

<script lang="ts">
import { defineComponent, computed } from '@vue/composition-api';
import { thirdPartLogin, init } from '@ks/sso';
import { startIdentityVerification } from '@ks/identity-verification';
import { sendRadarFmp, sendRadarCustomEvent } from '@/common/js/log';

const APP_PREFIX = 'passport_';
const emptyObj = {};

export default defineComponent({
    setup(props, { root }: { root: any }) {
        sendRadarFmp();
        const queryData = root.$route.query.data;
        const redirect = computed(() => {
            return !queryData;
        });


        const queryFollowUrl = root.$route.query.followUrl;

        const goToThirdPart = async (data?: {
            captchaConfig: string;
        }) => {
            const originAndPath = location.href.split('?')[0];
            const followUrl = queryFollowUrl
                // 后端会对 callback 和 followUrl 做域名校验，前端就不做校验了
                ? `${originAndPath}?callback=${encodeURIComponent(queryFollowUrl)}&followUrl=${encodeURIComponent(queryFollowUrl)}`
                : originAndPath;

            if (!data) {
                // 跳转到帐号中台后端接口，进行第三方登录
                return thirdPartLogin({
                    type: (APP_PREFIX + root.$route.params.type) as 'passport_qq' | 'passport_wechat',
                    followUrl,
                });
            }

            // From 2.1、若命中验证码，则先验证码后再次进行第三方登录（这一步绝大多数情况下不会有）
            const { captchaConfig } = data;
            const res = await startIdentityVerification({
                url: captchaConfig,
            });

            if (res.result === 1) {
                return thirdPartLogin({
                    type: APP_PREFIX + root.$route.params.type as 'passport_qq' | 'passport_wechat',
                    followUrl,
                    captchaToken: res.token,
                });
            }

            // 若验证出问题（验证多次未通过、取消），则关闭页面
            return window.close();
        };

        init({
            sid: root.$route.query.sid,
            // TODO 这块后面改一下
            env: 'production',
            // env: process.env.NODE_ENV as 'development' | 'production',
        });

        if (redirect.value) {
            console.log('third-part-login11');
            // 第 1 步、首次访问该页面，请求帐号中台后端 api 进行第三方登录
            goToThirdPart();
            return emptyObj;
        }

        /**
         * 第三方登录成功/失败后会再次 302 到该页面，携带 data 参数
         * 1、若登录成功，data 解析后的结构为：{ authToken, result: 1, sid }
         * 2、若登录失败，比如 callback 不合法（调用 thirdPartLogin 方法里调用的 /pass/kuaishou/login/sns/auth 时会校验），data 的结构为：
         * {
         *     result:*********,
         *     error_msg:"检测到跳转地址不合法，请访问该链接申请添加callback白名单:
         *         https://passport-portal.corp.kuaishou.com/#/apply/sidWhitelist?sid=kuaishou.account.test&callBackUrl=https://www.baidu.com"
         * }
         * 此时没有 authToken
         */
        try {
            // 第 2 步、对第三方登录结果进行处理
            const data = JSON.parse(queryData);

            if (data.result === *********) {
                // 开启了账号保护
                const path = '/pc/account/login-protect';
                location.replace(location.origin + path);
            } else if (data.captchaConfig) {
                // 2.1、可能会命中风控，让用户进行验证后重新走第一步
                goToThirdPart(data);
                return emptyObj;
            }

            // query 上有 followUrl 参数时，跳转过去；否则，停留在该空白页面吧
            if (queryFollowUrl) {
                location.href = decodeURIComponent(queryFollowUrl);
            }
        } catch (e) {
            sendRadarCustomEvent({
                name: 'third-part-login-parse-data',
                event_type: 'error',
                extra_info: JSON.stringify({
                    data: queryData,
                }),
            });
            console.error('parse data error');
        }

        return emptyObj;
    },
});
</script>
