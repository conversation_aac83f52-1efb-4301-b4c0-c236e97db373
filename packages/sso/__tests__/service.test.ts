import services, {privateGetConfig, InitParam} from '../src/services';

describe('services should', () => {
    test('init config and baseUrl with initParams', () => {
        const initParam: InitParam = {
            sid: 'kuaishou.live.web',
            env: 'production',
        };
        const initConfig = {
            sid: 'kuaishou.live.web',
            baseUrl: 'https://id.kuaishou.com',
            callback: 'https://live.kuaishou.com/',
        };
        services.init(initParam);
        expect(privateGetConfig()).toEqual(initConfig);
    });
});

// TODO 兼容性
// 还是需要手动测试
