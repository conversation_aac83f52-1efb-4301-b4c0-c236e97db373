<script lang="ts">
    import Button from "$lib/shared/button/index.svelte";
    import QuickBindConflict from "$lib/features/bind/QuickBindConflict.svelte";
    import BindOther from "$lib/features/bind/BindOther.svelte";
    import ConfirmDialog from "$lib/shared/popup/ConfirmDialog.svelte";
    import { dialog } from "$lib/shared/popup/dialog";
    import { phoneQuickBindAndLogin, notBindAndLogin } from "@ks/general-sso";
    import { bindPhoneInfo, bindConflictInfo } from "$lib/features/store";
    import { getContext } from "svelte";
    import { CODE_BIND_CONFLICT, EnumLoginType } from "$lib/shared/const";
    import { useToast } from "$lib/shared/toast/toastStore";


    const onLoginStart = getContext('onLoginStart');
    const onSuccess = getContext('onSuccess');
    const onFail = getContext('onFail');
    const authorizeLogoUrl = getContext('authorizeLogoUrl');

    enum EnumBindStatus {
        QuickBind = 'QuickBind',
        BindConflict = 'BindConflict',
        BindOther = 'BindOther',
    }

    let buttondisabled = false;

    let bindStatus = EnumBindStatus.QuickBind;

    let buttonText = `使用 ${$bindPhoneInfo.phone} 一键绑定`;
    let loginErrorMsg = '';
    const toast = useToast();

    async function onQuickBind() {
        try {
            onLoginStart({
                name: EnumLoginType.QuickBindB,
            });
            const res = await phoneQuickBindAndLogin($bindPhoneInfo.bindAuthToken);
            onSuccess({
                loginType: EnumLoginType.QuickBindB,
                result: res,
            });
        } catch (e) {
            if (e?.result === CODE_BIND_CONFLICT) {
                bindStatus = EnumBindStatus.BindConflict;
                bindConflictInfo.set({
                    authToken: e?.authToken,
                    hintInfo: e?.hintInfo,
                });
                return;
            }
            loginErrorMsg = e?.error_msg || '未知错误，请稍后重试';
            toast.show({
                message: loginErrorMsg,
            });
            onFail({
                loginType: EnumLoginType.QuickBindB,
                error: e,
            });
        }
    }

    function onBindOther() {
        bindStatus = EnumBindStatus.BindOther;
    }

    function updateBindStatus(event) {
        bindStatus = event.detail;
    }

    async function notBind() {
        const confirmed = await dialog.confirm({
            title: '是否确认暂不绑定',
            message: '将无法使用手机号登录商家账号',
            cancelText: '取消',
            confirmText: '暂不绑定'
        });

        if (!confirmed) {
            return;
        }
        try {
            onLoginStart({
                name: EnumLoginType.NotBindB,
            });
            buttondisabled = true;
            buttonText = '暂不绑定手机号，登录中';
            const res = await notBindAndLogin($bindPhoneInfo.bindAuthToken);
            buttondisabled = false;
            onSuccess({
                loginType: EnumLoginType.NotBindB,
                result: res,
            });
        } catch (e) {
            buttondisabled = false;
            buttonText = `使用 ${$bindPhoneInfo.phone} 一键绑定`;
            loginErrorMsg = e?.error_msg || '未知错误，请稍后重试';
            toast.show({
                message: loginErrorMsg,
            });
            onFail({
                loginType: EnumLoginType.NotBindB,
                error: e,
            });
        }
    }

    let imgsrc = 'lib/assets/IconLogo.png';
    console.log('authorizeLogoUrl', authorizeLogoUrl);
    if (authorizeLogoUrl) {
        imgsrc = authorizeLogoUrl as string;
    }

</script>

<div class="container">
    {#if bindStatus === EnumBindStatus.QuickBind || bindStatus === EnumBindStatus.BindOther}
        <img class="icon" src={imgsrc} alt="" />
        <div class="title">
            商家账号绑定手机号
        </div>
        <div class="desc">
            首次通过快手APP账号授权登录商家账号，需要绑定手机号
        </div>
        {#if bindStatus === EnumBindStatus.QuickBind}
            <div class="px-[57px] w-full">
                <Button
                        disabled={buttondisabled}
                        class="mb-[12px] py-[16px]"
                        on:click={onQuickBind}
                >
                    {buttonText}
                </Button>
                <Button
                        disabled={false}
                        class="text-[black] py-[15px] !bg-[#FFFFFF] border-[#E6E6E6ff] border mb-[16px]"
                        on:click={onBindOther}
                >
                    绑定其他手机号
                </Button>
            </div>
            <div class="not-bind"
                 on:click={notBind}
            >
                暂不绑定
            </div>
        {:else}
            <BindOther />
        {/if}

    {:else if bindStatus === EnumBindStatus.BindConflict}
        <QuickBindConflict
                on:updateBindStatus={updateBindStatus}
        />
    {/if}
</div>
<ConfirmDialog />

<style>
    .container {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    .icon {
        width: 60px;
        height: 60px;
        margin-bottom: 24px;
        margin-top: 40px;
    }
    .title {
        color: #000000;
        font-size: 24px;
        font-weight: 500;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 28px;
        text-align: center;
        vertical-align: middle;
        margin-bottom: 24px;
    }
    .desc {
        color: #646B73;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 22px;
        text-align: center;
        vertical-align: top;
        margin-bottom: 40px;
    }
    .not-bind {
        color: #9C9C9C;
        font-size: 13px;
        font-weight: 500;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: center;
        vertical-align: top;
    }
</style>
