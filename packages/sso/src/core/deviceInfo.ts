// deviceInfo.ts
import KsDeviceInfo from '@ks-cqc/device-info'
import { ICollectConfig } from '../types/common'

let collector: KsDeviceInfo | null = null;
// 初始化：根据需要启用或禁用特定功能（传感器数据采集、用户行为跟踪，木马扫描）。如果不提供任何配置参数，所有功能默认禁用
export function initKsDeviceInfo(config: ICollectConfig): void {
  collector = new KsDeviceInfo(config)
}
// 使用 collectDeviceInfo 方法来收集设备信息
export function getDeviceInfo(): Promise<string> {
    return collector?.collectDeviceInfo()
}
