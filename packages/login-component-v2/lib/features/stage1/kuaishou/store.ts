import { writable } from 'svelte/store';

export const multiUserInfo = writable({
    userInfos: [],
    multiUserToken: '',
    phone: '',
    // 登录方式
    loginType: '',
    countryCode: '+86',
});

export const bindPhoneInfo = writable<{
    phone: string;
    bindAuthToken: string;
    quickLoginResponse?: {
        [key: string]: any;
    };
}>({
    phone: '',
    bindAuthToken: '',
});

export const bindConflictInfo = writable<{
    authToken: string;
    hintInfo: {
        bidBindInfo: {
            userId: number;
            nickName: string;
            blurryPhone: string;
            icons: Array<string>;
        };
        oldKsBindInfo: {
            userId: number;
            nickName: string;
        };
        newKsBindInfo: {
            userId: number;
            nickName: string;
        }
    }
}>({
    authToken: '',
    hintInfo: {
        bidBindInfo: {
            userId: 0,
            nickName: '',
            blurryPhone: '',
            icons: [],
        },
        oldKsBindInfo: {
            userId: 0,
            nickName: '',
        },
        newKsBindInfo: {
            userId: 0,
            nickName: '',
        }
    }
});
