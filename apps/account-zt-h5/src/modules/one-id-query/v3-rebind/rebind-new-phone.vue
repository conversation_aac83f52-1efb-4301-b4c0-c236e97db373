<template>
    <div class="one-id-query-container">
      <div class="tips" v-if="!isRebindOkay">
        更换手机号，下次登录使用新的手机号登录。当前绑定手机号 +86{{ phone }}。
      </div>
      <VerifyViaSMS
        v-if="!isRebindOkay"
        phone-placeholder="请输入新手机号码"
        :biz-type="bizType"
        @confirm="onConfirm"
      />
      <div class="result-okay" v-if="isRebindOkay">
        <img class="logo-icon" src="@/assets/kwai.png"/>
        <sp-icon class="okay-icon" name="right" color="green"></sp-icon>
        <div>手机号码换绑成功</div>
      </div>
    </div>
</template>

<script lang="ts">
import VerifyViaSMS from '@/modules/one-id-query/components/verify-via-sms/VerifyViaSMS.vue';
import { rebindForPhone } from '@/api/one-id-query';
import { Toast, Icon } from '@ks/sharp-ui';
import { ONE_ID_QUERY_SID, BIZ_TYPE_REBIND, LOGIN_STATUS_INVALID } from '@/common/config/constants';
import { defineComponent } from '@vue/composition-api';

export default defineComponent({
  components: { VerifyViaSMS, SpIcon: Icon },

  computed: {
    phone() {
      const { phone = '***********' } = (this as any).$route.query;
      return phone.replace(/(\d{3})\d{4}(\d{4})/g, '$1****$2');
    },
    bizType() {
      return BIZ_TYPE_REBIND;
    }
  },

  data() {
    return {
      isRebindOkay: false,
    };
  },

  methods: {
    onConfirm(data: any) {
      rebindForPhone({
        sid: ONE_ID_QUERY_SID,
        countryCode: data.countryCode,
        phone: data.phone,
        smsCode: data.smsCode,
      })
        .then(res => {
          console.log('rebindForPhone OK', res);
          (this as any).isRebindOkay = true;
          Toast.info('账号换绑成功！');
        })
        .catch(err => {
          console.log('rebindForPhone Error', err);
          if (LOGIN_STATUS_INVALID.includes(err?.result)) {
            Toast.error('换绑失败，请重试！');
            (this as any).$router.push('/one-id-query/v3/rebind');
            return;
          }
          Toast.error(err?.error_msg || '换绑失败，请检查网络后再试！');
        });
    }
  }
});
</script>

<style scoped lang="less">
@import "../common/styles.less";

.tips {
  margin-top: ;
  padding: .5em 1.12em;
  color: #666;
}

.result-okay {
  display: flex;
  height: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 0.46rem;

  .okay-icon {
    width: 1rem;
    margin-bottom: 0.5rem;
  }

  .logo-icon {
    width: 3.2rem;
    margin-bottom: 3rem;
  }
}
</style>
