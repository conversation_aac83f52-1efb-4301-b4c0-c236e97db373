@import "../common/styles.less";

.phoneNumber {
  color: #222;
  text-align: center;
  font-family: PingFang SC;
  font-size: 1.625em;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  margin-top: -0.1875em;
}

.gray-text {
  color: #9C9C9C;
  font-feature-settings: 'clig' off, 'liga' off;
  font-family: PingFang SC;
  font-size: 0.875em;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.black-text {
  color: #222;
  font-family: PingFang SC;
  font-size: 0.875em;
  font-style: normal;
  font-weight: 400;
  line-height: 1.25em;
}

.unbound-btn {
  color: #222;
  opacity: .5;
}

.unbind-btn {
  color: #FE3666;

  &:hover {
    opacity: .8;
  }

  &:active {
    opacity: .618;
    cursor: pointer;
  }
}

.unbind-btn {
  width: 5.3333em;
  height: 2.66667em;
}

.unbound-btn {
  width: 6em;
  height: 2.66667em;
  border-color: #EAEAEA !important;
}

.unbind-btn, .unbound-btn {

  display: inline-flex;
  justify-content: center;
  align-items: center;
  padding: 0.46875em 0.75em;

  border-radius: 1.33333em;
  border: 1px solid;

  text-align: center;
  font-feature-settings: 'clig' off, 'liga' off;

  /* 八级12-辅助信息、小按钮文本/中粗|font_grade_8_medium */
  font-family: PingFang SC;
  font-size: 0.75em;
  font-style: normal;
  font-weight: 500;
  line-height: 1.0625em; /* 141.667% */

}

table.bind-info-table {
  width: 100%;

  tr {

    &:first-child {
      td {
        padding-bottom: .1875em;
      }
    }

    td.nick-name-td {
      width: 4.5em;
      white-space: nowrap;
    }

    td.unbind-btn-td {
      text-align: right;
      width: 5em;
    }
  }
}
