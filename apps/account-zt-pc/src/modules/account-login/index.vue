<template>
    <HomeScaffold @pageInfo="handlePageInfo">
        <main ref="refLoginForm" id="login-form" class="login-main"></main>
    </HomeScaffold>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed, watch } from '@vue/composition-api';
import { renderComponent } from '@ks/login-component';
import type { AuthTokenResult } from '@ks/sso';
import { PageInfo } from '@/services/login';
import { webLog, sendRadarFmp } from '@/common/js/log';
import {
    updateLoggerLoginChannel,
    LoginChannelType,
    redirectTo,
    parseUrl,
    paramsToString,
    isUrlValid,
    isCallbackValid,
} from '@/common/js/util';
import HomeScaffold from '@/components/home-scaffold.vue';
import { IS_PROD } from '@/common/js/const';

const indexPageWhiteList = [
    'login-password',
    'login-verify-sms',
    'login-qrcode',
    'register',
];

// 更新login用法为pc登录页
updateLoggerLoginChannel(LoginChannelType.PC_PAGE);

export default defineComponent({
    components: {
        HomeScaffold,
    },
    setup() {
        const refLoginForm = ref<HTMLElement>();
        const query = parseUrl(location.href).paramObj;
        const querySid = query.sid || 'kuaishou.web.api';
        const queryStsURL = query.callback || '';
        const queryRedirectURL = query.redirectURL || '';
        const isQueryRedirectURLValid = isUrlValid(query.redirectURL);
        const queryIndexPage = indexPageWhiteList.includes(query.indexPage) ? query.indexPage : 'login-password';
        const thirdPartLoginFollowUrl = isQueryRedirectURLValid ? query.redirectURL : 'https://www.kuaishou.com';

        const data = ref<PageInfo>();

        const showThirdPartLogin = computed(() => !!data.value?.thirdPartyLogin);
        const callbackUrls = computed(() => data.value?.callbackUrls || []);

        /**
         * 获取 种 Cookie 的URL地址
         *
         * callback 有 followURL 参数需要特殊处理，见下表
         * | callback |  res.stsURL | 处理完:followURL  | return |
         * | --------- | -----------| -------------| --------------------------------- |
         * | 有followURL | 被callback覆盖 | '/' 默认 | callback + param {sid, authToken} |
         * | 有followURL | 被callback覆盖 | 有具体值  | callback + param {sid, authToken, followURL} |
         * | 米有followURL | 被callback覆盖 | '/' 默认 | callback + param {sid, authToken, followURL} |
         * | 米有followURL | 被callback覆盖 | 有具体值  | callback + param {sid, authToken, followURL |
         * | 无值         | 无followURL   | 任意         | stsURL + param {sid, authToken, followURL} |
         * @param {string} stsURL 下一跳默认地址 URL
         * @param redirectURL stsURL 后第二跳地址
         * @param commonParams 公共 sid authToken 参数
         * @return {string} 下一跳需要跳转的地址
         */
        const getSetCookieURL = (stsURL: string, redirectURL: string, commonParams: object) => {
            const {
                baseUrl,
                paramObj,
            } = parseUrl(stsURL);
            const stsURLHasFollowURL = !!paramObj.followUrl;
            const param = {
                ...paramObj,
                ...commonParams,
                ...(
                    stsURLHasFollowURL && !isUrlValid(redirectURL)
                        ? {}
                        : {
                            followUrl: redirectURL || '/',
                        }
                ),
            };
            return `${baseUrl}?${paramsToString(param)}`;
        };

        const asyncRenderComponent = (): Promise<AuthTokenResult> => {
            return new Promise(resolve => {
                const param = {
                    env: IS_PROD ? 'production' : 'staging',
                    sid: querySid,
                    target: refLoginForm.value!,
                    onSuccess(res: {
                        result: AuthTokenResult;
                        loginType: string;
                    }) {
                        resolve(res.result);
                    },
                    enableSig4: true,
                    logger: webLog,
                    indexPage: queryIndexPage,
                    showThirdPartLogin: showThirdPartLogin.value,
                    thirdPartLoginFollowUrl: thirdPartLoginFollowUrl,
                    channelType: LoginChannelType.PC_PAGE,
                } as const;
                renderComponent(param);
            });
        };

        const renderLoginComponent = async () => {
            const {
                sid,
                authToken,
                stsUrl: resStsURL,
                followUrl,
            } = await asyncRenderComponent();

            // 优先使用页面 callback 参数，没有或无效的话，再使用接口返回的 stsUrl
            const stsUrl: string = isCallbackValid(
                queryStsURL,
                callbackUrls.value.concat('.kproxy.corp.kuaishou.com'), // KProxy 域名放行。
            ) ? queryStsURL : (resStsURL || '');

            /**
             * 如果有返回 stsUrl （业务方指定的 set 登录 cookie 的接口），说明当前是属于中转登录的case；
             * 业务方跳转到本中转登录页，用户登录完之后，带上返回的authToken访问stsUrl，在目标域名下的种上登录cookie
             */
            if (stsUrl) {
                // 优先使用页面 query 上的 redirectURL，后使用接口返回的 followUrl
                const redirectURL = isQueryRedirectURLValid ? queryRedirectURL : (followUrl || '');

                const setCookieUrl = getSetCookieURL(
                    stsUrl,
                    redirectURL,
                    {
                        sid,
                        authToken,
                    },
                );

                redirectTo(setCookieUrl);
            } else {
                alert('跳转域名或路径不在白名单内, 请确认跳转域名已在账号后端加白。');
            }
        };

        onMounted(async () => {
            if (data.value) {
                renderLoginComponent();
            } else {
                const unwatch = watch(data, (val, oldVal) => {
                    if (val && !oldVal) {
                        renderLoginComponent();
                        unwatch();
                    }
                });
            }
        });

        return {
            refLoginForm,
            handlePageInfo(pageInfo: PageInfo) {
                data.value = pageInfo;
                sendRadarFmp();
            },
        };
    },
});
</script>

<style lang="stylus" scoped>
.login-main {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translate(0,-50%);
    width: 380px;
    min-height: 200px;
    box-shadow: 0 8px 20px -4px rgba(74,83,93,0.12);
    background: #fff
}

@media (max-width:1180px) {
    .login-main {
        margin-right: 10px
    }
}
</style>
