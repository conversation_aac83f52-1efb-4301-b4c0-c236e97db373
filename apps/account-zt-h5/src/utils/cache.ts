const localstorageKey = 'loginStatus';

export enum WAYS {
    password,
    verifycode,
    wx,
}

export interface LOGININFO {
    phone: string;
    code: string;
    password: string;
    agreement: number;
    way: WAYS;
}

export function get() {
    const cache = sessionStorage.getItem(localstorageKey);
    if (!cache) {
        return {};
    }
    return JSON.parse(cache);
}

export function update(loginInfo: LOGININFO) {
    let cache: any = sessionStorage.getItem(localstorageKey);
    if (cache) {
        cache = JSON.parse(cache);
        Object.assign(cache, loginInfo);
        sessionStorage.setItem(localstorageKey, JSON.stringify(cache));
    } else {
        sessionStorage.setItem(localstorageKey, JSON.stringify(loginInfo));
    }
}

// export function remove(userInfo) {
//     let cache = sessionStorage.getItem(localstorageKey);
//     if (cache) {
//         cache = JSON.parse(cache);
//         cache.some((info, index) => {
//             if (info.id === userInfo.id) {
//                 cache.splice(index, 1);
//                 return true;
//             }
//             return false;
//         });
//         sessionStorage.setItem(localstorageKey, JSON.stringify(cache));
//     }
// }

// export function clear() {
//     sessionStorage.setItem(localstorageKey, JSON.stringify([]));
// }
