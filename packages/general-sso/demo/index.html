<!DOCTYPE html>
<html>

<head>
    <meta charset="utf8">
    <script type="module" src="index.js"></script>
</head>

<body>
    <fieldset>
        <legend>发送短信验证码</legend>
        <form onsubmit="requestSmsCode(event); return false;">
            <label>phone:
                <input type="text" name="phone" value="18511756266">
            </label>
            <label>countryCode:
                <input type="text" name="countryCode" value="+86">
            </label>
            <label>type:
                <input type="text" name="type" value="3424">
            </label>
            <button type="submit">发送</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>短信验证码登录</legend>
        <form onsubmit="smsCodeLogin(event); return false;">
            <label>phone:
                <input type="text" name="phone" value="18511756266">
            </label>
            <label>countryCode:
                <input type="text" name="countryCode" value="+86">
            </label>
            <label>smsCode:
                <input type="text" name="smsCode" value="0001">
            </label>
            <label>registerOpen:
                <input type="radio" name="registerOpen" value="false" checked>false
                <input type="radio" name="registerOpen" value="true">true
            </label>
            <button type="submit">登录</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>密码登录</legend>
        <form onsubmit="passwordLogin(event); return false;">
            <label>phone:
                <input type="text" name="phone" value="18511756266">
            </label>
            <label>countryCode:
                <input type="text" name="countryCode" value="+86">
            </label>
            <label>password:
                <input type="text" name="password" value="general-sso-password">
            </label>
            <button type="submit">登录</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>多帐户情况下选择用户</legend>
        <form onsubmit="chooseUser(event); return false;">
            <label>phone:
                <input type="text" name="phone" value="18511756266">
            </label>
            <label>countryCode:
                <input type="text" name="countryCode" value="+86">
            </label>
            <label>userId:
                <input type="text" name="userId" value="">
            </label>
            <label>multiUserToken:
                <input type="text" name="multiUserToken" value="">
            </label>
            <button type="submit">选择用户登录</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>同步token</legend>
        <form onsubmit="passToken(event); return false;">
            <label>sid:
                <input type="text" name="sid" value="">
            </label>
            <button type="submit">同步</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>通过短信验证码重置密码</legend>
        <form onsubmit="resetPasswordByPhone(event); return false;">
            <label>phone:
                <input type="text" name="phone" value="18511756266">
            </label>
            <label>countryCode:
                <input type="text" name="countryCode" value="+86">
            </label>
            <label>smsCode:
                <input type="text" name="smsCode" value="0001">
            </label>
            <label>password:
                <input type="text" name="password" value="">
            </label>
            <span>setCookie:
                <label><input type="radio" name="setCookie" value="1" checked="checked">true</label>
                <label><input type="radio" name="setCookie" value="">false</label>
            </span>
            <button type="submit">重置密码</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>退出登录</legend>
        <form onsubmit="logout(event); return false;">
            <button type="submit">登出</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>手机号+短信验证码+密码注册</legend>
        <form onsubmit="smsCodeRegister(event); return false;">
            <label>phone:
                <input type="text" name="phone" value="18511756266">
            </label>
            <label>countryCode:
                <input type="text" name="countryCode" value="+86">
            </label>
            <label>smsCode:
                <input type="text" name="smsCode" value="0001">
            </label>
            <label>password:
                <input type="text" name="password" value="">
            </label>
            <span>setCookie:
                <label><input type="radio" name="setCookie" value="1" checked="checked">true</label>
                <label><input type="radio" name="setCookie" value="">false</label>
            </span>
            <button type="submit">注册</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>注册邮箱</legend>
        <form onsubmit="emailRegister(event); return false;">
            <label>email:
                <input type="text" name="email" value="">
            </label>
            <label>password:
                <input type="text" name="password" value="">
            </label>
            <label>emailCode:
                <input type="text" name="emailCode" value="">
            </label>
            <span>setCookie:
                <label><input type="radio" name="setCookie" value="1" checked="checked">true</label>
                <label><input type="radio" name="setCookie" value="">false</label>
            </span>
            <button type="submit">注册</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>邮箱+密码登录</legend>
        <form onsubmit="emailPasswordLogin(event); return false;">
            <label>email:
                <input type="text" name="email" value="">
            </label>
            <label>password:
                <input type="text" name="password" value="">
            </label>
            <button type="submit">登录</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>发送邮箱验证码</legend>
        <form onsubmit="requestEmailCode(event); return false;">
            <label>email:
                <input type="text" name="email" value="">
            </label>
            <label>type:
                <input type="text" name="type" value="">
            </label>
            <button type="submit">发送</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>通过邮箱重置密码</legend>
        <form onsubmit="resetPasswordByEmail(event); return false;">
            <label>email:
                <input type="text" name="email" value="">
            </label>
            <label>password:
                <input type="text" name="password" value="">
            </label>
            <label>emailCode:
                <input type="text" name="emailCode" value="">
            </label>
            <span>setCookie:
                <label><input type="radio" name="setCookie" value="1" checked="checked">true</label>
                <label><input type="radio" name="setCookie" value="">false</label>
            </span>
            <button type="submit">重置密码</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>检查密码强度</legend>
        <form onsubmit="checkPassStrength(event); return false;">
            <label>password:
                <input type="text" name="password" value="">
            </label>
            <button type="submit">检查</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>检查手机号是否可用</legend>
        <form onsubmit="checkPhoneAvailability(event); return false;">
            <label>phone:
                <input type="text" name="phone" value="">
            </label>
            <label>countryCode:
                <input type="text" name="countryCode" value="+86">
            </label>
            <button type="submit">检查</button>
        </form>
    </fieldset>
    <fieldset>
        <legend>检查邮箱是否可用</legend>
        <form onsubmit="checkEmailAvailability(event); return false;">
            <label>email:
                <input type="text" name="email" value="">
            </label>
            <button type="submit">检查</button>
        </form>
    </fieldset>
</body>

</html>
