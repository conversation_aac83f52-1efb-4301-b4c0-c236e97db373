import { stringify } from '@/utils';
import { getConfig } from '@/core/configInit';
import { sendEvent } from '@/core/radar';
import { getSig4Url } from "@/core/sig4";

let baseUrl = '/';
export function setBaseUrl(base: string) {
    baseUrl = base;
}

const pendingRequest: Record<
    string,
    {
        reject: (reason?: any) => void;
        xhr: XMLHttpRequest;
    }
> = {};

export function cancelRequest(url: string) {
    Object.keys(pendingRequest).forEach(o => {
        // 因为 sig4 会改变 query，所以这里要用startsWith来判断，而不能用等于判断了
        if (o.startsWith(url)) {
            const { reject, xhr } = pendingRequest[o];
            xhr.abort();
            reject('From Cancel Request');
        }
    });
    Object.keys(pendingRequest).forEach(o => {
        if (o.startsWith(url)) {
            delete pendingRequest[o];
        }
    });
}

export function cancelAllRequests (){
    Object.keys(pendingRequest).forEach(url => {
        cancelRequest(url);
    });
}

/**
 * request请求的回调方法
 */
function requestHookCreator() {
    const reportData: Record<string, any> = {};
    type ResultType = 'SUCC' | 'EXCEPTION' | 'FAILED';
    function getResult(status: 'resolved'| 'rejected', response: any): ResultType {
        // 100110031: 对应多账号的情况的登录
        if (status === 'resolved' || response?.result === 100110031) {
            return 'SUCC';
        }
        if (response?.isXHRError) {
            return 'EXCEPTION';
        }
        return 'FAILED';
    }
    return {
        before(url: string, data: Record<string, any>, method: string) {
            console.log('before time', Date.now());
            Object.assign(reportData, {
                startTime: new Date().getTime(),
                request: data,
                apiUrl: url,
                method,
            });
        },
        after(status: 'resolved'| 'rejected', reportType: 'service' | 'catch', response: any) {
            console.log('before time', Date.now());
            const result: ResultType = getResult(status, response);
            Object.assign(reportData, {
                endTime: new Date().getTime(),
                result,
                response,
                reportType,
            });
            sendEvent({
                name: 'HTTP API',
                message: reportData.apiUrl,
                extra_info: {
                    ...reportData,
                },
            }, {
                duration: reportData.endTime - reportData.startTime,
            });
        }
    }
}


export function request<Response = unknown>(url: string, data: Record<string, any>, method = 'POST') {
    cancelRequest(url);
    // 附加 language 语言参数
    const { language, enableSig4 } = getConfig();
    if (language) {
        data.language = language;
    }
    const finalBaseUrl = data.baseUrl || baseUrl;
    const requestHook = requestHookCreator();
    return new Promise(async (resolve, reject) => {
        if (enableSig4) {
            data.isWebSig4 = true;
        }
        url = await getSig4Url(url, data);
        requestHook.before(url, data, method);
        const xhr = new XMLHttpRequest();
        xhr.open(method, finalBaseUrl + url);
        xhr.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');
        const customRequestHeader = getConfig().customRequestHeader;
        if (customRequestHeader) {
            Object.entries<string>(customRequestHeader).forEach(function ([key, value]) {
                xhr.setRequestHeader(key, value);
            });
        }
        const { encryptHeaders, ...bodyData } = data;
        if (encryptHeaders) {
            // 加密文本的请求头
            Object.entries<string>(encryptHeaders).forEach(function ([key, value]) {
                xhr.setRequestHeader(key, value);
            });
        }
        xhr.responseType = 'json';
        xhr.withCredentials = true;
        // 只有异步才能设置timeout
        xhr.onload = () => {
            delete pendingRequest[url];
            let res = xhr.response;
            if (typeof res === 'string') {
                // ie 11 xhr.responseType = 'json'; 这个设置是无效的
                // https://github.com/naugtur/xhr/issues/123
                try {
                    res = JSON.parse(res);
                } catch (e) {
                    requestHook.after('rejected', 'catch', e);
                    reject(e);
                }
            }
            const status = xhr.status;
            if ((status < 200 && status >= 300 && status !== 304) || res?.result !== 1) {
                reject(res as Response);
                requestHook.after('rejected', 'service', {
                    ...res,
                    isXHRError: false,
                    errorReason: 'responseData',
                });
            } else {
                resolve(res);
                requestHook.after('resolved', 'service', res);
            }
        };

        xhr.ontimeout = xhr.onerror = (e) => {
            reject(e);
            requestHook.after('rejected', 'service', {
                ...e,
                isXHRError: true,
                errorReason: 'timeout',
                error_msg: '请求超时',
            });
        };
        pendingRequest[url] = {
            reject,
            xhr,
        };
        const filteredObject = Object.fromEntries(
            Object.entries(bodyData).filter(([key, value]) => key !== 'encryptHeaders' &&value !== undefined && value !== null)
        );
        xhr.send(stringify(filteredObject));
    }) as Promise<Response>;
}


/**
 * 这块主要处理302相关的req
 */
export function pageReq(url: string, params: Record<string, any>) {
    if (typeof window !== 'undefined') {
        const targetUrl = `${url}?${stringify(params)}`;
        localStorage.setItem('General_SSO_PAGE_REQ', targetUrl);
        window.location.href = targetUrl;
    }
}
