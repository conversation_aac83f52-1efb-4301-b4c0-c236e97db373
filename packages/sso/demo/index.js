
import {
    init,
    login,
    chooseUser,
    passwordLogin,
    requestMobileCode,
    requestEmailCode,
    emailLogin,
    emailRegister,
    resetPasswordByEmail,
    getKsAccounts,
    resetPasswordByPhone,
    checkPassStrength,
    getCountryCodeList,
    validatePhone,
    validateEmail,
    bindPhone,
    bindEmail,
    getAccountInfo,
    saveToken,
    saveTokenInIframe,
    isLegalCallbackUrl,
} from '../dist/index.esm.js';

init({
  env: 'staging',
  sid: 'kuaishou.account.test',
  baseUrl: 'https://ksid-staging.corp.kuaishou.com',
  callback: ''
});

window.login = async function (e) {
    const form = e.target;
    try {
        const res = await login({
            phone: form.phone.value,
            countryCode: form.countryCode.value,
            smsCode: form.smsCode.value,
            createId: !!form.createId.value,
            setCookie: !!form.setCookie.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.chooseUser = async function (e) {
    const form = e.target;
    try {
        const res = await chooseUser({
            phone: form.phone.value,
            countryCode: form.countryCode.value,
            userId: form.userId.value,
            multiUserToken: form.multiUserToken.value,
            setCookie: !!form.setCookie.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.passwordLogin = async function (e) {
    const form = e.target;
    try {
        const res = await passwordLogin({
            phone: form.phone.value,
            account: form.account.value,
            password: form.password.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.requestMobileCode = async function (e) {
    const form = e.target;
    try {
        const res = await requestMobileCode({
            phone: form.phone.value,
            type: form.type.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.requestEmailCode = async function (e) {
    const form = e.target;
    try {
        const res = await requestEmailCode({
            email: form.email.value,
            type: form.type.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.emailLogin = async function (e) {
    const form = e.target;
    try {
        const res = await emailLogin({
            email: form.email.value,
            emailCode: form.emailCode.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.emailRegister = async function (e) {
    const form = e.target;
    try {
        const res = await emailRegister({
            email: form.email.value,
            password: form.password.value,
            emailCode: form.emailCode.value,
            setCookie: !!form.setCookie.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.resetPasswordByEmail = async function (e) {
    const form = e.target;
    try {
        const res = await resetPasswordByEmail({
            email: form.email.value,
            emailCode: form.emailCode.value,
            password: form.password.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.getKsAccounts = async function (e) {
    const form = e.target;
    try {
        const res = await getKsAccounts({
            phone: form.phone.value,
            countryCode: form.countryCode.value,
            smsCode: form.smsCode.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.resetPasswordByPhone = async function (e) {
    const form = e.target;
    try {
        const res = await resetPasswordByPhone({
            phone: form.phone.value,
            countryCode: form.countryCode.value,
            smsCode: form.smsCode.value,
            password: form.password.value,
            targetUserId: form.targetUserId.value,
            multiUserToken: form.multiUserToken.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.checkPassStrength = async function (e) {
    const form = e.target;
    try {
        const res = await checkPassStrength({
            password: form.password.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.getCountryCodeList = async function () {
    try {
        const res = await getCountryCodeList({});
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.validatePhone = async function (e) {
    const form = e.target;
    try {
        const res = await validatePhone({
            smsCode: form.smsCode.value,
            type: form.type.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.validateEmail = async function (e) {
    const form = e.target;
    try {
        const res = await validateEmail({
            emailCode: form.emailCode.value,
            type: form.type.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.bindPhone = async function (e) {
    const form = e.target;
    try {
        const res = await bindPhone({
            phone: form.phone.value,
            countryCode: form.countryCode.value,
            newSmsCode: form.newSmsCode.value,
            originalSmsCode: form.originalSmsCode.value,
            originalEmailCode: form.originalEmailCode.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.bindEmail = async function (e) {
    const form = e.target;
    try {
        const res = await bindEmail({
            email: form.email.value,
            newEmailCode: form.newEmailCode.value,
            originalSmsCode: form.originalSmsCode.value,
            originalEmailCode: form.originalEmailCode.value,
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};

window.getAccountInfo = async function (e) {
    const form = e.target;
    try {
        const res = await getAccountInfo({
            accountInfoType: [form.accountInfoType.value],
        });
        console.log(res);
    } catch (res) {
        console.error(res);
    }
};


window.saveToken = async function(e) {
    const form = e.target;
    try {
        const res = await passwordLogin({
            phone: form.phone.value,
            account: form.account.value,
            password: form.password.value,
        });
        console.log(res);
        localStorage.setItem('__DEBUG_LOGIN_RESULT', JSON.stringify(res));
        const {
            stsUrl,
            authToken,
            followUrl,
            sid,
        } = res;
        saveToken({
            stsUrl,
            authToken,
            sid,
            followUrl: followUrl || location.href,
        })
    } catch (error) {
        console.error(error);
    }
}

window.saveTokenInIframe = async function(e) {
    const form = e.target;
    try {
        const res = await passwordLogin({
            phone: form.phone.value,
            account: form.account.value,
            password: form.password.value,
        });
        console.log(res);
        localStorage.setItem('__DEBUG_LOGIN_RESULT', JSON.stringify(res));
        const {
            stsUrl,
            authToken,
            followUrl,
            sid,
        } = res;

        const params = {
            stsUrl: stsUrl,
            authToken,
            sid,
            followUrl: followUrl || location.href,
        }
        console.log('params', params);
        const result = await saveTokenInIframe(params);
        console.log('result', result);

    } catch (error) {
        console.error(error);
    }
}

window.isLegalCallbackUrl = async function(e) {
    const form = e.target;

    try {
        const res = await isLegalCallbackUrl(form.callbackUrl)
        console.log('isLegalCallbackUrl', res);
    } catch (error) {
        console.log('isLegalCallbackUrl error', error);
    }

}
