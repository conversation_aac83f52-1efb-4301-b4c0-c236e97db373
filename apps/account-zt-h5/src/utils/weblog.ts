import { Weblog, TaskOptions } from '@ks/weblogger';
import { isDev } from '@/common/config/base';

const weblog = new Weblog(
    {
        env: isDev() ? 'test' : 'production',
        autoPV: false,
        radar: {
            projectId: '03265832dd', // 雷达平台上的唯一标识
            sampling: 1, // 上报采样率，1 为全部上报
        },
    },
    {
        // kpn
        product_name: 'KUAISHOU',
    },
);

/**
 * 发送 TaskEvent 事件
 * 兼容v2 v3 格式
 * @note 格式判断依赖于 WebLogger.logConfig.proto 变量
 * v2 格式时，使用 option.action 作为 sendImmediately 第一个参数
 * v3 格式时，使用 'CLICK' 作为 sendImmediately 第一个参数
 * @param option
 */
export function sendTaskEvent(option: TaskOptions) {
    const isV3 = weblog.logConfig.proto === 'v3';
    const action = option.action;
    if (!action) {
        throw TypeError('option.action is required');
    }
    weblog.sendImmediately(
        isV3 ? 'CLICK' : action,
        option,
    );
}

export enum LoginChannelType {
    H5_PAGE = 'H5_PAGE',
}

export function updateLoggerLoginChannel(channelType: LoginChannelType) {
    const h5_extra_attr = weblog.commonPackage?.h5_extra_attr;
    weblog.updateCommonPackage({
        h5_extra_attr: {
            ...h5_extra_attr,
            channelType,
        },
    });
}

export default weblog;
