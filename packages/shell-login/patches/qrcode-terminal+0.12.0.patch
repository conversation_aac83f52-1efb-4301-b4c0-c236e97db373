diff --git a/node_modules/qrcode-terminal/lib/main.js b/node_modules/qrcode-terminal/lib/main.js
index 488cc1a..2d21167 100644
--- a/node_modules/qrcode-terminal/lib/main.js
+++ b/node_modules/qrcode-terminal/lib/main.js
@@ -1,7 +1,7 @@
 var QRCode = require('./../vendor/QRCode'),
     QRErrorCorrectLevel = require('./../vendor/QRCode/QRErrorCorrectLevel'),
-    black = "\033[40m  \033[0m",
-    white = "\033[47m  \033[0m",
+    black = "\x1b[40m  \x1b[0m",
+    white = "\x1b[47m  \x1b[0m",
     toCell = function (isBlack) {
         return isBlack ? black : white;
     },
