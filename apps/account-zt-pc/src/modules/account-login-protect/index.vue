<template>
    <HomeScaffold>
        <main
            class="login-main"
        >
            <img class="top-icon" src="https://var----h5----var.kskwai.com/kos/nlav10761/account-zt/account-protect-icon.svg">
            <div class="hint-text">
                <span>您已开启登录保护，请使用快手APP</span>
                <div class="hint-text">
                    扫码登录或短信验证码登录
                </div>
                <div class="bottom-btn" @click="close">
                    确认
                </div>
            </div>
        </main>
    </HomeScaffold>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed, watch } from '@vue/composition-api';
import HomeScaffold from '@/components/home-scaffold.vue';
import { sendRadarFmp } from '@/common/js/log';

export default defineComponent({
    components: {
        HomeScaffold,
    },
    setup() {
        onMounted(() => {
            sendRadarFmp();
        });
        return {
            close() {
                window.close();
            },
        };
    },
});
</script>

<style lang="stylus" scoped>
.login-main {
    width: 380px;
    height: 411px;
    background: #ffffff;
    border-radius: 4px;
    box-shadow: 0px 8px 20px -4px rgba(74,83,93,0.12);

    > .top-icon {
        display: block;
        margin: 88px auto 0 auto;
        width: 96px;
        height: 96px;
        margin-bottom: 20px;
    }

    > .hint-text {
        padding-top: 4px;
        line-height: 22px;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        text-align: center;
        color: #222222;

        > .bottom-btn {
            width: 316px;
            margin: 87px auto 0 auto;
            background: #fe3666;
            border-radius: 2px;
            line-height: 36px;
            font-size: 14px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            text-align: center;
            color: #ffffff;
        }
    }
}
</style>
