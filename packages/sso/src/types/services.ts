export interface BizAccountParam {
    sid?: string;
    followUrl?: string;
    loginUrl?: string;
    pullTokenFromCenter?: boolean;
}

export interface AuthTokenResult {
    authToken: string;
    sid: string;
    callback: string;
    serviceToken: string;
    ssecurity: string;
    userId: number;
    stsUrl: string;
    followUrl: string;
    isNewUser: boolean;
    serviceOwnToken?: string;
    [key: string]: any;
}

export interface BaseRes {
    result: number;
    error_msg?: string;
}

export interface RefreshLoginResult {
    result: number,
    message?: string
}

export interface MessageEventWithRefreshResult extends MessageEvent {
    data: string,
}

export interface QuickLoginResult extends BaseRes {
    snsProFile: Record<string, any>;
    passToken: string;
    authToken: string;
    userId: number;
    [key: string]: any;
}

// export interface LoginResult {
//     sid: string | number;
//     authToken: string | number;
//     callback: string;
// }

// export interface ServiceInfo {
//     sid?: string | number;
//     authToken: string | number;
// }
