// rollup.config.js
import typescript from 'rollup-plugin-typescript2';
import resolve from 'rollup-plugin-node-resolve';
import commonjs from 'rollup-plugin-commonjs';
import json from '@rollup/plugin-json';
import { terser } from 'rollup-plugin-terser';
// import visualizer from 'rollup-plugin-visualizer';
import rollupReplace from '@rollup/plugin-replace';
const pkg = require('./package.json');

export default [
    {
        input: 'src/index.ts',
        output: [{
            file: 'dist/index.esm.js',
            format: 'es',
            name: '@ks/sso',
            sourcemap: true,
        }, {
            file: 'dist/index.js',
            format: 'cjs',
            name: '@ks/sso',
            sourcemap: true,
        }, {
            file: 'dist/index.umd.js',
            format: 'umd',
            name: 'sso',
            sourcemap: true,
        }],
        plugins: [
            json(),
            resolve({browser: true}),
            commonjs(),
            // tsConfigPaths(),
            rollupReplace({
                'process.env.VERSION': JSON.stringify(pkg.version),
            }),
            typescript({
                clean: true,
                tsconfig: `tsconfig.json`,
                tsconfigOverride: {
                    compilerOptions: {
                        target: 'es5'
                    }
                },
                rollupCommonJSResolveHack: false
            }),
            terser(),
            // visualizer(),
        ],
        external: [
            /@ks\/identity-verification/,
        ],
    }
];
