import { ENCRYPT_STRATEGY } from '../config';

export type Env = 'production' | 'development' | 'staging';

export interface InitParam {
    env: Env;
    sid: string;
    appName: string;
    baseUrl: string;
    callback?: string;
    language?: string;
    platform?: 'PC' | 'H5' | 'OUTSIDE_H5';
    // 新版验证码参数
    qrType?: string;
    kpn?: string;
    serviceOwnParams?: any;
    // 被使用方式渠道
    // (统一登录页使用: PC_PAGE, 登录弹窗: PC_MODAL, h5登录页:H5_PAGE)
    channelType?: string;
    collectConfig?: ICollectConfig;
    encryptStrategy?: ENCRYPT_STRATEGY;
    // 是否开启sig4加签
    enableSig4?: boolean;
    // 业务自定义 request header
    customRequestHeader?: Record<string, string>;
}

export { ENCRYPT_STRATEGY };

export interface ICollectConfig {
    // 风控采集参数
    sensor?: boolean; // 启用传感器数据采集, 默认false
    trace?: boolean; // 启用用户行为跟踪，默认false
    trojanScan?: boolean; // 启用木马扫描，默认false
    trojanApi?: boolean; // 上报安全组扫描设备
}

export interface BaseRes {
    result: number;
    error_msg?: string;
}
