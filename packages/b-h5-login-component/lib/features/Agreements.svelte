<script lang="ts">
    import { getContext } from "svelte";
    import Checkbox from "$lib/shared/forms/Checkbox.svelte";


    const configStore = getContext('loginPageConfig');
    const baseAgreementOnClick: Function = $configStore?.bAccountConfig?.baseAgreementOnClick;
    const currentMainState = $configStore.currentMainState;
    const baseUrl = 'https://passport.kuaishou.com/b-account-h5/agreement';
    function onBaseAgreementClick() {
        baseAgreementOnClick && baseAgreementOnClick();
        window.open(baseUrl, '_blank');
    }
    let agreements: Array<{
        title: string;
        url: string;
        onClick?: Function;
    }>;
    if (currentMainState === 1) {
        agreements = $configStore?.cAccountConfig?.agreements ?? [];
    } else {
        const base = [{
            title: '《用户协议》',
            url: baseUrl,
            onClick: onBaseAgreementClick
        }];
        const bAgreements = $configStore?.bAccountConfig?.agreements ?? [];
        agreements = [
            ...base,
            ...bAgreements,
        ] ?? base;
    }

    export let checked: boolean=false;
    function onClick(url: string, callback?: Function) {
        callback && callback();
        window.open(url, '_blank');
    }
</script>

<div class="agreements-wrapper">
    <Checkbox
            class="rounded-full"
            bind:checked={checked}
    >
    </Checkbox>
    <div class="agreements">
        <span>我已阅读并同意</span><span class="agreement"
                                      on:click={() => onClick(agreements[0].url, agreements[0]?.onClick)}
    >{agreements[0].title}</span>{#each agreements.slice(1) as agreement}和<span class="agreement" on:click={() => onClick(agreement.url, agreement?.onClick)}
        >{agreement.title}</span>{/each}
    </div>
</div>

<style>
    .agreements-wrapper {
        margin-top: 16px;
        margin-bottom: 40px;
        display: flex;
    }
    .agreements {
        margin-left: 4px;
        /*display: flex;*/
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        font-size: 13px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 18.2px;
        text-align: left;
        vertical-align: top;
        color: #9C9C9Cff;
        flex-wrap: wrap;
        white-space: normal; /* 确保文本内容在必要时换行 */
        word-break: break-word;
    }
    .agreements span {
        word-break: break-word;
    }
    .agreement {
        color: #385080;
        font-size: 13px;
        font-weight: 400;
        letter-spacing: 0px;
        text-align: left;
        vertical-align: top;
        white-space: normal; /* 确保文本内容在必要时换行 */
        word-break: break-word;
    }
</style>
