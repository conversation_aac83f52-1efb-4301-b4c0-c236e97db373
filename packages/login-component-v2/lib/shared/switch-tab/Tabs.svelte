<script lang="ts">
    import { writable } from 'svelte/store';
    import { setContext } from 'svelte';
    import { twMerge } from "tailwind-merge";

    const ctx = {
        selected: writable()
    };
    setContext('ctx', ctx);

    function init(node) {
        console.log('node is', node);
        const destroy = ctx.selected.subscribe((x) => {
            console.log('x is', x);
            if (x) {
                node.replaceChildren(x);
            }
        });
        return { destroy };
    }
    let divClass: string;
    console.log('$$props.class', $$props.class);
    $: divClass = twMerge(
        'flex gap-[32px] mb-[6px]',
        $$props.class
    );
</script>

<div class={divClass}
>
    <slot/>
</div>
<div use:init/>
