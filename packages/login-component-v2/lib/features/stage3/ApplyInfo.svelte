<script lang="ts">
    import { getContext } from "svelte";

    const configStore = getContext('loginPageConfig');

    let permissionConfig;
    configStore.subscribe(value => {
        permissionConfig = value.cAloneAuthAccountConfig.permissionConfig;
    });
</script>


<div class="flex flex-col mx-[40px] text-left pt-[40px]">
    <div class="text-[#333840] leading-[24px] font-[400] mr-[4px] my-[4px] flex items-center">
        <div class="w-[32px] h-[32px] bg-red-50"></div><span class="font-[600] mr-[4px]">{permissionConfig.name}</span><span class="text-[#8A9199]">申请使用以下权限</span>
    </div>
    <div class="mt-[37px] text-[#333840] font-[600] leading-[22px] flex flex-col text-left gap-[17px]">
        {#each permissionConfig.permissionInfo as info}
            <div class="line">
                <div class="marker">{info.title}</div>
                {#if info.explain}
                    <div class="detail">{info.explain}</div>
                {/if}
            </div>
        {/each}
    </div>
</div>

<style>
    div.marker::before {
        content: '•';
        color: #8A9199;
        display: inline-block;
        width: 12px;
        font-size: 12px;
    }
    .line .detail {
        margin-top: 8px;
        margin-left: 12px;
        color: #8A9199;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        text-align: left;
        vertical-align: top;
    }
</style>
