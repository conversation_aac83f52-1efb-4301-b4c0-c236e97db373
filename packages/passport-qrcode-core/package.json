{"name": "@ks-passport/qrcode-core", "private": false, "version": "0.0.3", "license": "MIT", "module": "./dist/index.esm.js", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "index.d.ts"], "repository": {"type": "git", "url": "https://git.corp.kuaishou.com/mfe/tp/unified/account-zt-general"}, "publishConfig": {"access": "public", "registry": "https://npm.corp.kuaishou.com"}, "devDependencies": {"@rollup/plugin-json": "^4.1.0", "@types/node": "^18.14.2", "i": "^0.3.7", "npm": "^10.2.0", "rimraf": "^2.6.2", "rollup": "^2.38.4", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-livereload": "^2.0.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "typescript": "^4.1.3"}, "scripts": {"dev": "rollup -c -w", "build": "rimraf ./dist && rollup -c", "lint": "echo 'eslint skiped'", "publish:latest": "npm run build && npm publish"}, "dependencies": {"axios": "^1.6.0", "@ks/weblogger": "^3.0.17", "@ks-radar/radar-core": "^1.2.4", "@ks-radar/radar-event-collect": "^1.2.4", "es-event-emitter": "^1.3.9", "eventemitter3": "^4.0.7", "@ks-cqc/h5-sig4-lite-obf": "^2.0.7", "tslib": "2"}}