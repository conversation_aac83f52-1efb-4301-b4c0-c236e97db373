import request from './request';
import { baseUrl } from '@/common/config/base';

/**
 * 一证通查
 * 账号前端文档 https://docs.corp.kuaishou.com/d/home/<USER>
 * 账号后端文档 https://docs.corp.kuaishou.com/d/home/<USER>
 */

interface AccountBindInfoParams {
    /** 包含入网时间的图片 */
    accessTimeFile: File;
    /** 服务 id，spaceship.api */
    sid: string;
    /** 国家码，+86 */
    countryCode: string;
    /** 手机号 不包含国家码部分 */
    phone: string;
    /** 短信验证码 */
    smsCode: string;

    // 下面这 3 个是风控要的参数。
    kpn: string;
    // ztIdentityVerificationCheckToken?: string;
    // ztIdentityVerificationType?: string;
}

export interface AccountBindInfoResult {
    result: number; // 为 1 表示成功，非 1 表示失败。
    error_msg?: string; // 失败时的错误信息。
    phone: string; // "***********"
    verifyToken: string;
    accessTime: string; // "2022-01-01"
    accessPhone: string; // "180****5678"
    bindAccount: {
        userId: number; // 34
        nickName: string; // "交个朋友"
        bindTime?: string; // "2018-08-08 08:08:08"
        /** 后端接口不会返回下面这个字段，仅前端用作标识账号是否已经被解绑。 **/
        __fe_unbound?: boolean;
    }[];
}

/**
 * 获取账号绑定信息
 */
export function getAccountBindInfo(param: AccountBindInfoParams): Promise<AccountBindInfoResult> {
    const formData = new FormData();
    for (const [key, value] of Object.entries(param)) {
        formData.append(key, value);
    }

    return request.post(`${baseUrl}/pass/kuaishou/account/getAccountBindInfo`, formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
}

/**
 * **************** 解绑逻辑 ***********************
 * */

interface UnbindForPhoneParams {
    /** 服务id，spaceship.api */
    sid: string;
    countryCode: string; // 固定为 '+86'
    /** 手机号 不包含国家码部分 */
    phone: string;
    /** 用户id **/
    userId: number;

    verifyToken: string;

    /** ocr解析出的入网时间，获取账号绑定信息传回的数据，"2022-01-01" */
    // accessTime: string;
    /** ocr解析出的入网手机号；获取账号绑定信息传回的数据，"180****5678" */
    // accessPhone: string;
}

interface UnbindForPhoneResult {
    result: number; // 为 1 表示成功，非 1 表示失败。
    error_msg?: string; // 失败时的错误信息。
}

/**
 * 解绑手机号
 */
export function unbindForPhone(param: UnbindForPhoneParams): Promise<UnbindForPhoneResult> {
    return request.post(`${baseUrl}/pass/kuaishou/account/unbindForPhone`, param);
}

/**
 * **************** 换绑逻辑 ***********************
 * */

interface RebindForPhoneParams {
    /** 服务id，spaceship.api */
    sid: string;
    countryCode: string; // 固定为 '+86'
    /** 手机号 不包含国家码部分 */
    phone: string;
    /** 用户id **/
    smsCode: number;
}

interface RebindForPhoneResult {
    result: number; // 为 1 表示成功，非 1 表示失败。
    error_msg?: string; // 失败时的错误信息。
}

/**
 * 换绑手机号
 */
export function rebindForPhone(param: RebindForPhoneParams): Promise<RebindForPhoneResult> {
    return request.post(`${baseUrl}/pass/kuaishou/account/rebindPhone`, param);
}