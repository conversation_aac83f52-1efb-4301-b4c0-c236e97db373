{"name": "account-zt-pc", "version": "0.1.4", "private": true, "scripts": {"dev": "gitCommitLog=$(git log -1 --pretty=format:'%h on %ct') vue-cli-service serve", "build": "gitCommitLog=$(git log -1 --pretty=format:'%h on %ct') vue-cli-service build", "build:dev": "gitCommitLog=$(git log -1 --pretty=format:'%h on %ct') vue-cli-service build --mode=development", "lint": "vue-cli-service lint", "build:login": "vue-cli-service build --target lib --name account-login 'src/modules/account-login/main.ts'", "build:login-component-page": "vue-cli-service build --target lib --name login-component-page 'src/modules/login-component-page/main.ts'", "build:third-part-login-page": "vue-cli-service build --target lib --name third-part-login-page 'src/modules/third-part-login/main.ts'"}, "dependencies": {"@ks-passport/qrcode-core": "workspace:*", "@ks/identity-verification": "^0.2.16", "@ks/login-component": "workspace:*", "@ks/passport-iframe-client": "0.1.7", "@ks/sso": "workspace:*", "@ks/general-sso": "workspace:*", "qs": "^6.11.2", "@ks/weblogger": "^3.9.46", "@nestjs/config": "^0.6.3", "@vue/composition-api": "1.0.0-rc.1", "axios": "^0.21.1", "core-js": "^3.6.5", "swrv": "^0.9.6", "vue": "2.6.11", "vue-router": "^3.2.0"}, "devDependencies": {"@ks/eslint-config-game": "^1.1.9-alpha.3", "@types/node": "^18.7.18", "@typescript-eslint/eslint-plugin": "^4.14.2", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.11", "@vue/cli-plugin-eslint": "~4.5.11", "@vue/cli-plugin-router": "~4.5.11", "@vue/cli-plugin-typescript": "~4.5.11", "@vue/cli-service": "~4.5.11", "@vue/eslint-config-typescript": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-svelte3": "^2.7.3", "eslint-plugin-vue": "^6.2.2", "lint-staged": "^9.5.0", "stylus": "^0.54.7", "stylus-loader": "^3.0.2", "typescript": "~3.9.3", "vue-template-compiler": "2.6.11", "webpack-bundle-analyzer": "^4.6.1", "webpack-runtime-public-path-plugin": "^1.1.2"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["vue-cli-service lint", "git add"]}}