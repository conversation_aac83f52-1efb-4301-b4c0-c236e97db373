import { SSOLogger } from '../dist/index.esm.js';

function getLogger() {
    const loggerConfig = {
        callerSDK: {
            name: '@ks/sso-logger-test',
            version: '2.6.7-beta.2',
        },
        sid: 'kuaishou.web.api',
        channelType: 'test-channel',
    };
    const env = 'development';
    const logger = new SSOLogger(loggerConfig, { env });
    return logger;
}
const logger = getLogger();

document.querySelector('#sendLogger').onclick = () => {
    logger.report({
        startTime: 1,
        endTime: 1001,
        result: 'SUCC',
        request: {
            email: '<EMAIL>',
        },
        response: {
            result: 1,
            userId: '123',
        },
        apiUrl: 'https://id.kuaishou.com/pass/kuaishou/login/test',
        reportType: 'service',
    });
}

console.log('logger is', logger);
