{"compilerOptions": {"declaration": true, "target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "experimentalDecorators": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"], "outDir": "dist"}, "include": ["src/**/*.ts", "typings/**/*.d.ts"], "exclude": ["node_modules", "dist"]}