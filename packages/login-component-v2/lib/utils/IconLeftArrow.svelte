<script lang="ts">
    import { twMerge } from 'tailwind-merge';
    import ToolbarButton from '$lib/shared/toolbar/ToolbarButton.svelte';
    export let name: string = 'LeftArrow';
    export let width = 16;
    export let height = 16;
</script>

<ToolbarButton on:click {name} {...$$restProps} class={twMerge($$props.class)} let:svgSize
>
    <svg width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M10.3136 13.6868L4.62671 7.99988L10.3136 2.31299L11.0207 3.0201L6.04092 7.99988L11.0207 12.9797L10.3136 13.6868Z" fill="#C6C6C6"/>
    </svg>
</ToolbarButton>

<style>
</style>
