<script>
    import {onMount, onDestroy, createEventDispatcher, getContext} from 'svelte';
    import { cancelQrLogin, getQRLoginInfo, getUserLoginInfo, scanQRLoginResult } from '@ks/sso';
    import { logger, sendTaskEvent } from '../../common/logger';

    const themeColor = getContext('themeColor');
    export let width = 146;
    export let height = 146;
    const dispatch = createEventDispatcher();
    let status = 'scan';
    let qrLoginInfo = {};
    let errorMsg = '';

    function setErrorMsg(msg) {
        errorMsg = msg;
        dispatch('code-need-refresh', errorMsg);
    }
    async function start() {
        // 重新开始的时候 清除一下错误
        setErrorMsg('');
        cancelQrLogin();
        let baseTime = Date.now();
        try {
            qrLoginInfo = await getQRLoginInfo();
            status = 'scan';
            dispatch('code-need-refresh', status);
            // 这块的res可能为user(老扫码) 或者 {qrLoginSignature: 'xxx'}(新扫码)
            const res = await getUserLoginInfo({
                qrLoginSignature: qrLoginInfo.qrLoginSignature,
                qrLoginToken: qrLoginInfo.qrLoginToken,
            });
            sendTaskEvent({
                action: 'USER_LOGIN',
                params: {
                    tab_name: 'QRCODE',
                },
                status: 'START',
            })
            status = 'confirm';
            const result = await scanQRLoginResult({
                // 这块扫码有两套 新的扫码优先使用 scanResult 返回的签名
                // 老的扫码用 start 返回的签名
                qrLoginSignature: res.qrLoginSignature || qrLoginInfo.qrLoginSignature,
                qrLoginToken: qrLoginInfo.qrLoginToken,
            });
            sendTaskEvent({
                action: 'USER_LOGIN',
                params: {
                    result_code: 1,
                    tab_name: 'QRCODE',
                    cost_time: Date.now() - baseTime,
                },
                status: 'SUCCESS',
            })
            dispatch('login-success', {
                loginType: 'qrcode',
                result,
            });
        } catch (e) {
            dispatch('login-fail', e);
            sendTaskEvent({
                action: 'USER_LOGIN',
                params: {
                    result_code: e.result,
                    tab_name: 'QRCODE',
                    cost_time: Date.now() - baseTime,
                },
                status: 'FAIL',
            })
            // IP防刷，错误需要扔出来
            if (e === 'From Cancel Request' || e.message === 'from cancel') { // sso中cancel时的reject内容变更，需要兼容
                return;
            }
            status = 'timeout';
            dispatch('code-need-refresh', status);
            //  二维码超时不提示
            if (e.result !== 707) {
                dispatch('error', e.error_msg);
                setErrorMsg(e.error_msg);
            }
        }
    }
    onMount(() => {
        start();
    });
    onDestroy(() => {
        cancelQrLogin();
    });
</script>

<div class="qrcode {themeColor}">
    <div class="corner tl {themeColor}"></div>
    <div class="corner tr {themeColor}"></div>
    <div class="corner bl {themeColor}"></div>
    <div class="corner br {themeColor}"></div>
    {#if qrLoginInfo.imageData}
        <img
            width="{width + 18}px"
            height="{height + 18}px"
            src="data:image/png;base64,{qrLoginInfo.imageData}"
            alt="qrcode" />
    {/if}
    <span class="qrcode-icon" />
    <div class="qrcode-status qrcode-status-{status}">
        {#if errorMsg}
            <strong>{errorMsg}</strong>
            <p class="qrcode-refresh" on:click={start}>点击刷新</p>
        {:else if status === 'confirm'}
            <strong>扫码成功</strong>
            <p class="qrcode-desc">请在手机上确认登录</p>
        {:else if status === 'timeout'}
            <strong>二维码已失效</strong>
            <p class="qrcode-refresh" on:click={start}>点击刷新</p>
        {/if}
    </div>
</div>

<style type="text/stylus" scoped>
    @import '../../common/variables.styl';

.qrcode
    display inline-block
    position relative
    vertical-align bottom
    border solid 1px rgba(254,54,102,0.1)
    background white;
    min-width: 160px;
    min-height: 160px;
    &.blue
        border-color rgba(50,107,251, 0.1)
    .corner
        content ""
        position absolute
        width 10px
        height @width
        background brand-primary
        z-index 10
        &.blue
            background brand-primary-blue
        &.tl
            top -1px
            left @top
        &.tr
            top -1px
            right @top
        &.bl
            bottom -1px
            left @bottom
        &.br
            bottom -1px
            right @bottom
    img
        display block
        position relative
        z-index 100
    &-icon
        position absolute
        top 0
        right 0
        bottom 0
        left 0
        margin auto
        width 32px
        height 32px
        background url(https://static.yximgs.com/udata/pkg/cloudcdn/i/logo-simple.f8bc2fc.svg) no-repeat
        background-size 32px
        z-index 150
    &-status
        position absolute
        top 0
        right 0
        bottom 0
        left 0
        padding-top 94px
        background rgba(#fff, .95) url(https://static.yximgs.com/s1/i/qr-confirm.f32a3c5.svg) no-repeat 50% 44px
        display none
        z-index 200
        &-confirm
            display block
        &-timeout
            background-image url(https://static.yximgs.com/s1/i/qr-timeout.36ec907.svg)
            display block
        strong
            color font-color-secondary
            font-size 13px
    &-refresh
        cursor pointer
        margin-top: 10px
        link-color(font-color-primary, brand-primary)
        font-size 12px
    &-desc
        margin-top: 16px;
</style>
