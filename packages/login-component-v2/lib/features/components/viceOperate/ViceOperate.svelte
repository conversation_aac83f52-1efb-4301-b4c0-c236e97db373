<script lang="ts">
    import { createEventDispatcher, getContext } from "svelte";
    import { EnumPageState } from "$lib/shared/const";
    import OperateSection from "$lib/features/components/viceOperate/OperateSection.svelte";

    const dispatch = createEventDispatcher();

    export let currentType;
    export let viceOperateConfig;
    // let

    let leftSection = viceOperateConfig?.leftSection || [];
    let rightSection = viceOperateConfig?.rightSection || [];
    if (currentType === 'smsForm') {
        leftSection = leftSection.filter(e => e?.type !== 'reset');
        rightSection = rightSection.filter(e => e?.type !== 'reset');
    }

    const goToRegister = () => {
        dispatch('updatePageState', EnumPageState.register);
    }
    const goToResetPassword = () => {
        dispatch('updatePageState', EnumPageState.resetPassword);
    }
</script>

{#if !(leftSection?.length === 0 && rightSection?.length === 0)}
    <div class="flex justify-between form-footer-line mt-[8px]">
        <div class="flex items-center gap-[8px] left-section">
            <OperateSection
                {goToRegister}
                {goToResetPassword}
                items={leftSection}
            />
        </div>
        <div class="flex items-center gap-[8px] right-section">
            <OperateSection
                    {goToRegister}
                    {goToResetPassword}
                    items={rightSection}
            />
        </div>
    </div>
{/if}

<style>
    .form-footer-line {
        color: #9C9C9C;
        font-size: 14px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        text-align: center;
        vertical-align: top;
    }
</style>
