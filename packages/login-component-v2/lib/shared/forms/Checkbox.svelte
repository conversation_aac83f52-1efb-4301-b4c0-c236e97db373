<script lang="ts">
    import { getContext } from 'svelte';
    import type { FormColorType } from '../types';
    import { labelClass, inputClass } from './Radio.svelte';
    import Label from './Label.svelte';
    import type { CheckboxItem } from '../types';

    // properties forwarding
    export let name: string | undefined = undefined;
    export let color: FormColorType = 'primary';
    export let custom: boolean = false;
    export let inline: boolean = false;
    export let group: string[] = [];
    export let choices: CheckboxItem[] = [];
    export let value: string | number = 'on';
    export let checked: boolean | undefined = undefined;
    export let spacing: string = $$slots.default ? 'me-2' : '';
    export let groupLabelClass: string = '';
    export let groupInputClass: string = '';


    // tinted if put in component having its own background
    let background: boolean = getContext('background');
    const themeColor = getContext('themeColor');
    const colorC = themeColor.colorC;
    console.log('colorC', colorC);
</script>
{#if choices.length > 0}
    {#each choices as {value, label}, i}
        <Label class={labelClass(inline, groupLabelClass)} show={$$slots.default} for={`checkbox-${i}`}>{ label }
            <input {name} id={`checkbox-${i}`} type="checkbox" value={ value } bind:group {...$$restProps}  class={inputClass(custom, color, true, background, spacing, groupInputClass)} />
            <slot />
        </Label>
    {/each}
{:else}
    <Label class={labelClass(inline, $$props.class)}
           show={$$slots.default}
           style="--colorC: {colorC}"
    >
        <input {name} type="checkbox" bind:checked on:keyup on:keydown on:keypress on:focus on:blur on:click on:mouseover on:mouseenter on:mouseleave on:paste on:change {value} {...$$restProps} class={inputClass(custom, color, true, background, spacing, $$slots.default || $$props.class)} />
        <slot />
    </Label>
{/if}

<style>
    [type='checkbox'] {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        padding: 0;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
        display: inline-block;
        vertical-align: middle;
        background-origin: border-box;
        -webkit-user-select: none;
        -moz-user-select: none;
        user-select: none;
        flex-shrink: 0;
        height: 1rem;
        width: 1rem;
        color: #1C64F2;
        background-color: #fff;
        border-color: #6B7280;
        border-width: 1px;
        --tw-shadow: 0 0 #0000;
    }
    [type='checkbox'].currentC {
        color: var(--colorC);
    }

    [type='checkbox']:checked,[type='radio']:checked,.dark [type='checkbox']:checked,.dark [type='radio']:checked {
        border-color: transparent;
        background-color: currentColor;
        background-size: 0.55em 0.55em;
        background-position: center;
        background-repeat: no-repeat;
    }

    [type='checkbox']:checked {
        background-image: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 12'%3e %3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M1 5.917 5.724 10.5 15 1.5'/%3e %3c/svg%3e");
        background-repeat: no-repeat;
        background-size: 0.55em 0.55em;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
</style>
