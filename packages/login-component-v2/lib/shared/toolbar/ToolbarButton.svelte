<script lang="ts">
    import type { ToolbarButtonType } from '../types';
    import { getContext } from 'svelte';
    import { twMerge } from 'tailwind-merge';

    const background = getContext('background');

    export let color: ToolbarButtonType = 'default';
    export let name: string | undefined = undefined;
    export let ariaLabel: string | undefined = undefined;
    export let size: 'xs' | 'sm' | 'md' | 'lg' = 'md';
    export let href: string | undefined = undefined;

    let buttonClass: string;
    $: buttonClass = twMerge('focus:outline-none border-none', $$props.class);

    const svgSizes = {
        xs: 'w-3 h-3',
        sm: 'w-3.5 h-3.5',
        md: 'w-5 h-5',
        lg: 'w-5 h-5'
    };
</script>

{#if href}
    <a {href} {...$$restProps} class={buttonClass} aria-label={ariaLabel ?? name}>
        {#if name}<span class="sr-only">{name}</span>{/if}
        <slot svgSize={svgSizes[size]} />
    </a>
{:else}
    <button on:click type="button" {...$$restProps} class={buttonClass} aria-label={ariaLabel ?? name}>
        {#if name}<span class="sr-only">{name}</span>{/if}
        <slot svgSize={svgSizes[size]} />
    </button>
{/if}
