/**
 * @file 日志实例的代理
 */
import AsyncInstanceProxy from '@/proxy/index';
import Logger from '@/logger/instance';
import { LoggerConfig } from '@/logger/types';

interface BaseModule {
    getInstance: (config: LoggerConfig) => Logger;
}

export default class AsyncLoggerProxy extends AsyncInstanceProxy<Logger, BaseModule> {
    private readonly loggerConfig!: LoggerConfig;
    constructor(baseModule: BaseModule, remoteSrc: string|Promise<string>, loggerConfig: LoggerConfig) {
        super(baseModule, remoteSrc);
        this.loggerConfig = loggerConfig;
    }
    protected createInstance(module: BaseModule) { // 接口来定义Module
        return module.getInstance(this.loggerConfig);
    }
    report(...args: unknown[]) {
        return this.invoke('report', ...args);
    }
}
