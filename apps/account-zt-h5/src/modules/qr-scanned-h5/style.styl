.loading-ctn {
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center
}
.qr-scanned-page
    // 7.5rem => 750设计稿
    // width 7.5rem
    background-color transparent
    text-align center
    font-size .28rem

    &-icon
        display block
        width 1.68rem
        height 1.68rem
        margin .32rem auto
        background center / contain no-repeat;
    &-title
        font-size .4rem
        line-height .56rem
        color #222222
        margin .32rem auto
    &-desc
        font-size .28rem
        line-height .32rem
        color #9c9c9c
.header
    margin 20vh auto 20vh

.button
    size = .96rem
    width 4.5rem
    border-radius (size/2)
    border 0 none
    text-align center
    margin 0.3rem auto
    font-size .3rem
    line-height size
    display block
    &:focus
        outline: 0 none
    &--primary
        background #fe3666
        color white
    &--ghost
        border solid .01rem #eaeaea
        color #666666
        background #ffffff
