<script lang="ts">
    import SMSForm from "$lib/features/stage1/kuaishou/SMSForm.svelte";
    import PasswordForm from "$lib/features/stage1/kuaishou/PasswordForm.svelte";
    import QrCodeC from "$lib/features/stage1/kuaishou/QrCode.svelte";
    import { TabItem, Tabs } from '$lib/shared/switch-tab';
    import IconQrCode from "$lib/utils/IconQrCode.svelte";
    import IconPc from "$lib/utils/IconPc.svelte";
    import ChooseUser from "$lib/features/stage1/kuaishou/ChooseUser.svelte";
    import BindPhone from "$lib/features/stage1/kuaishou/bind/QuickBindPhone.svelte";
    import ResetPassword from "$lib/features/stage1/kuaishou/ResetPassword/ResetPassword.svelte";
    import { EnumPageState } from "$lib/shared/const";
    import IconQuestion from "$lib/utils/IconQuestion.svelte";
    import Tooltip from "$lib/shared/tooltip/Tooltip.svelte";
    import { createEventDispatcher, getContext, onMount } from "svelte";
    import { tabKuaiShouStore } from "$lib/store/tabStore";

    const dispatch = createEventDispatcher();


    const loginPageConfig = getContext('loginPageConfig');
    const getCurrentHeight = getContext('getCurrentHeight');


    const currentStage = $loginPageConfig.currentMainState;

    let pageState = EnumPageState.form;
    $: {
        if (pageState === EnumPageState.register) {
            dispatch('updateAgreementType', 'register');
        } else {
            dispatch('updateAgreementType', 'login');
        }
        if ([EnumPageState.qrcode, EnumPageState.chooseUser].includes(pageState)) {
            dispatch('updateShowFooter', false);
        } else {
            dispatch('updateShowFooter', true);
        }
    }

    let loginMainMethod;
    let mainTitle = '';
    let mainTitleTooltip = '';
    let qrCodeToolTip = '';
    let pcToolTip = '';
    loginPageConfig.subscribe(value => {
        loginMainMethod = value.cAccountConfig.loginMainMethod;
        mainTitle = value.cAccountConfig.mainTitle;
        mainTitleTooltip = value.cAccountConfig.mainTitleTooltip;
        qrCodeToolTip = value.cAccountConfig.qrCodeToolTip;
        pcToolTip = value.cAccountConfig.pcToolTip;
    });

    let qrCodeHeight = 0;
    onMount(() => {
        qrCodeHeight = getCurrentHeight();
    })
    function switchCurrentState() {
        qrCodeHeight = getCurrentHeight();
        pageState = pageState === EnumPageState.qrcode ? EnumPageState.form : EnumPageState.qrcode;
        dispatch('toggleShowFooter');
    }

    function updatePageState(s) {
        pageState = s.detail;
        if (pageState === EnumPageState.resetPassword) {
            dispatch('updateShowFooter', false);
        } else {
            dispatch('updateShowFooter', true);
        }
    }

    export let tabItemActiveClasses = `currentC font-[600]`;
    export let tabItemInactiveClasses = "text-[#646B73]";
    let tabsClass = $loginPageConfig.cAccountConfig?.tabsClass || '';
    if (loginMainMethod?.length === 3 && loginMainMethod?.every(e => e.title)) {
        tabsClass = 'justify-between';
    }
    function calculateHeight() {
        // 最外层 margin-top: 8
        // tab-tip: 20
        // 第一个input：48+24
        // 第二个input：48+44
        // Button：48
        // 副操作：28
        // 返回身份选择：44
        const baseHeight = 8 + 20 + 48+24 + 48+44 + 48;
        let height = baseHeight;
        if ($loginPageConfig.cAccountConfig?.viceOperateConfig) {
            // 副操作
            height += 28;
        }
        if ($loginPageConfig.cAccountConfig?.otherOperateConfig) {
            // 返回身份选择
            height += 44;
        }
        // 二阶段切换登录方式 20+68
        if (currentStage === 2) {
            height += (20+68);
        }

        console.log('height is' ,height);
        return height;
    }

</script>

<div>
    {#if loginMainMethod[loginMainMethod.length-1].type === 'scan' && !loginMainMethod[loginMainMethod.length-1]?.title && [EnumPageState.form, EnumPageState.qrcode].includes(pageState)}
        <div class="absolute right-0 top-0">
            {#if pageState === EnumPageState.qrcode}
                <IconPc
                        class="currentC"
                        on:click={switchCurrentState}
                        toolTip={pcToolTip}
                />
            {:else}
                <IconQrCode
                        class="currentC"
                        on:click={switchCurrentState}
                        toolTip={qrCodeToolTip}
                />
            {/if}
        </div>
    {/if}
    {#if pageState === EnumPageState.chooseUser}
        <ChooseUser
                height={qrCodeHeight}
                on:updatePageState={updatePageState}
        />
    {:else if pageState === EnumPageState.qrcode}
        <QrCodeC
                height={qrCodeHeight}
                on:updatePageState={updatePageState}
        />
    {:else if pageState === EnumPageState.resetPassword}
        <ResetPassword
                on:updatePageState={updatePageState}
        />
    {:else if pageState === EnumPageState.bindPhone}
        <BindPhone
                on:updatePageState={updatePageState}
        />
    {:else}
        <div class={`px-[48px] pt-[48px] currentStage-${currentStage}`}>
            {#if mainTitle}
                <div class="main-title">
                    {mainTitle}
                    {#if mainTitleTooltip}
                        <IconQuestion
                                width={24}
                                height={24}
                        />
                        <Tooltip class="mb-[4px] opacity-[0.85] bg-[#000000]">{mainTitleTooltip}</Tooltip>
                    {/if}
                </div>
            {/if}
            {#if loginMainMethod.filter(e => e.title)?.length === 1}
                {#if loginMainMethod[0].tabTip}
                    <div class="tab-tip">{loginMainMethod[0].tabTip}</div>
                {/if}
                {#if loginMainMethod[0].type === 'password'}
                    <PasswordForm
                            on:updatePageState={updatePageState}
                            on:toggleShowFooter={() => dispatch('toggleShowFooter')}
                    ></PasswordForm>
                {:else if loginMainMethod[0].type === 'code'}
                    <SMSForm
                            on:updatePageState={updatePageState}
                            on:toggleShowFooter={() => dispatch('toggleShowFooter')}
                    ></SMSForm>
                {:else if loginMainMethod[0].type === 'scan'}
                    <QrCodeC
                            height={calculateHeight()}
                            displayType="tab"
                    />
                {/if}
            {:else}
                <Tabs class={tabsClass}>
                    {#each loginMainMethod.filter(e => e.title) as method, index}
                        <TabItem title={method.title}
                                 activeClasses={tabItemActiveClasses}
                                 inactiveClasses={tabItemInactiveClasses}
                                 open={$tabKuaiShouStore[index]}
                                 tabTip={method.tabTip}
                                 showTabTip={method.type !== 'scan'}
                        >
                            {#if method.type === 'password'}
                                <PasswordForm
                                        on:updatePageState={updatePageState}
                                        on:toggleShowFooter={() => dispatch('toggleShowFooter')}
                                ></PasswordForm>
                            {:else if method.type === 'code'}
                                <SMSForm
                                        on:updatePageState={updatePageState}
                                        on:toggleShowFooter={() => dispatch('toggleShowFooter')}
                                ></SMSForm>
                            {:else if method.type === 'scan'}
                                <QrCodeC
                                        height={calculateHeight()}
                                        displayType="tab"
                                />
                            {/if}
                        </TabItem>
                    {/each}
                </Tabs>
            {/if}
        </div>
    {/if}
</div>

<style>
    .main-title {
        color: #222222;
        font-size: 24px;
        font-weight: 600;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 32px;
        text-align: left;
        vertical-align: top;
        margin-bottom: 24px;
        display: flex;
        gap: 4px;
        align-items: center;
    }
    .currentStage-1 {
        padding-bottom: 48px;
    }
    .currentStage-2 {
        padding-bottom: 32px;
    }
    .currentStage-3 {
        padding-bottom: 32px;
    }
    .tab-tip {
        color: #9C9C9C;
        font-size: 12px;
        font-weight: 400;
        font-family: PingFang SC;
        letter-spacing: 0px;
        line-height: 20px;
        height: 20px;
        text-align: left;
        vertical-align: top;
    }
</style>
