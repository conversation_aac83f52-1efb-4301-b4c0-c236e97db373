export type envType = 'production' | 'staging' | 'development' | 'local';
const availableEnvList = [ 'production', 'staging','development','local']

let NODE_ENV: envType = process.env.NODE_ENV as envType;

if (availableEnvList.indexOf(process.env.NODE_ENV || '') === -1) {
    console.warn( `NODE_ENV ${NODE_ENV} is not support, ${availableEnvList.join(' | ')}` );
    console.warn('use production as default');
    NODE_ENV = 'production';
}

export const IS_PROD = NODE_ENV === 'production';
export const IS_DEV = NODE_ENV === 'development' || NODE_ENV === 'local';

export interface Config {
    API: {
        ACCOUNT_HOST: string;
        ID_HOST: string;
        ACCOUNT_QRCODE_HOST: string;
    }
}

const configMap: Record<envType, Config> = {
    local: {
        API: {
            ACCOUNT_HOST: '',
            ACCOUNT_QRCODE_HOST: '',
            ID_HOST: '',
        }
    },
    production: {
        API: {
            ACCOUNT_HOST: 'https://app.m.kuaishou.com',
            ACCOUNT_QRCODE_HOST: 'https://app.m.kuaishou.com',
            ID_HOST: '//id.kuaishou.com',
        }
    },
    development: {
        API: {
            // ACCOUNT_HOST: 'http://infra-id-card.test.gifshow.com',
            ACCOUNT_HOST: 'https://realname-zt-fe.staging.kuaishou.com',
            // ACCOUNT_QRCODE_HOST: 'https://node-account-dev1.test.gifshow.com',
            ACCOUNT_QRCODE_HOST: 'https://realname-zt-fe.staging.kuaishou.com',
            // ID_HOST: '//ksid2.test.gifshow.com'
            ID_HOST: 'https://ksid-staging.corp.kuaishou.com'
        }
    },
    staging: {
        API: {
            // ACCOUNT_HOST: 'https://zt-idcardweb.staging.kuaishou.com',
            ACCOUNT_HOST: 'https://realname-zt-fe.staging.kuaishou.com',
            // ACCOUNT_QRCODE_HOST: 'https://zt-idcardweb.staging.kuaishou.com',
            ACCOUNT_QRCODE_HOST: 'https://realname-zt-fe.staging.kuaishou.com',
            ID_HOST: 'https://ksid-staging.corp.kuaishou.com'
        }
    }
}

export function getConfig(env: envType = NODE_ENV) {
    return configMap[env];
}
