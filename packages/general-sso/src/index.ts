// 增加一下Promise判断
if (!Promise || typeof Promise.prototype.then !== 'function') {
    throw new Error('@ks/general-sso requires Promise polyfill');
}

export * from './types/index';
export * from './core/configInit';

// export * from './services/index';
export * from './services/saveToken';
export * from './services/QRLogin';
export * from './services/smsLogin';
export * from './services/passwordLogin';
export * from './services/passwordReset';
export * from './services/multipleUser';
export * from './services/loginStatus';
export * from './services/logout';
export * from './services/register';
export * from './services/emailLogin';
export * from './services/validate';
export * from './services/rebind';
export * from './services/bind';
